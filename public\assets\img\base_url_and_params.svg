<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">

  <rect width="100%" height="100%" fill="#ffffff" stroke="#e9ecef" stroke-width="2"/>
  <rect x="20" y="20" width="560" height="60" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="40" y="45" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#495057">API Configuration Tutorial</text>
  <text x="40" y="65" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">Base URL and Parameters Setup</text>
  
  <rect x="40" y="100" width="520" height="40" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="60" y="125" font-family="monospace" font-size="12" fill="#1976d2">https://api.example.com/v1/endpoint</text>
  
  <text x="40" y="160" font-family="Arial, sans-serif" font-size="14" fill="#333">Parameters:</text>
  <text x="60" y="180" font-family="monospace" font-size="11" fill="#666">• api_key: your_api_key_here</text>
  <text x="60" y="200" font-family="monospace" font-size="11" fill="#666">• format: json</text>
  <text x="60" y="220" font-family="monospace" font-size="11" fill="#666">• version: 1.0</text>
  
  <rect x="40" y="250" width="520" height="100" fill="#f1f8e9" stroke="#4caf50" stroke-width="1"/>
  <text x="60" y="275" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2e7d32">Example Request:</text>
  <text x="60" y="295" font-family="monospace" font-size="10" fill="#388e3c">GET /api/v1/data?api_key=abc123&amp;format=json</text>
  <text x="60" y="315" font-family="monospace" font-size="10" fill="#388e3c">Host: api.example.com</text>
  <text x="60" y="335" font-family="monospace" font-size="10" fill="#388e3c">Content-Type: application/json</text>
</svg>