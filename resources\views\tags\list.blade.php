@extends('layout')

@section('title')
    <?= get_label('tags', 'Tags') ?>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between mt-4">
            <div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-style1">
                        <li class="breadcrumb-item">
                            <a href="{{ route('home.index') }}"><?= get_label('home', 'Home') ?></a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('projects.index') }}"><?= get_label('projects', 'Projects') ?></a>
                        </li>
                        <li class="breadcrumb-item active">
                            <?= get_label('tags', 'Tags') ?>
                        </li>

                    </ol>
                </nav>
            </div>
            <div>
                <a href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#create_tag_modal"><button
                        type="button" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-placement="right"
                        data-bs-original-title=" <?= get_label('create_tag', 'Create tag') ?>"><i
                            class="bx bx-plus"></i></button></a>
            </div>
        </div>
        <x-tags-card />
    </div>
    <script>
        var label_update = '<?= get_label('update ', 'Update ') ?>';
        var label_delete = '<?= get_label('delete ', 'Delete ') ?>';
    </script>
    <script src="{{ asset('assets/js/pages/tags.js') }}"></script>
@endsection
