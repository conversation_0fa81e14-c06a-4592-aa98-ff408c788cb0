<div style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'; background-color: #ffffff; color: #718096; height: 100%; line-height: 1.4; margin: 0; padding: 0; width: 100%!important;">
<table style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'; background-color: #edf2f7; margin: 0; padding: 0; width: 100%;" role="presentation" width="100%" cellspacing="0" cellpadding="0">
<tbody>
<tr>
<td style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol';" align="center">
<table style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; margin: 0px; padding: 0px; width: 100%; height: 846.823px;" role="presentation" width="100%" cellspacing="0" cellpadding="0">
<tbody>
<tr style="height: 77.0833px;">
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; padding: 25px 0px; height: 77.0833px; text-align: center;"><span style="font-size: 18px;">{COMPANY_LOGO}</span></td>
</tr>
<tr style="height: 672.986px;">
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; background-color: rgb(237, 242, 247); margin: 0px; padding: 0px; width: 100%; height: 672.986px; border: hidden !important;" width="100%">
<table class="m_2051327272198114809inner-body" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; background-color: rgb(255, 255, 255); border-color: rgb(232, 229, 239); border-radius: 2px; border-width: 1px; margin: 0px auto; padding: 0px; width: 674px; height: 676px;" role="presentation" width="570" cellspacing="0" cellpadding="0" align="center">
<tbody>
<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; max-width: 100vw; padding: 32px; width: 672.882px;">
<h1 style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; color: rgb(61, 72, 82); font-size: 18px; font-weight: bold; margin-top: 0px; text-align: left;">Hello, {USER_FIRST_NAME} {USER_LAST_NAME}</h1>
<p style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">This is <span class="hljs-selector-tag">to</span> inform you that the status of <span class="hljs-selector-tag">a</span> leave request has been updated from {OLD_STATUS} to {NEW_STATUS}.<br><br>Details of the leave request are as follows:</p>
<p style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">- ID: {ID}<br>- Requestee: {REQUESTEE_FIRST_NAME} {REQUESTEE_LAST_NAME}<br>- Type: {TYPE}<br>- From: {FROM}<br>- To: {TO}<br>- Duration: {DURATION}<br>- Reason: {REASON}<br><br>Thank you.</p>
{SITE_URL}<br><br>Regards,</td>
</tr>
<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; max-width: 100vw; padding: 32px; width: 672.882px;">
<h1 style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'; color: #3d4852; font-size: 18px; font-weight: bold; margin-top: 0; text-align: left;">&nbsp;</h1>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
<tr style="height: 96.7535px;">
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; height: 96.7535px;">
<table class="m_2051327272198114809footer" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; margin: 0px auto; padding: 0px; text-align: center; width: 673px; height: 97px;" role="presentation" width="570" cellspacing="0" cellpadding="0" align="center">
<tbody>
<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; max-width: 100vw; padding: 32px; width: 671.875px;" align="center">
<p style="box-sizing: border-box; font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol'; line-height: 1.5em; margin-top: 0; color: #b0adc5; font-size: 12px; text-align: center;">&copy; {CURRENT_YEAR} {COMPANY_TITLE}. All rights reserved.</p>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<div class="yj6qo">&nbsp;</div>
<div class="adL">&nbsp;</div>
</div>