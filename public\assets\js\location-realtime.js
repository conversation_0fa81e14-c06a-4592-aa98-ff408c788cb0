/**
 * Real-time Location Updates Handler
 * Handles WebSocket connections for live location tracking
 */
class LocationRealtime {
    constructor(workspaceId, pusherConfig) {
        this.workspaceId = workspaceId;
        this.pusher = null;
        this.channel = null;
        this.pusherConfig = pusherConfig;
        this.callbacks = {
            onLocationCaptured: [],
            onConnectionStateChange: []
        };
        
        this.init();
    }

    /**
     * Initialize Pusher connection
     */
    init() {
        try {
            // Initialize Pusher
            this.pusher = new Pusher(this.pusherConfig.key, {
                cluster: this.pusherConfig.cluster,
                encrypted: this.pusherConfig.encrypted,
                authEndpoint: this.pusherConfig.authEndpoint,
                auth: {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                }
            });

            // Subscribe to workspace location channel
            this.channel = this.pusher.subscribe(`private-workspace.${this.workspaceId}.locations`);

            // Listen for location captured events
            this.channel.bind('location.captured', (data) => {
                this.handleLocationCaptured(data);
            });

            // Handle connection state changes
            this.pusher.connection.bind('state_change', (states) => {
                this.handleConnectionStateChange(states);
            });

            console.log('Location real-time tracking initialized');
        } catch (error) {
            console.error('Failed to initialize location real-time tracking:', error);
        }
    }

    /**
     * Handle new location capture
     */
    handleLocationCaptured(data) {
        console.log('New location captured:', data);
        
        // Trigger all registered callbacks
        this.callbacks.onLocationCaptured.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Error in location captured callback:', error);
            }
        });
    }

    /**
     * Handle connection state changes
     */
    handleConnectionStateChange(states) {
        console.log('Connection state changed:', states.current);
        
        // Trigger all registered callbacks
        this.callbacks.onConnectionStateChange.forEach(callback => {
            try {
                callback(states);
            } catch (error) {
                console.error('Error in connection state callback:', error);
            }
        });
    }

    /**
     * Register callback for location captured events
     */
    onLocationCaptured(callback) {
        if (typeof callback === 'function') {
            this.callbacks.onLocationCaptured.push(callback);
        }
    }

    /**
     * Register callback for connection state changes
     */
    onConnectionStateChange(callback) {
        if (typeof callback === 'function') {
            this.callbacks.onConnectionStateChange.push(callback);
        }
    }

    /**
     * Disconnect from Pusher
     */
    disconnect() {
        if (this.channel) {
            this.pusher.unsubscribe(`private-workspace.${this.workspaceId}.locations`);
        }
        if (this.pusher) {
            this.pusher.disconnect();
        }
    }
}

/**
 * Global location realtime instance
 */
window.LocationRealtime = LocationRealtime;
