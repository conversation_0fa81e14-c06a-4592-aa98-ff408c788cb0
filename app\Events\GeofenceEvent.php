<?php

namespace App\Events;

use App\Models\TaskLocation;
use App\Models\GeofenceZone;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GeofenceEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $location;
    public $zone;
    public $eventType;
    public $workspaceId;

    /**
     * Create a new event instance.
     */
    public function __construct(TaskLocation $location, GeofenceZone $zone, string $eventType)
    {
        $this->location = $location;
        $this->zone = $zone;
        $this->eventType = $eventType;
        $this->workspaceId = $zone->workspace_id;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('workspace.' . $this->workspaceId . '.geofence'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'location_id' => $this->location->id,
            'user_id' => $this->location->user_id,
            'user_name' => $this->location->user->first_name . ' ' . $this->location->user->last_name,
            'task_id' => $this->location->task_id,
            'task_title' => $this->location->task->title,
            'zone_id' => $this->zone->id,
            'zone_name' => $this->zone->name,
            'zone_purpose' => $this->zone->zone_purpose,
            'event_type' => $this->eventType,
            'latitude' => $this->location->latitude,
            'longitude' => $this->location->longitude,
            'address' => $this->location->formatted_address,
            'timestamp' => $this->location->created_at->toISOString(),
            'time' => $this->location->created_at->format('h:i A'),
            'date' => $this->location->created_at->format('M d, Y'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'geofence.event';
    }
}
