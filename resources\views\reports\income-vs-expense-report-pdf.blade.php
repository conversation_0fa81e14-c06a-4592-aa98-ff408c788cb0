<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Income vs Expense Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }

        /* Header Styles */
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            border-bottom: 2px solid #ddd;
        }

        .company-info {
            display: flex;
            align-items: center;
        }

        .company-logo {
            width: 400px;
            height: auto;
            margin-right: 10px;
        }

        .company-details h1 {
            margin: 0;
            color: #333;
        }

        .company-details p {
            margin: 5px 0;
            color: #666;
        }

        .report-info {
            text-align: right;
        }

        .report-info h2 {
            margin: 0;
            color: #333;
        }

        .report-info p {
            margin: 5px 0;
            color: #666;
        }

        /* Content Styles */
        .report-content {
            padding: 20px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th,
        td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
        }

        th {
            background-color: #f4f4f4;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .mb-10 {
            margin-bottom: 10px;
        }

        .mt-20 {
            margin-top: 20px;
        }

        .font-bold {
            font-weight: bold;
        }

        .text-large {
            font-size: 16px;
        }

        .text-small {
            font-size: 12px;
        }

        .text-muted {
            color: #777;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 16px;
        }

        .summary-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
        }

        .summary-label {
            font-size: 14px;
            color: #666;
        }

        .summary-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .summary-item {
            width: 20%;
            text-align: center;
        }

        @media (max-width: 576px) {
            .summary-item {
                font-size: 12px;
            }
        }

        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
            }

            table {
                page-break-inside: auto;
            }

            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
        }
    </style>
</head>

<body>
    <header>
        <div class="report-header">
            <div class="company-info">
                <img src="{{ asset($general_settings['full_logo']) }}" alt="{{ $general_settings['company_title'] }}"
                    class="company-logo">
                <div class="company-details">
                    <h1>{{ $general_settings['company_title'] }}</h1>
                    <span>{!! $general_settings['company_address'] !!}</span>
                    <p>{{ $general_settings['support_email'] }}</p>
                </div>
            </div>
            <div class="report-info">
                <h2>Income vs Expense Report</h2>
                <p>Date: {{ date('F d, Y h:m:s') }}</p>

                @php
                    $authUser = getAuthenticatedUser();
                @endphp
                <p>Generated by: {{ ucfirst($authUser->first_name) }} {{ ucfirst($authUser->last_name) }}</p>
            </div>
        </div>
    </header>
    <main>
        <div class="report-content">
            <table class="summary-table">
                <tr>
                    <td class="summary-item">
                        <div class="summary-label">Total Income</div>
                        <div class="summary-value">{{ $report->total_income }}</div>
                    </td>
                    <td class="summary-item">
                        <div class="summary-label">Total Expenses</div>
                        <div class="summary-value">{{ $report->total_expenses }}</div>
                    </td>
                    <td class="summary-item">
                        <div class="summary-label">Profit/Loss</div>
                        <div class="summary-value">{{ $report->profit_or_loss }}</div>
                    </td>
                </tr>
            </table>
            <div class="section mt-20">
                <h2 class="section-title">Invoices Details</h2>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Date Range</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($report->invoices as $invoice)
                            <tr>
                                <td><a href="/master-panel/estimates-invoices/view/{{ $invoice->id }}">{{ $invoice->id }}</a></td>
                                <td>{{ $invoice->from_date }} - {{ $invoice->to_date }}</td>
                                <td>{{ $invoice->amount }}</td>
                            </tr>
                        @endforeach
                        @if(count($report->invoices) == 0)
                            <tr>
                                <td colspan="3" class="text-center">No data available</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
            <div class="section mt-20">
                <h2 class="section-title">Expenses Details</h2>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($report->expenses as $expense)
                            <tr>
                                <td>{{ $expense->id }}</td>
                                <td>{{ $expense->title }}</td>
                                <td>{{ $expense->amount }}</td>
                                <td>{{ $expense->expense_date }}</td>
                            </tr>
                        @endforeach
                        @if(count($report->expenses) == 0)
                            <tr>
                                <td colspan="4" class="text-center">No data available</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
            <div class="section mt-20">
                <h2 class="section-title">Additional Information</h2>
                <p class="text-muted">This report was generated automatically. For any questions or concerns, please contact admin for support.</p>
            </div>
        </div>
    </main>
</body>

</html>
