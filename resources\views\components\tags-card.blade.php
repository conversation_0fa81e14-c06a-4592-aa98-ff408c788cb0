<div class="card">
    <div class="card-body">
        <div class="table-responsive text-nowrap">
            {{ $slot }}
            @if (is_countable($tags) && count($tags) > 0)
                <input type="hidden" id="data_type" value="tags">

                <table id="table" data-toggle="table" data-loading-template="loadingTemplate"
                    data-url="{{ route('tags.list') }}" data-icons-prefix="bx" data-icons="icons"
                    data-show-refresh="true" data-total-field="total" data-trim-on-search="false" data-data-field="rows"
                    data-page-list="[5, 10, 20, 50, 100, 200]" data-search="true" data-side-pagination="server"
                    data-show-columns="true" data-pagination="true" data-sort-name="id" data-sort-order="desc"
                    data-mobile-responsive="true" data-query-params="queryParams"
                    data-route-prefix="{{ Route::getCurrentRoute()->getPrefix() }}">

                    <thead>
                        <tr>
                            <th data-checkbox="true"></th>
                            <th data-sortable="true" data-field="id"><?= get_label('id', 'ID') ?></th>
                            <th data-sortable="true" data-field="title"><?= get_label('title', 'Title') ?></th>
                            <th data-sortable="true" data-field="color"><?= get_label('preview', 'Preview') ?></th>
                            <th data-formatter="actionsFormatter"><?= get_label('actions', 'Actions') ?></th>
                        </tr>
                    </thead>
                </table>
            @else
                <?php
                $type = 'Tags'; ?>
                <x-empty-state-card :type="$type" />
            @endif
        </div>
    </div>
</div>
