/* fonts import */
@import url("../fonts/octin_sports_rg.ttf");

body {
  font-family: "Poppins", sans-serif;
  color: #566a7f;
  background-color: #f4f5fb;
  overflow-x: hidden;
}

.layout_padding {
  padding: 90px 0;
}

.layout_padding2 {
  padding: 45px 0;
}

.layout_padding2-top {
  padding-top: 45px;
}

.layout_padding2-bottom {
  padding-bottom: 45px;
}

.layout_padding-top {
  padding-top: 90px;
}

.layout_padding-bottom {
  padding-bottom: 90px;
}

.long_section {
  margin-left: 45px;
  margin-right: 45px;
  padding-left: 15px;
  padding-right: 15px;
}

.heading_container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.heading_container h2 {
  position: relative;
  font-weight: bold;
  text-transform: uppercase;
}

/*header section*/
.hero_area {
  min-height: 100vh;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.sub_page .hero_area {
  min-height: auto;
}

.header_section {
  background-color: #f4f5fb;
}

.header_section .container-fluid {
  padding-right: 25px;
  padding-left: 25px;
}

.header_section .nav_container {
  margin: 0 auto;
}

.custom_nav-container .navbar-nav .nav-item .nav-link {
  padding: 3px 15px;
  margin: 10px 15px;
  color: #272727;
  text-align: center;
  text-transform: uppercase;
}

a,
a:hover,
a:focus {
  text-decoration: none;
}

a:hover,
a:focus {
  color: initial;
}

.btn,
.btn:focus {
  outline: none !important;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.custom_nav-container .nav_search-btn {
  width: 35px;
  height: 35px;
  padding: 0;
  border: none;
}

.navbar-brand span {
  font-size: 24px;
  font-weight: 700;
  color: #272727;
  text-transform: uppercase;
}

.custom_nav-container {
  z-index: 99999;
  padding: 5px 0;
}

.custom_nav-container .navbar-toggler {
  outline: none;
}

.custom_nav-container .navbar-toggler {
  padding: 0;
  width: 37px;
  height: 42px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.custom_nav-container .navbar-toggler span {
  display: block;
  width: 32px;
  height: 4px;
  background-color: #696cff;
  border-radius: 15px;
  margin: 7px 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
  transition: all 0.3s;
}

.custom_nav-container .navbar-toggler span::before,
.custom_nav-container .navbar-toggler span::after {
  content: "";
  position: absolute;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #696cff;
  border-radius: 15px;
  top: -10px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.custom_nav-container .navbar-toggler span::after {
  top: 10px;
}

.custom_nav-container .navbar-toggler[aria-expanded="true"] {
  -webkit-transform: rotate(360deg);
  transform: rotate(360deg);
}

.custom_nav-container .navbar-toggler[aria-expanded="true"] span {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.custom_nav-container .navbar-toggler[aria-expanded="true"] span::before,
.custom_nav-container .navbar-toggler[aria-expanded="true"] span::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
  top: 0;
}

.quote_btn-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.quote_btn-container a {
  color: #151515;
  margin-right: 25px;
  text-transform: uppercase;
}

.quote_btn-container a span {
  margin-right: 5px;
}

/*end header section*/
/* slider section */
.slider_section {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  background-color: #f9fafa;
}

.slider_section .row {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.slider_section #customCarousel {
  width: 100%;
  z-index: 3;
}

.slider_section .detail-box {
  color: #000000;
}

.slider_section .detail-box h1 {
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 0;
}

.slider_section .detail-box p {
  margin: 25px 0;
}

.slider_section .detail-box .btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0 -5px;
}

.slider_section .detail-box .btn-box a {
  margin: 5px;
  text-align: center;
  width: 165px;
}

.slider_section .detail-box .btn-box .btn1 {
  display: inline-block;
  padding: 10px 15px;
  background-color: #f89646;
  color: #ffffff;
  border-radius: 0;
  border: 1px solid #f89646;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.slider_section .detail-box .btn-box .btn1:hover {
  background-color: transparent;
  color: #f89646;
}

.slider_section .detail-box .btn-box .btn2 {
  display: inline-block;
  padding: 10px 15px;
  background-color: #6bb7be;
  color: #ffffff;
  border-radius: 0;
  border: 1px solid #6bb7be;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.slider_section .detail-box .btn-box .btn2:hover {
  background-color: transparent;
  color: #6bb7be;
}

.slider_section .img-box img {
  width: 100%;
}

.slider_section .carousel-indicators {
  position: unset;
  margin: 0;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 45px;
}

.slider_section .carousel-indicators li {
  background-color: #6bb7be;
  width: 12px;
  height: 12px;
  border-radius: 100%;
  opacity: 1;
}

.slider_section .carousel-indicators li.active {
  width: 17px;
  height: 17px;
  background-color: #f89646;
}

.furniture_section .box {
  margin-top: 45px;
  background-color: #f7fafa;
  padding: 25px;
  border-radius: 5px;
}

.furniture_section .box .img-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 275px;
  padding: 25px;
}

.furniture_section .box .img-box img {
  max-width: 100%;
  max-height: 100%;
}

.furniture_section .box .detail-box {
  margin-top: 15px;
}

.furniture_section .box .detail-box h5 {
  text-transform: uppercase;
  font-size: 18px;
}

.furniture_section .box .detail-box .price_box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.furniture_section .box .detail-box .price_box .price_heading {
  margin-bottom: 0;
}

.furniture_section .box .detail-box .price_box .price_heading span {
  color: #6bb7be;
}

.furniture_section .box .detail-box .price_box a {
  color: #6bb7be;
  text-transform: uppercase;
  font-size: 15px;
}

.about_section {
  background-color: #f9fafa;
}

.about_section .row {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.about_section .img-box img {
  width: 100%;
}

.about_section .detail-box p {
  margin-top: 10px;
  margin-bottom: 35px;
}

.about_section .detail-box a {
  display: inline-block;
  padding: 10px 45px;
  background-color: #6bb7be;
  color: #ffffff;
  border-radius: 0;
  border: 1px solid #6bb7be;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.about_section .detail-box a:hover {
  background-color: transparent;
  color: #6bb7be;
}

.blog_section .heading_container {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.blog_section .heading_container h2::before {
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.blog_section .box {
  margin-top: 55px;
  background-color: #ffffff;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
}

.blog_section .box .img-box {
  position: relative;
}

.blog_section .box .img-box img {
  width: 100%;
}

.blog_section .box .detail-box {
  padding: 25px;
}

.blog_section .box .detail-box h5 {
  font-weight: bold;
}

.blog_section .box .detail-box p {
  font-size: 15px;
  color: #444;
}

.blog_section .box .detail-box a {
  display: inline-block;
  padding: 10px 30px;
  background-color: #6bb7be;
  color: #ffffff;
  border-radius: 0;
  border: 1px solid #6bb7be;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.blog_section .box .detail-box a:hover {
  background-color: transparent;
  color: #6bb7be;
}

.client_section .heading_container {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.client_section .box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 45px;
  padding: 35px 25px;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
}

.client_section .box .img-box {
  border-radius: 100%;
  border: 10px solid #6bb7be;
  margin-right: 25px;
  min-width: 175px;
  max-width: 175px;
  overflow: hidden;
}

.client_section .box .img-box img {
  width: 100%;
}

.client_section .box .detail-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.client_section .box .detail-box .name {
  text-align: center;
}

.client_section .box .detail-box .name img {
  width: 25px;
  margin-bottom: 5px;
}

.client_section .box .detail-box .name h6 {
  color: #6bb7be;
  font-size: 20px;
}

.client_section .carousel_btn-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.client_section .carousel-control-prev,
.client_section .carousel-control-next {
  position: unset;
  width: 45px;
  height: 45px;
  border: none;
  opacity: 1;
  background-repeat: no-repeat;
  background-size: 12px;
  background-position: center;
  background-color: #6bb7be;
  background-position: center;
  border-radius: 100%;
  margin: 0 2.5px;
}

.client_section .carousel-control-prev:hover,
.client_section .carousel-control-next:hover {
  background-color: #000000;
}

.client_section .carousel-control-next {
  left: initial;
}

.contact_section {
  position: relative;
  background-color: #f9fafa;
  padding-top: 75px;
  padding-bottom: 75px;
}

.contact_section .heading_container {
  margin-bottom: 25px;
}

.contact_section .heading_container h2 {
  text-transform: uppercase;
}

.contact_section .form_container input {
  width: 100%;
  border: none;
  height: 50px;
  margin-bottom: 25px;
  padding-left: 15px;
  outline: none;
  color: #101010;
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.05);
}

.contact_section .form_container input::-webkit-input-placeholder {
  color: #333;
}

.contact_section .form_container input:-ms-input-placeholder {
  color: #333;
}

.contact_section .form_container input::-ms-input-placeholder {
  color: #333;
}

.contact_section .form_container input::placeholder {
  color: #333;
}

.contact_section .form_container input.message-box {
  height: 120px;
}

.contact_section .form_container button {
  border: none;
  text-transform: uppercase;
  display: inline-block;
  padding: 12px 55px;
  background-color: #6bb7be;
  color: #ffffff;
  border-radius: 0px;
  border: 1px solid #6bb7be;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.contact_section .form_container button:hover {
  background-color: transparent;
  color: #6bb7be;
}

.contact_section .map_container {
  height: 500px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  padding: 0;
}

.contact_section .map_container .map {
  height: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.contact_section .map_container .map #googleMap {
  height: 100%;
}

.info_section {
  background-color: #191e1f;
  color: #ffffff;
  padding-top: 45px;
  padding-bottom: 45px;
}

.info_section .contact_nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 25px 0;
  text-align: center;
}

.info_section .contact_nav a {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #ffffff;
}

.info_section .contact_nav a i {
  font-size: 28px;
}

.info_section .contact_nav a:hover {
  color: #f89646;
}

.info_section .info_top {
  padding: 45px 0;
}

.info_section h4 {
  text-transform: uppercase;
  position: relative;
  margin-bottom: 25px;
}

.info_section .info_links .info_links_menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.info_section .info_links .info_links_menu a {
  color: #ffffff;
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
}

.info_section .info_links .info_links_menu a:hover,
.info_section .info_links .info_links_menu a.active {
  color: #f89646;
}

.info_section .info_post .post_box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.info_section .info_post .post_box .img-box {
  min-width: 65px;
  max-width: 65px;
  height: 65px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #ffffff;
  -ms-flex-preferred-size: calc(33% - 10px);
  flex-basis: calc(33% - 10px);
  padding: 10px;
  margin: 5px;
}

.info_section .info_post .post_box .img-box img {
  max-width: 100%;
  max-height: 100%;
}

.info_section .info_post .post_box p {
  margin: 0;
}

.info_section .info_post .post_box:not(:nth-last-child(1)) {
  margin-bottom: 15px;
}

.info_section .info_form input {
  width: 100%;
  border: none;
  height: 45px;
  margin-bottom: 15px;
  padding-left: 25px;
  background-color: #eaeaea;
  outline: none;
  color: #101010;
}

.info_section .info_form button {
  display: inline-block;
  padding: 10px 45px;
  background-color: #6bb7be;
  color: #ffffff;
  border-radius: 0px;
  border: 1px solid #6bb7be;
  -webkit-transition: all .2s;
  transition: all .2s;
}

.info_section .info_form button:hover {
  background-color: transparent;
  color: #6bb7be;
}

.info_section .info_form .social_box {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 25px;
}

.info_section .info_form .social_box a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 5px;
  width: 45px;
  height: 45px;
  border: 1px solid #6bb7be;
  color: #6bb7be;
  font-size: 18px;
  margin-right: 10px;
}

.info_section .info_form .social_box a:hover {
  color: #f89646;
  border-color: #f89646;
}

/* footer section*/
.footer_section {
  position: relative;
  text-align: center;
}

.footer_section p {
  color: #000000;
  padding: 25px 0;
  margin: 0;
}

.footer_section p a {
  color: inherit;
}

/*# sourceMappingURL=style.css.map */
.navbar-brand {
  display: flex;
  justify-content: center;
  /* Center horizontally */
  align-items: center;
  /* Center vertically */
}
.nav-bar-logo {
  display: flex;
  justify-content: center;
  /* Center horizontally */
  align-items: center;
  /* Center vertically */
  position: relative;
  width: 200px;
  height: auto;
}
.font-size-18{
  font-size: 18px;
}

.img-box {
  position: relative;
  width: 100%;
  padding-top: calc(12/ 16 * 100%);
  /* 16:9 aspect ratio (height = width * 9/16) */
  overflow: hidden;
}

.img-box img {
  position: absolute;
  top: 0;
  left: 0;
  width: 120%;
  height: 120%;
  object-fit: cover;
  /* Ensure the image covers the entire container */
}
