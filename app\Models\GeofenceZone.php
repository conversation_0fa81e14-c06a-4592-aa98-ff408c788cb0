<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GeofenceZone extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'workspace_id',
        'created_by',
        'zone_type',
        'center_latitude',
        'center_longitude',
        'radius',
        'polygon_coordinates',
        'is_active',
        'zone_purpose',
        'alert_type',
        'dwell_time_threshold',
        'notify_managers',
        'notify_admins',
        'notify_user',
        'notification_settings',
        'active_hours',
        'active_days',
        'respect_working_hours',
        'entry_count',
        'exit_count',
        'last_activity'
    ];

    protected $casts = [
        'center_latitude' => 'decimal:7',
        'center_longitude' => 'decimal:7',
        'radius' => 'decimal:2',
        'polygon_coordinates' => 'array',
        'is_active' => 'boolean',
        'notify_managers' => 'boolean',
        'notify_admins' => 'boolean',
        'notify_user' => 'boolean',
        'notification_settings' => 'array',
        'active_hours' => 'array',
        'active_days' => 'array',
        'respect_working_hours' => 'boolean',
        'last_activity' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Valid zone types
     */
    const ZONE_TYPES = [
        'circle',
        'polygon',
        'rectangle',
    ];

    /**
     * Valid zone purposes
     */
    const ZONE_PURPOSES = [
        'office',
        'client_site',
        'restricted',
        'general',
        'parking',
        'meeting_point',
    ];

    /**
     * Valid alert types
     */
    const ALERT_TYPES = [
        'entry',
        'exit',
        'both',
        'none',
        'dwell',
    ];

    /**
     * Get the workspace that owns the geofence zone
     */
    public function workspace()
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Get the user who created the zone
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter active zones
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by zone type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('zone_type', $type);
    }

    /**
     * Scope to filter by purpose
     */
    public function scopeByPurpose($query, $purpose)
    {
        return $query->where('zone_purpose', $purpose);
    }

    /**
     * Check if a point is within this geofence zone
     */
    public function containsPoint($latitude, $longitude)
    {
        switch ($this->zone_type) {
            case 'circle':
                return $this->isPointInCircle($latitude, $longitude);
            case 'polygon':
                return $this->isPointInPolygon($latitude, $longitude);
            case 'rectangle':
                return $this->isPointInRectangle($latitude, $longitude);
            default:
                return false;
        }
    }

    /**
     * Check if point is within circular zone
     */
    private function isPointInCircle($latitude, $longitude)
    {
        if (!$this->radius) return false;
        
        $distance = $this->calculateDistance(
            $this->center_latitude,
            $this->center_longitude,
            $latitude,
            $longitude
        );
        
        return $distance <= $this->radius;
    }

    /**
     * Check if point is within polygon zone
     */
    private function isPointInPolygon($latitude, $longitude)
    {
        if (!$this->polygon_coordinates) return false;
        
        $vertices = $this->polygon_coordinates;
        $vertexCount = count($vertices);
        
        if ($vertexCount < 3) return false;
        
        $inside = false;
        $j = $vertexCount - 1;
        
        for ($i = 0; $i < $vertexCount; $i++) {
            $xi = $vertices[$i]['lat'];
            $yi = $vertices[$i]['lng'];
            $xj = $vertices[$j]['lat'];
            $yj = $vertices[$j]['lng'];
            
            if ((($yi > $longitude) != ($yj > $longitude)) &&
                ($latitude < ($xj - $xi) * ($longitude - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
            $j = $i;
        }
        
        return $inside;
    }

    /**
     * Check if point is within rectangle zone
     */
    private function isPointInRectangle($latitude, $longitude)
    {
        if (!$this->polygon_coordinates || count($this->polygon_coordinates) < 2) {
            return false;
        }
        
        $bounds = $this->polygon_coordinates;
        $minLat = min($bounds[0]['lat'], $bounds[1]['lat']);
        $maxLat = max($bounds[0]['lat'], $bounds[1]['lat']);
        $minLng = min($bounds[0]['lng'], $bounds[1]['lng']);
        $maxLng = max($bounds[0]['lng'], $bounds[1]['lng']);
        
        return $latitude >= $minLat && $latitude <= $maxLat &&
               $longitude >= $minLng && $longitude <= $maxLng;
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371000; // Earth's radius in meters
        
        $latFrom = deg2rad($lat1);
        $lonFrom = deg2rad($lon1);
        $latTo = deg2rad($lat2);
        $lonTo = deg2rad($lon2);
        
        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;
        
        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * Check if zone is currently active based on time/day settings
     */
    public function isCurrentlyActive()
    {
        if (!$this->is_active) return false;
        
        $now = now();
        
        // Check active days
        if ($this->active_days && !in_array($now->dayOfWeek, $this->active_days)) {
            return false;
        }
        
        // Check active hours
        if ($this->active_hours) {
            $currentTime = $now->format('H:i');
            $startTime = $this->active_hours['start'] ?? '00:00';
            $endTime = $this->active_hours['end'] ?? '23:59';
            
            if ($currentTime < $startTime || $currentTime > $endTime) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Increment entry count
     */
    public function recordEntry()
    {
        $this->increment('entry_count');
        $this->update(['last_activity' => now()]);
    }

    /**
     * Increment exit count
     */
    public function recordExit()
    {
        $this->increment('exit_count');
        $this->update(['last_activity' => now()]);
    }

    /**
     * Get zone center coordinates
     */
    public function getCenterCoordinatesAttribute()
    {
        return $this->center_latitude . ', ' . $this->center_longitude;
    }

    /**
     * Get zone area (approximate for circles)
     */
    public function getAreaAttribute()
    {
        if ($this->zone_type === 'circle' && $this->radius) {
            return pi() * pow($this->radius, 2);
        }
        
        return null; // For polygons, would need more complex calculation
    }

    /**
     * Get formatted zone description
     */
    public function getFormattedDescriptionAttribute()
    {
        $parts = [];
        
        $parts[] = ucfirst($this->zone_type) . ' zone';
        
        if ($this->zone_type === 'circle' && $this->radius) {
            $parts[] = 'radius: ' . $this->radius . 'm';
        }
        
        $parts[] = 'purpose: ' . ucfirst(str_replace('_', ' ', $this->zone_purpose));
        
        return implode(', ', $parts);
    }
}
