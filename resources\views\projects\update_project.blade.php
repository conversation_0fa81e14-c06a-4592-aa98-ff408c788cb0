@extends('layout')

@section('title')
    <?= get_label('update_project', 'Update project') ?>
@endsection

@section('content')
    <div class="container-fluid">


        <div class="d-flex justify-content-between mt-4">
            <div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-style1">
                        <li class="breadcrumb-item">
                            <a href="{{ route('home.index') }}"><?= get_label('home', 'Home') ?></a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('projects.index') }}"><?= get_label('projects', 'Projects') ?></a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('projects.info', ['id' => $project->id]) }}">{{ $project->title }}</a>
                        </li>
                        <li class="breadcrumb-item active">
                            <?= get_label('update', 'Update') ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>



        <div class="card">
            <div class="card-body">
                <form action="{{ route('projects.update', ['id' => $project->id]) }}" class="form-submit-event"
                    method="POST">
                    <input type="hidden" name="redirect_url" value="{{ route('projects.index') }}">
                    @csrf
                    @method('PUT')
                    <div class="row">
                        <div class="mb-3">
                            <label for="title" class="form-label"><?= get_label('title', 'Title') ?> <span
                                    class="asterisk">*</span></label>
                            <input class="form-control" type="text" id="title" name="title"
                                placeholder="Enter Title" value="{{ $project->title }}">
                            @error('title')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    <div class="row">
                        <div class="mb-3 col-md-6">
                            <label class="form-label" for="status_id"><?= get_label('status', 'Status') ?> <span
                                    class="asterisk">*</span></label>
                            <div class="input-group">
                                <select class="form-select" id="status_id" name="status_id">
                                @foreach ($statuses as $status)

@if ($status->admin_id == getAdminIdByUserRole() || $status->admin_id === null)


                                        <option value="{{ $status->id }}" class="badge bg-label-{{ $status->color }}"
                                            <?php if ($project->status->id == $status->id) {
                                                print_r('selected');
                                            } ?>>{{ $status->title }} ({{ $status->color }})</option>
                                   @endif

                                    @endforeach
                                </select>
                            </div>
                            <div class="mt-2">
                                <a href="javascript:void(0);" data-bs-toggle="modal"
                                    data-bs-target="#create_status_modal"><button type="button"
                                        class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-placement="right"
                                        data-bs-original-title=" <?= get_label('create_status', 'Create status') ?>"><i
                                            class="bx bx-plus"></i></button></a>
                                <a href="{{ route('status.index') }}" target="_blank"><button type="button"
                                        class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-placement="right"
                                        data-bs-original-title="<?= get_label('manage_statuses', 'Manage statuses') ?>"><i
                                            class="bx bx-list-ul"></i></button></a>
                            </div>
                            @error('status_id')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-6">
                            <label for="budget" class="form-label"><?= get_label('budget', 'Budget') ?></label>
                            <div class="input-group input-group-merge">
                                <span class="input-group-text">{{ $general_settings['currency_symbol'] }}</span>
                                <input class="form-control" type="number" min="0" id="budget" name="budget"
                                    placeholder="Enter Budget" value="{{ $project->budget ?? '' }}">
                            </div>
                            @error('budget')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-6">
                            <label class="form-label" for="start_date"><?= get_label('starts_at', 'Starts at') ?> <span
                                    class="asterisk">*</span></label>
                            <input type="text" name="start_date" id="start_date" class="form-control"
                                value="{{ format_date($project->start_date) }}">
                            @error('start_date')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="mb-3 col-md-6">
                            <label class="form-label" for="end_date"><?= get_label('ends_at', 'Ends at') ?> <span
                                    class="asterisk">*</span></label>
                            <input type="text" id="end_date" name="end_date" class="form-control"
                                value="{{ format_date($project->end_date) }}">
                            @error('end_date')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-3">
                            <label class="form-label"
                                for="user_id"><?= get_label('select_users', 'Select users') ?></label>
                            <div class="input-group">
                                <select id="" class="form-control js-example-basic-multiple" name="user_id[]"
                                    multiple="multiple"
                                    data-placeholder="<?= get_label('type_to_search', 'Type to search') ?>">
                                    <?php
                                    $project_users = $project->users;
                                    ?>
                                    @foreach ($users as $user)
                                        <?php $selected = $user->id == $project->created_by ? 'selected' : ''; ?>
                                        <option value="{{ $user->id }}" <?= $selected ?> <?php if ($project_users->contains($user)) {
                                            echo 'selected';
                                        } ?>>
                                            {{ $user->first_name }} {{ $user->last_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="mb-3">
                            <label class="form-label"
                                for="client_id"><?= get_label('select_clients', 'Select clients') ?></label>
                            <div class="input-group">
                                <select id="" class="form-control js-example-basic-multiple" name="client_id[]"
                                    multiple="multiple"
                                    data-placeholder="<?= get_label('type_to_search', 'Type to search') ?>">
                                    <?php
                                    $project_clients = $project->clients;
                                    ?>
                                    @foreach ($clients as $client)
                                        <option value="{{ $client->id }}" <?php if ($project_clients->contains($client)) {
                                            echo 'selected';
                                        } ?>>{{ $client->first_name }}
                                            {{ $client->last_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="mb-3 col-md-12">
                            <label class="form-label"
                                for=""><?= get_label('select_tags', 'Select tags') ?></label>
                            <div class="input-group">
                                <select id="" class="form-control js-example-basic-multiple" name="tag_ids[]"
                                    multiple="multiple"
                                    data-placeholder="<?= get_label('type_to_search', 'Type to search') ?>">
                                    <?php
                                    $project_tags = $project->tags;
                                    ?>
                                    @foreach ($tags as $tag)
                                        <option value="{{ $tag->id }}" <?php if ($project_tags->contains($tag)) {
                                            echo 'selected';
                                        } ?>>{{ $tag->title }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mt-2">
                                <a href="javascript:void(0);" data-bs-toggle="modal"
                                    data-bs-target="#create_tag_modal"><button type="button"
                                        class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-placement="right"
                                        data-bs-original-title=" <?= get_label('create_tag', 'Create tag') ?>"><i
                                            class="bx bx-plus"></i></button></a>
                                <a href="{{ route('tags.index') }}"><button type="button"
                                        class="btn btn-sm btn-primary" data-bs-toggle="tooltip" data-bs-placement="right"
                                        data-bs-original-title="<?= get_label('manage_tags', 'Manage tags') ?>"><i
                                            class="bx bx-list-ul"></i></button></a>
                            </div>
                        </div>
                    </div>

                    <div class="row">

                        <div class="mb-3">
                            <label for="description" class="form-label"><?= get_label('description', 'Description') ?>
                                <span class="asterisk">*</span></label>
                            <textarea class="form-control" id="description" name="description" placeholder="Enter Description" rows="5">{{ $project->description }}</textarea>
                            @error('description')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>


                    <div class="mt-2">
                        <button type="submit" class="btn btn-primary me-2"
                            id="submit_btn"><?= get_label('update', 'Update') ?></button>
                        <button type="reset"
                            class="btn btn-outline-secondary"><?= get_label('cancel', 'Cancel') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
