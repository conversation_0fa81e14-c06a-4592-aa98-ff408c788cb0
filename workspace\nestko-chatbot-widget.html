<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NestKo AI Chat Widget</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom scrollbar */
        .chat-scroll::-webkit-scrollbar {
            width: 3px;
        }
        .chat-scroll::-webkit-scrollbar-track {
            background: transparent;
        }
        .chat-scroll::-webkit-scrollbar-thumb {
            background: #e5e7eb;
            border-radius: 2px;
        }
        
        /* Smooth animations */
        .chat-widget {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
        
        .slide-up {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Floating button pulse */
        .pulse-ring {
            animation: pulseRing 2s infinite;
        }
        
        @keyframes pulseRing {
            0% { transform: scale(0.33); }
            40%, 50% { opacity: 0; }
            100% { opacity: 0; transform: scale(1.2); }
        }
        
        /* Typing indicator */
        .typing-dot {
            animation: typingDot 1.5s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typingDot {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        /* Button hover effects */
        .quick-btn {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .quick-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* Gradient background for closed state */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <!-- Chat Widget Container -->
    <div class="fixed bottom-6 right-6 z-50">
        <!-- Closed State - Initial Landing -->
        <div id="closedWidget" class="chat-widget">
            <div class="w-80 bg-white rounded-2xl shadow-2xl overflow-hidden slide-up">
                <!-- Header with gradient -->
                <div class="gradient-bg text-white p-6 relative">
                    <div class="absolute top-4 left-4">
                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="absolute top-4 right-4">
                        <button id="minimizeFromClosed" class="text-white/80 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    
                    <h2 class="text-2xl font-bold mt-8 mb-2">How can we help you today?</h2>
                    <p class="text-white/90 text-sm">👋</p>
                </div>
                
                <!-- Content -->
                <div class="p-6 bg-white">
                    <!-- Bot message -->
                    <div class="flex items-start space-x-3 mb-6">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 text-sm mb-1">ChatBot</div>
                            <div class="text-gray-600 text-sm">Let me know if you have any questions!</div>
                        </div>
                    </div>
                    
                    <!-- Main CTA Button -->
                    <button id="openChatBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-xl font-medium transition-colors mb-4">
                        Chat with us
                    </button>
                    
                    <!-- Quick Action Items -->
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl quick-btn cursor-pointer" data-action="trial">
                            <div class="flex items-center space-x-3">
                                <span class="text-orange-500">⚡</span>
                                <span class="text-gray-700 font-medium text-sm">Start free trial</span>
                            </div>
                            <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl quick-btn cursor-pointer" data-action="help">
                            <div class="flex items-center space-x-3">
                                <span class="text-blue-500">📘</span>
                                <span class="text-gray-700 font-medium text-sm">Visit Help Center</span>
                            </div>
                            <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Footer -->
                    <div class="flex justify-between mt-6 pt-4 border-t border-gray-100">
                        <button class="flex flex-col items-center space-y-1 text-gray-600 hover:text-gray-900 transition-colors">
                            <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                </svg>
                            </div>
                            <span class="text-xs">Home</span>
                        </button>
                        <button class="flex flex-col items-center space-y-1 text-gray-600 hover:text-gray-900 transition-colors">
                            <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                                </svg>
                            </div>
                            <span class="text-xs">Contact</span>
                        </button>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="px-6 py-3 bg-gray-50 border-t">
                    <div class="text-center text-xs text-gray-400">
                        Powered by <span class="text-blue-600 font-medium">ChatBot</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat State - Full Chat Interface -->
        <div id="chatWidget" class="chat-widget hidden">
            <div class="w-80 h-[500px] bg-white rounded-2xl shadow-2xl overflow-hidden flex flex-col slide-up">
                <!-- Header -->
                <div class="bg-white border-b border-gray-100 p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <button id="backBtn" class="text-gray-600 hover:text-gray-900 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs font-bold">CB</span>
                        </div>
                        <span class="font-medium text-gray-900">ChatBot</span>
                    </div>
                    <button id="minimizeFromChat" class="text-gray-600 hover:text-gray-900 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </button>
                </div>
                
                <!-- Messages -->
                <div id="chatMessages" class="flex-1 overflow-y-auto p-4 space-y-4 chat-scroll bg-gray-50">
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-2 fade-in">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            CB
                        </div>
                        <div class="flex-1">
                            <div class="bg-white rounded-2xl rounded-tl-md p-4 shadow-sm">
                                <p class="text-gray-800 font-medium mb-2">Hello there! 👋 It's nice to meet you!</p>
                                <p class="text-gray-600 text-sm mb-4">What brings you here today? Please use the navigation below or ask me anything about ChatBot product. ✨</p>
                                
                                <!-- Quick Reply Buttons -->
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="quick-reply-btn quick-btn bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg p-2 text-xs text-blue-700 font-medium" data-message="I want to build an AI chatbot">
                                        🔨 Build AI chatbot
                                    </button>
                                    <button class="quick-reply-btn quick-btn bg-yellow-50 hover:bg-yellow-100 border border-yellow-200 rounded-lg p-2 text-xs text-yellow-700 font-medium" data-message="I need help using ChatBot">
                                        👍 Using ChatBot
                                    </button>
                                    <button class="quick-reply-btn quick-btn bg-orange-50 hover:bg-orange-100 border border-orange-200 rounded-lg p-2 text-xs text-orange-700 font-medium" data-message="I have questions about NestKo">
                                        😥 I have questions
                                    </button>
                                    <button class="quick-reply-btn quick-btn bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg p-2 text-xs text-purple-700 font-medium" data-message="I'm just browsing">
                                        👀 Just browsing
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Input Area -->
                <div class="p-4 bg-white border-t border-gray-100">
                    <div class="flex items-center space-x-3">
                        <div class="flex-1 relative">
                            <input 
                                id="messageInput" 
                                type="text" 
                                placeholder="Write a message..." 
                                class="w-full bg-gray-100 border-0 rounded-full px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-colors"
                            />
                        </div>
                        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-8-2h8m-8-2h8"/>
                            </svg>
                        </button>
                        <button 
                            id="sendBtn" 
                            class="p-2 bg-gray-100 hover:bg-blue-600 hover:text-white text-gray-400 rounded-full transition-colors disabled:opacity-50"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="text-center mt-2">
                        <div class="text-xs text-gray-400">
                            Powered by <span class="text-blue-600 font-medium">ChatBot</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Button (Minimized State) -->
        <div id="floatingBtn" class="hidden relative">
            <div class="absolute inset-0 bg-blue-600 rounded-full pulse-ring"></div>
            <button class="relative w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-2xl text-white transition-colors">
                <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                </svg>
            </button>
        </div>
    </div>

    <script>
        class NestKoChatWidget {
            constructor() {
                this.apiKey = 'YOUR_OPENROUTER_API_KEY';
                this.apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
                this.model = 'z-ai/glm-4.5-air:free';
                this.chatHistory = [];
                this.currentState = 'closed'; // closed, chat, minimized
                
                this.initializeElements();
                this.attachEventListeners();
                this.initializeChat();
            }
            
            initializeElements() {
                this.closedWidget = document.getElementById('closedWidget');
                this.chatWidget = document.getElementById('chatWidget');
                this.floatingBtn = document.getElementById('floatingBtn');
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
            }
            
            attachEventListeners() {
                // State transitions
                document.getElementById('openChatBtn').addEventListener('click', () => this.openChat());
                document.getElementById('backBtn').addEventListener('click', () => this.goBack());
                document.getElementById('minimizeFromClosed').addEventListener('click', () => this.minimize());
                document.getElementById('minimizeFromChat').addEventListener('click', () => this.minimize());
                document.getElementById('floatingBtn').addEventListener('click', () => this.restore());
                
                // Quick actions
                document.addEventListener('click', (e) => {
                    if (e.target.closest('[data-action="trial"]')) {
                        this.openChat();
                        setTimeout(() => this.sendMessage("I want to start a free trial"), 500);
                    }
                    if (e.target.closest('[data-action="help"]')) {
                        this.openChat();
                        setTimeout(() => this.sendMessage("I need help with ChatBot"), 500);
                    }
                });
                
                // Quick reply buttons
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('quick-reply-btn')) {
                        const message = e.target.getAttribute('data-message');
                        this.sendMessage(message);
                    }
                });
                
                // Send message
                this.sendBtn.addEventListener('click', () => this.handleSend());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.handleSend();
                    }
                });
            }
            
            initializeChat() {
                this.chatHistory.push({
                    role: "system",
                    content: "You are NestBot, the official AI assistant for NestKo (https://nestko.in), a SaaS platform for building and integrating AI chatbots. Help users with questions, features, onboarding, pricing, signup, and navigation. You know the full functionality of the site. Respond clearly and professionally. If user asks to visit a page, return: button:Go to Pricing. If user wants to sign up, return: [signup-form]. Never mention Tskify — only NestKo."
                });
            }
            
            openChat() {
                this.currentState = 'chat';
                this.closedWidget.classList.add('hidden');
                this.chatWidget.classList.remove('hidden');
                this.floatingBtn.classList.add('hidden');
                
                // Focus input
                setTimeout(() => this.messageInput.focus(), 300);
            }
            
            goBack() {
                this.currentState = 'closed';
                this.chatWidget.classList.add('hidden');
                this.closedWidget.classList.remove('hidden');
                this.floatingBtn.classList.add('hidden');
            }
            
            minimize() {
                this.currentState = 'minimized';
                this.closedWidget.classList.add('hidden');
                this.chatWidget.classList.add('hidden');
                this.floatingBtn.classList.remove('hidden');
            }
            
            restore() {
                if (this.chatHistory.length > 1) {
                    this.openChat();
                } else {
                    this.goBack();
                }
            }
            
            async handleSend() {
                const message = this.messageInput.value.trim();
                if (message) {
                    this.sendMessage(message);
                    this.messageInput.value = '';
                }
            }
            
            async sendMessage(message) {
                // Add user message
                this.addUserMessage(message);
                
                // Add to history
                this.chatHistory.push({
                    role: "user",
                    content: message
                });
                
                // Show typing indicator
                this.showTypingIndicator();
                
                try {
                    const response = await this.callOpenRouterAPI();
                    this.hideTypingIndicator();
                    
                    if (response) {
                        this.addBotMessage(response);
                        this.chatHistory.push({
                            role: "assistant",
                            content: response
                        });
                    }
                } catch (error) {
                    this.hideTypingIndicator();
                    console.error('API Error:', error);
                    this.addBotMessage("I'm sorry, I'm having trouble connecting right now. Please try again later.");
                }
            }
            
            async callOpenRouterAPI() {
                const response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey}`
                    },
                    body: JSON.stringify({
                        model: this.model,
                        messages: this.chatHistory
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                return data.choices[0].message.content;
            }
            
            addUserMessage(message) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex justify-end fade-in';
                messageDiv.innerHTML = `
                    <div class="max-w-xs">
                        <div class="bg-blue-600 text-white rounded-2xl rounded-tr-md p-3">
                            <p class="text-sm">${this.escapeHtml(message)}</p>
                        </div>
                    </div>
                `;
                
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            addBotMessage(message) {
                const processedMessage = this.processSpecialTags(message);
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-2 fade-in';
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                        CB
                    </div>
                    <div class="flex-1 max-w-xs">
                        <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                            ${processedMessage}
                        </div>
                    </div>
                `;
                
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.id = 'typingIndicator';
                typingDiv.className = 'flex items-start space-x-2 fade-in';
                typingDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                        CB
                    </div>
                    <div class="flex-1">
                        <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                                <div class="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                                <div class="w-2 h-2 bg-gray-400 rounded-full typing-dot"></div>
                            </div>
                        </div>
                    </div>
                `;
                
                this.chatMessages.appendChild(typingDiv);
                this.scrollToBottom();
            }
            
            hideTypingIndicator() {
                const indicator = document.getElementById('typingIndicator');
                if (indicator) indicator.remove();
            }
            
            processSpecialTags(message) {
                let processed = message;
                
                // Handle [signup-form] tag
                if (message.includes('[signup-form]')) {
                    processed = processed.replace('[signup-form]', this.createSignupForm());
                }
                
                // Handle [button:Text](url:link) pattern
                const buttonRegex = /\[button:([^\]]+)\]\(url:([^\)]+)\)/g;
                processed = processed.replace(buttonRegex, (match, buttonText, url) => {
                    return this.createActionButton(buttonText, url);
                });
                
                return processed;
            }
            
            createSignupForm() {
                return `
                    <div class="signup-form mt-3 p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-800 mb-3">Sign up for NestKo</h4>
                        <form id="signupForm" class="space-y-3">
                            <input type="text" id="signupName" placeholder="Full Name" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <input type="email" id="signupEmail" placeholder="Email Address" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <input type="password" id="signupPassword" placeholder="Password" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg text-sm transition-colors">
                                Create Account
                            </button>
                        </form>
                    </div>
                `;
            }
            
            createActionButton(text, url) {
                return `
                    <div class="mt-3">
                        <a href="${url}" target="_blank" class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                            ${this.escapeHtml(text)}
                        </a>
                    </div>
                `;
            }
            
            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }
            
            escapeHtml(text) {
                const map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#039;'
                };
                return text.replace(/[&<>"']/g, m => map[m]);
            }
        }
        
        // Handle dynamic form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'signupForm') {
                e.preventDefault();
                const name = document.getElementById('signupName').value;
                
                setTimeout(() => {
                    const successMessage = `🎉 Welcome to NestKo, ${name}! Your chatbot journey begins now.`;
                    chatWidget.addBotMessage(successMessage);
                    e.target.closest('.signup-form').innerHTML = `
                        <div class="text-center text-green-600 font-medium">
                            ✅ Account created successfully!
                        </div>
                    `;
                }, 1000);
            }
        });
        
        // Initialize widget
        const chatWidget = new NestKoChatWidget();
    </script>
</body>
</html>