@extends('layouts.app')

@section('title', 'Location Analytics')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Location Analytics
                </h2>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filtersModal">
                        <i class="fas fa-filter me-1"></i>
                        Filters
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportAnalytics()">
                        <i class="fas fa-download me-1"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-route fa-2x mb-2"></i>
                    <h6>Total Distance</h6>
                    <h4 id="totalDistance">-</h4>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h6>Total Duration</h6>
                    <h4 id="totalDuration">-</h4>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                    <h6>Total Locations</h6>
                    <h4 id="totalLocations">-</h4>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                    <h6>Efficiency Score</h6>
                    <h4 id="efficiencyScore">-</h4>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                    <h6>Geofence Events</h6>
                    <h4 id="geofenceEvents">-</h4>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                    <h6>Active Days</h6>
                    <h4 id="activeDays">-</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Daily Distance Traveled
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="distanceChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Location Activity by Hour
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="hourlyChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Efficiency and Geofence Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-gauge-high me-2"></i>
                        Efficiency Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="efficiencyChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Geofence Activity
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="geofenceChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        Daily Analytics Breakdown
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="analyticsTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Distance</th>
                                    <th>Duration</th>
                                    <th>Locations</th>
                                    <th>Check-ins</th>
                                    <th>Check-outs</th>
                                    <th>Efficiency</th>
                                    <th>Geofence Events</th>
                                </tr>
                            </thead>
                            <tbody id="analyticsTableBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Modal -->
<div class="modal fade" id="filtersModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Analytics Filters</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="filtersForm">
                    <div class="mb-3">
                        <label for="startDate" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="startDate" name="start_date">
                    </div>
                    <div class="mb-3">
                        <label for="endDate" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="endDate" name="end_date">
                    </div>
                    <div class="mb-3">
                        <label for="userId" class="form-label">Employee</label>
                        <select class="form-select" id="userId" name="user_id">
                            <option value="">All Employees</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let analyticsData = {};
let charts = {};

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadAnalyticsData();
    
    // Set default dates (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
});

function initializeCharts() {
    // Distance Chart
    const distanceCtx = document.getElementById('distanceChart').getContext('2d');
    charts.distance = new Chart(distanceCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Distance (km)',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Hourly Activity Chart
    const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
    charts.hourly = new Chart(hourlyCtx, {
        type: 'bar',
        data: {
            labels: Array.from({length: 24}, (_, i) => `${i}:00`),
            datasets: [{
                label: 'Location Updates',
                data: new Array(24).fill(0),
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Efficiency Chart
    const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
    charts.efficiency = new Chart(efficiencyCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Efficiency Score',
                data: [],
                borderColor: 'rgb(255, 206, 86)',
                backgroundColor: 'rgba(255, 206, 86, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Geofence Chart
    const geofenceCtx = document.getElementById('geofenceChart').getContext('2d');
    charts.geofence = new Chart(geofenceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Entries', 'Exits'],
            datasets: [{
                data: [0, 0],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(255, 99, 132, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function loadAnalyticsData() {
    const formData = new FormData(document.getElementById('filtersForm'));
    const params = new URLSearchParams(formData);
    
    fetch(`/location/analytics-data?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.message);
            }
            
            analyticsData = data.analytics;
            updateSummaryCards();
            updateCharts();
            updateTable();
        })
        .catch(error => {
            console.error('Error loading analytics data:', error);
            alert('Failed to load analytics data: ' + error.message);
        });
}

function updateSummaryCards() {
    document.getElementById('totalDistance').textContent = formatDistance(analyticsData.total_distance);
    document.getElementById('totalDuration').textContent = formatDuration(analyticsData.total_duration);
    document.getElementById('totalLocations').textContent = analyticsData.total_locations.toLocaleString();
    document.getElementById('efficiencyScore').textContent = Math.round(analyticsData.avg_efficiency_score || 0) + '%';
    document.getElementById('geofenceEvents').textContent = analyticsData.total_geofence_events.toLocaleString();
    document.getElementById('activeDays').textContent = analyticsData.unique_days;
}

function updateCharts() {
    // This would be populated with actual daily data from the server
    // For now, showing placeholder implementation
    
    // Update distance chart with sample data
    const dates = getLast7Days();
    charts.distance.data.labels = dates;
    charts.distance.data.datasets[0].data = dates.map(() => Math.random() * 50);
    charts.distance.update();
    
    // Update efficiency chart
    charts.efficiency.data.labels = dates;
    charts.efficiency.data.datasets[0].data = dates.map(() => 60 + Math.random() * 40);
    charts.efficiency.update();
    
    // Update geofence chart
    charts.geofence.data.datasets[0].data = [
        Math.floor(analyticsData.total_geofence_events * 0.6),
        Math.floor(analyticsData.total_geofence_events * 0.4)
    ];
    charts.geofence.update();
}

function updateTable() {
    const tbody = document.getElementById('analyticsTableBody');
    tbody.innerHTML = '';
    
    // Sample data - in real implementation, this would come from server
    const dates = getLast7Days();
    dates.forEach(date => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${date}</td>
            <td>${formatDistance(Math.random() * 50000)}</td>
            <td>${formatDuration(Math.random() * 28800)}</td>
            <td>${Math.floor(Math.random() * 100)}</td>
            <td>${Math.floor(Math.random() * 10)}</td>
            <td>${Math.floor(Math.random() * 10)}</td>
            <td>${Math.floor(60 + Math.random() * 40)}%</td>
            <td>${Math.floor(Math.random() * 20)}</td>
        `;
    });
}

function getLast7Days() {
    const dates = [];
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toLocaleDateString());
    }
    return dates;
}

function formatDistance(meters) {
    if (meters >= 1000) {
        return (meters / 1000).toFixed(1) + ' km';
    }
    return Math.round(meters) + ' m';
}

function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
}

function applyFilters() {
    loadAnalyticsData();
    bootstrap.Modal.getInstance(document.getElementById('filtersModal')).hide();
}

function exportAnalytics() {
    // Implementation for exporting analytics data
    const csvContent = "data:text/csv;charset=utf-8," + 
        "Metric,Value\n" +
        `Total Distance,${analyticsData.total_distance}\n` +
        `Total Duration,${analyticsData.total_duration}\n` +
        `Total Locations,${analyticsData.total_locations}\n` +
        `Efficiency Score,${analyticsData.avg_efficiency_score}\n` +
        `Geofence Events,${analyticsData.total_geofence_events}\n` +
        `Active Days,${analyticsData.unique_days}`;
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "location_analytics.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
@endpush
