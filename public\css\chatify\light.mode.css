/*app scroll*/
.app-scroll::-webkit-scrollbar-thumb,
.app-scroll-thin::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-color);
}
.app-scroll-thin::-webkit-scrollbar {
    background: var(--secondary-bg-color);
}
.app-scroll::-webkit-scrollbar:hover,
.app-scroll-thin::-webkit-scrollbar:hover {
    background: var(--secondary-bg-color);
}

.messenger {
    background: var(--primary-bg-color);
}
.messenger-search[type="text"] {
    background: var(--secondary-bg-color);
    color: #333;
}
.messenger-listView {
    background: var(--primary-bg-color);
    border: 1px solid var(--border-color);
}
.messenger-listView-tabs {
    border-bottom: 1px solid var(--border-color);
}
.messenger-listView-tabs a:hover,
.messenger-listView-tabs a:focus {
    background-color: var(--secondary-bg-color);
}
.messenger-favorites div.avatar {
    border: 2px solid var(--primary-bg-color);
}

.messenger-list-item:hover {
    background: var(--secondary-bg-color);
}
.messenger-messagingView {
    border-top: 1px solid var(--secondary-bg-color);
    border-bottom: 1px solid var(--secondary-bg-color);
    background: var(--messagingView-bg-color);
}
.m-header-messaging {
    background: var(--primary-bg-color);
}
.messenger-infoView {
    background: var(--primary-bg-color);
    border: 1px solid var(--border-color);
}
.messenger-infoView > p {
    color: #000;
}
.divider {
    border-top: 1px solid var(--border-color);
}
.messenger-sendCard {
    background: var(--primary-bg-color);
    border-top: 1px solid var(--border-color);
}
.attachment-preview > p {
    color: #333;
}
.m-send {
    color: #333;
}
.message-card .message {
    background: var(--message-card-color);
    color: #656b75;
    box-shadow: 0px 6px 11px rgba(18, 67, 105, 0.03);
}
.m-li-divider {
    border-bottom: 1px solid var(--border-color);
}
.m-header a,
.m-header a:hover,
.m-header a:focus {
    text-decoration: none;
    color: #202020;
}
.messenger-list-item td p {
    color: #3c3c3c;
}
.messenger-list-item td span {
    color: #929292;
}
.activeStatus {
    border: 2px solid var(--primary-bg-color);
}
.messenger-list-item:hover .activeStatus {
    border-color: var(--secondary-bg-color);
}
.messenger-favorites > div p {
    color: #4a4a4a;
}

.avatar {
    background-color: var(--secondary-bg-color);
    border-color: var(--border-color);
}
.messenger-sendCard svg {
    color: var(--send-input-icons-color);
}
.messenger-title {
    color: #797979;
}
.messenger-title > span {
    background-color: var(--primary-bg-color);
}
.messenger-title::before {
    background-color: var(--border-color);
}
.message-hint span {
    background: var(--message-hint-bg-color);
    color: var(--message-hint-color);
}
/*
***********************************************
* Placeholder loading
***********************************************
*/
.loadingPlaceholder-body div,
.loadingPlaceholder-header tr td div {
    background: var(--secondary-bg-color);
    background-image: -webkit-linear-gradient(
        left,
        var(--secondary-bg-color) 0%,
        var(--secondary-bg-color) 20%,
        var(--secondary-bg-color) 40%,
        var(--secondary-bg-color) 100%
    );
}
.messenger-infoView > nav > p {
    color: #333;
}
/*
***********************************************
* App Modal
***********************************************
*/

.app-modal-card {
    background: var(--modal-bg-color);
}
.app-modal-header {
    color: #000;
}
.app-modal-body {
    color: #000;
}

/*
*****************************************
* Responsive Design
*****************************************
*/
@media (max-width: 1060px) {
    .messenger-infoView {
        box-shadow: 0px 0px 20px rgba(18, 67, 105, 0.06);
    }
}
@media (max-width: 980px) {
    .messenger-listView {
        box-shadow: 0px 0px 20px rgba(18, 67, 105, 0.06);
    }
}
