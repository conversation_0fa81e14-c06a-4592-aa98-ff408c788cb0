<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">

  <rect width="100%" height="100%" fill="#ffffff" stroke="#e9ecef" stroke-width="2"/>
  <rect x="20" y="20" width="560" height="60" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="40" y="45" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#495057">SMS Gateway Setup - Step 1</text>
  <text x="40" y="65" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">Provider Configuration</text>

  <rect x="40" y="100" width="520" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="1"/>
  <text x="60" y="125" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1976d2">1. Choose SMS Provider:</text>
  <text x="80" y="145" font-family="Arial, sans-serif" font-size="12" fill="#1565c0">• Twilio</text>
  <text x="80" y="165" font-family="Arial, sans-serif" font-size="12" fill="#1565c0">• Nexmo/Vonage</text>
  <text x="80" y="185" font-family="Arial, sans-serif" font-size="12" fill="#1565c0">• AWS SNS</text>
  <text x="80" y="205" font-family="Arial, sans-serif" font-size="12" fill="#1565c0">• Custom Gateway</text>

  <rect x="40" y="240" width="520" height="120" fill="#f1f8e9" stroke="#4caf50" stroke-width="1"/>
  <text x="60" y="265" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2e7d32">2. Configuration Fields:</text>
  <text x="80" y="285" font-family="monospace" font-size="11" fill="#388e3c">Gateway URL: https://api.provider.com/sms</text>
  <text x="80" y="305" font-family="monospace" font-size="11" fill="#388e3c">Account SID: AC1234567890abcdef</text>
  <text x="80" y="325" font-family="monospace" font-size="11" fill="#388e3c">Auth Token: your_auth_token_here</text>
  <text x="80" y="345" font-family="monospace" font-size="11" fill="#388e3c">From Number: +1234567890</text>
</svg>