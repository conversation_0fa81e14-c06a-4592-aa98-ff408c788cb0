<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('location_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->date('analytics_date');
            
            // Daily Analytics
            $table->integer('total_locations')->default(0);
            $table->decimal('total_distance', 10, 2)->default(0); // Total distance in meters
            $table->integer('total_duration')->default(0); // Total tracking time in seconds
            $table->integer('checkin_count')->default(0);
            $table->integer('checkout_count')->default(0);
            $table->integer('auto_track_count')->default(0);
            
            // Location Patterns
            $table->decimal('avg_accuracy', 8, 2)->nullable();
            $table->decimal('max_speed', 8, 2)->nullable();
            $table->decimal('avg_speed', 8, 2)->nullable();
            $table->time('first_location_time')->nullable();
            $table->time('last_location_time')->nullable();
            
            // Geofence Analytics
            $table->integer('geofence_entries')->default(0);
            $table->integer('geofence_exits')->default(0);
            $table->integer('time_in_zones')->default(0); // Seconds spent in geofence zones
            
            // Productivity Metrics
            $table->integer('tasks_worked_on')->default(0);
            $table->integer('unique_locations')->default(0);
            $table->decimal('location_efficiency_score', 5, 2)->nullable(); // 0-100 score
            
            // Heat Map Data (aggregated by grid cells)
            $table->json('heat_map_data')->nullable(); // Grid-based location density
            $table->json('frequent_locations')->nullable(); // Top visited locations
            
            $table->timestamps();
            
            // Indexes
            $table->unique(['user_id', 'analytics_date'], 'user_date_unique');
            $table->index(['workspace_id', 'analytics_date'], 'workspace_date_index');
            $table->index(['analytics_date', 'total_distance'], 'date_distance_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('location_analytics');
    }
};
