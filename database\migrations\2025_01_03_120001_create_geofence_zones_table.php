<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('geofence_zones', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Zone Definition
            $table->string('zone_type')->default('circle'); // 'circle', 'polygon', 'rectangle'
            $table->decimal('center_latitude', 10, 7);
            $table->decimal('center_longitude', 10, 7);
            $table->decimal('radius', 8, 2)->nullable(); // For circle zones (in meters)
            $table->json('polygon_coordinates')->nullable(); // For polygon zones
            
            // Zone Configuration
            $table->boolean('is_active')->default(true);
            $table->string('zone_purpose')->default('general'); // 'office', 'client_site', 'restricted', 'general'
            $table->string('alert_type')->default('both'); // 'entry', 'exit', 'both', 'none'
            $table->integer('dwell_time_threshold')->default(300); // Seconds before dwell alert
            
            // Notification Settings
            $table->boolean('notify_managers')->default(true);
            $table->boolean('notify_admins')->default(true);
            $table->boolean('notify_user')->default(false);
            $table->json('notification_settings')->nullable();
            
            // Working Hours & Scheduling
            $table->json('active_hours')->nullable(); // When zone is active
            $table->json('active_days')->nullable(); // Days of week
            $table->boolean('respect_working_hours')->default(true);
            
            // Analytics & Reporting
            $table->integer('entry_count')->default(0);
            $table->integer('exit_count')->default(0);
            $table->timestamp('last_activity')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->spatialIndex(['center_latitude', 'center_longitude'], 'zone_center_spatial_index');
            $table->index(['workspace_id', 'is_active'], 'workspace_active_zones_index');
            $table->index(['zone_type', 'is_active'], 'zone_type_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('geofence_zones');
    }
};
