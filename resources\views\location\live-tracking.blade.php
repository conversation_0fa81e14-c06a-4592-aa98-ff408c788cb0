@extends('layout')

@section('title')
    {{ get_label('live_tracking', 'Live Tracking') }}
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="fw-bold py-3 mb-4">
                <i class="bx bx-current-location me-2"></i>
                {{ get_label('live_employee_tracking', 'Live Employee Tracking') }}
            </h4>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-primary" id="refreshLiveBtn">
                <i class="bx bx-refresh me-1"></i>
                {{ get_label('refresh', 'Refresh') }}
            </button>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
                <label class="form-check-label" for="autoRefreshToggle">
                    {{ get_label('auto_refresh', 'Auto Refresh') }}
                </label>
            </div>
        </div>
    </div>

    <!-- Live Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-success">
                                <i class="bx bx-user-check"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('active_employees', 'Active Employees') }}</span>
                            <h3 class="card-title mb-2" id="activeEmployeesCount">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-info">
                                <i class="bx bx-time"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('last_update', 'Last Update') }}</span>
                            <h6 class="card-title mb-2" id="lastUpdateTime">--</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-warning">
                                <i class="bx bx-wifi"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('connection_status', 'Connection') }}</span>
                            <h6 class="card-title mb-2" id="connectionStatus">Connecting...</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-primary">
                                <i class="bx bx-map-pin"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('total_locations', 'Total Locations') }}</span>
                            <h3 class="card-title mb-2" id="totalLocationsCount">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Map -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bx bx-map me-2"></i>
                {{ get_label('live_location_map', 'Live Location Map') }}
            </h5>
        </div>
        <div class="card-body">
            <div id="liveMap" style="height: 600px; width: 100%;"></div>
        </div>
    </div>

    <!-- Active Employees List -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bx bx-list-ul me-2"></i>
                {{ get_label('active_employees_list', 'Active Employees List') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="activeEmployeesTable">
                    <thead>
                        <tr>
                            <th>{{ get_label('employee', 'Employee') }}</th>
                            <th>{{ get_label('current_task', 'Current Task') }}</th>
                            <th>{{ get_label('location', 'Location') }}</th>
                            <th>{{ get_label('check_in_time', 'Check-in Time') }}</th>
                            <th>{{ get_label('duration', 'Duration') }}</th>
                            <th>{{ get_label('actions', 'Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody id="activeEmployeesBody">
                        <!-- Dynamic content -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Include Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://js.pusher.com/7.2.0/pusher.min.js"></script>
<script src="{{ asset('assets/js/location-realtime.js') }}"></script>

<script>
let liveMap;
let liveMarkers = {};
let autoRefreshInterval;
let currentData = [];

$(document).ready(function() {
    // Initialize map
    initializeLiveMap();
    
    // Load initial data
    loadLiveData();
    
    // Set up auto refresh
    setupAutoRefresh();
    
    // Set up real-time updates
    setupRealTimeUpdates();
    
    // Event handlers
    $('#refreshLiveBtn').on('click', function() {
        loadLiveData();
    });
    
    $('#autoRefreshToggle').on('change', function() {
        if ($(this).is(':checked')) {
            setupAutoRefresh();
        } else {
            clearInterval(autoRefreshInterval);
        }
    });
});

function initializeLiveMap() {
    liveMap = L.map('liveMap').setView([0, 0], 2);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(liveMap);
}

function loadLiveData() {
    $.ajax({
        url: '{{ route('location.live.data') }}',
        method: 'GET',
        success: function(response) {
            if (response.error === false) {
                currentData = response.data;
                updateLiveMap(response.data);
                updateActiveEmployeesList(response.data);
                updateStatistics(response.data);
                $('#lastUpdateTime').text(new Date().toLocaleTimeString());
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            toastr.error('Failed to load live tracking data');
        }
    });
}

function updateLiveMap(data) {
    // Clear existing markers
    Object.values(liveMarkers).forEach(marker => {
        liveMap.removeLayer(marker);
    });
    liveMarkers = {};
    
    if (data.length === 0) {
        return;
    }
    
    // Add new markers
    data.forEach(location => {
        addLiveMarker(location);
    });
    
    // Fit map to show all markers
    if (data.length > 0) {
        const group = new L.featureGroup(Object.values(liveMarkers));
        liveMap.fitBounds(group.getBounds().pad(0.1));
    }
}

function addLiveMarker(location) {
    const marker = L.circleMarker([location.lat, location.lng], {
        radius: 10,
        fillColor: '#28a745',
        color: '#fff',
        weight: 3,
        opacity: 1,
        fillOpacity: 0.8
    });
    
    const popupContent = `
        <div class="p-2">
            <h6 class="mb-2">${location.user}</h6>
            <p class="mb-1"><strong>Task:</strong> ${location.task}</p>
            <p class="mb-1"><strong>Check-in:</strong> ${location.time}</p>
            <p class="mb-1"><strong>Duration:</strong> ${location.duration}</p>
            <p class="mb-0"><strong>Location:</strong> ${location.coordinates}</p>
        </div>
    `;
    
    marker.bindPopup(popupContent);
    marker.addTo(liveMap);
    
    // Store marker reference
    liveMarkers[location.user_id] = marker;
}

function updateActiveEmployeesList(data) {
    const tbody = $('#activeEmployeesBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted">
                    <i class="bx bx-info-circle me-1"></i>
                    No active employees found
                </td>
            </tr>
        `);
        return;
    }
    
    data.forEach(employee => {
        tbody.append(`
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-sm me-2">
                            <span class="avatar-initial rounded-circle bg-success">
                                <i class="bx bx-user"></i>
                            </span>
                        </div>
                        ${employee.user}
                    </div>
                </td>
                <td>${employee.task}</td>
                <td>
                    <small class="text-muted">${employee.coordinates}</small>
                </td>
                <td>
                    <span class="badge bg-success">${employee.time}</span>
                </td>
                <td>${employee.duration}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="focusOnEmployee(${employee.lat}, ${employee.lng})">
                        <i class="bx bx-map-pin"></i> View
                    </button>
                </td>
            </tr>
        `);
    });
}

function updateStatistics(data) {
    $('#activeEmployeesCount').text(data.length);
    $('#totalLocationsCount').text(data.length);
}

function setupAutoRefresh() {
    clearInterval(autoRefreshInterval);
    autoRefreshInterval = setInterval(function() {
        if ($('#autoRefreshToggle').is(':checked')) {
            loadLiveData();
        }
    }, 30000); // Refresh every 30 seconds
}

function setupRealTimeUpdates() {
    // Initialize real-time location tracking
    const locationRealtime = new LocationRealtime({{ session('workspace_id') }}, {
        key: '{{ config('chatify.pusher.key') }}',
        cluster: '{{ config('chatify.pusher.options.cluster') }}',
        encrypted: {{ config('chatify.pusher.options.encrypted') ? 'true' : 'false' }},
        authEndpoint: '/broadcasting/auth'
    });

    // Handle real-time location updates
    locationRealtime.onLocationCaptured(function(data) {
        // Refresh live data when new location is captured
        loadLiveData();
        
        // Show notification
        if (typeof toastr !== 'undefined') {
            toastr.info(`${data.user_name} location updated`, 'Live Update');
        }
    });

    // Handle connection status
    locationRealtime.onConnectionStateChange(function(states) {
        const statusElement = $('#connectionStatus');
        if (states.current === 'connected') {
            statusElement.text('Connected').removeClass('text-danger').addClass('text-success');
        } else if (states.current === 'disconnected') {
            statusElement.text('Disconnected').removeClass('text-success').addClass('text-danger');
        } else {
            statusElement.text('Connecting...').removeClass('text-success text-danger');
        }
    });
}

function focusOnEmployee(lat, lng) {
    liveMap.setView([lat, lng], 15);
    
    // Find and open the marker popup
    Object.values(liveMarkers).forEach(marker => {
        const markerLatLng = marker.getLatLng();
        if (Math.abs(markerLatLng.lat - lat) < 0.0001 && Math.abs(markerLatLng.lng - lng) < 0.0001) {
            marker.openPopup();
        }
    });
}
</script>
@endsection
