<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">

  <rect width="100%" height="100%" fill="#ffffff" stroke="#e9ecef" stroke-width="2"/>
  <rect x="20" y="20" width="560" height="60" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="40" y="45" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#495057">SMS Gateway Setup - Step 2</text>
  <text x="40" y="65" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">Testing and Validation</text>

  <rect x="40" y="100" width="520" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="1"/>
  <text x="60" y="125" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#e65100">3. Test Configuration:</text>
  <text x="80" y="145" font-family="Arial, sans-serif" font-size="12" fill="#bf360c">• Send test SMS to verify setup</text>
  <text x="80" y="165" font-family="Arial, sans-serif" font-size="12" fill="#bf360c">• Check delivery status</text>
  <text x="80" y="185" font-family="Arial, sans-serif" font-size="12" fill="#bf360c">• Validate phone number format</text>

  <rect x="40" y="220" width="520" height="140" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
  <text x="60" y="245" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6a1b9a">4. Sample Code:</text>
  <text x="80" y="265" font-family="monospace" font-size="10" fill="#7b1fa2">$sms = new SMSGateway();</text>
  <text x="80" y="285" font-family="monospace" font-size="10" fill="#7b1fa2">$sms->setCredentials($sid, $token);</text>
  <text x="80" y="305" font-family="monospace" font-size="10" fill="#7b1fa2">$sms->send($to, $message);</text>
  <text x="80" y="325" font-family="monospace" font-size="10" fill="#7b1fa2">$status = $sms->getDeliveryStatus();</text>
  <text x="80" y="345" font-family="monospace" font-size="10" fill="#7b1fa2">echo "SMS sent: " . $status;</text>
</svg>