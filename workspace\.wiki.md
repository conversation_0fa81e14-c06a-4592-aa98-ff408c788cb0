```markdown
# Project Summary
NestKo is a modern AI chatbot builder platform designed to enhance customer support for businesses through a versatile React-based chatbot widget. The project aims to deliver an interactive, engaging chatbot experience that integrates seamlessly with NestKo's services, providing users with advanced AI capabilities, responsive design, and a production-ready solution.

# Project Module Description
The primary module is the NestKo Chat Widget, functioning as a floating customer support assistant on websites. Key features include:
- A fully functional React component ensuring UI/UX design consistency.
- Utilization of Tailwind CSS for styling and modern React patterns for functionality.
- Integration with OpenRouter's API for real-time communication, featuring smooth animations and transitions.
- Three states: closed, chat, and minimized, allowing for a flexible user experience.
- Enhanced features including a three-dots menu for "New Chat" and "PDF Download", an AI-powered recommendation system, and complete NestKo.in knowledge integration.

# Directory Tree
```
.
├── src
│   ├── components
│   │   ├── NestKoChatWidget.tsx      # Main widget component
│   │   ├── chat
│   │   │   ├── ClosedWidget.tsx      # Landing page state
│   │   │   ├── ChatInterface.tsx     # Full chat interface
│   │   │   ├── FloatingButton.tsx    # Minimized state
│   │   │   ├── MessageBubble.tsx     # Individual messages
│   │   │   ├── QuickReplyButtons.tsx # Quick action buttons
│   │   │   ├── QuickReplyButton.tsx  # Single button component
│   │   │   ├── QuickActionButton.tsx # Action item buttons
│   │   │   └── TypingIndicator.tsx   # Loading animation
│   │   ├── hooks
│   │   │   └── useChat.ts            # Chat logic and API calls
│   │   └── types
│   │       └── chat.ts               # TypeScript definitions
│   ├── pages
│   │   └── Index.tsx                  # Main entry point for the application
├── index.html                          # HTML file for the application
└── README-Integration.md               # Integration guide for the widget
```

# File Description Inventory
- **NestKoChatWidget.tsx**: The main React component encapsulating the chatbot widget functionality.
- **ClosedWidget.tsx**: Component for the landing page state of the widget.
- **ChatInterface.tsx**: Component for the full chat interface.
- **FloatingButton.tsx**: Component for the minimized state of the widget.
- **MessageBubble.tsx**: Component for rendering individual messages.
- **QuickReplyButtons.tsx**: Component for displaying quick action buttons.
- **QuickReplyButton.tsx**: Component for a single quick reply button.
- **QuickActionButton.tsx**: Component for action item buttons.
- **TypingIndicator.tsx**: Component for displaying a typing animation.
- **useChat.ts**: Custom hook for managing chat state and API interactions.
- **chat.ts**: TypeScript definitions for chat-related types.
- **Index.tsx**: Main entry point for the application.
- **index.html**: HTML file for the application.
- **README-Integration.md**: Integration guide for the widget.

# Technology Stack
- React
- TypeScript (optional)
- Tailwind CSS
- OpenRouter API

# Usage
1. Clone the repository and navigate to the project directory.
2. Install dependencies:
   ```bash
   pnpm install
   ```
3. Build the project:
   ```bash
   pnpm run build
   ```
4. Deploy the `dist` folder to your Hostinger public_html directory.
5. Replace the OpenRouter API key in the code with your actual key.
6. The widget will be live on your website.
