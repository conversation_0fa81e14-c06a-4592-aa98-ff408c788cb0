<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">

  <defs>
    <linearGradient id="clockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffc107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0a800;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="64" cy="64" r="60" fill="url(#clockGradient)" stroke="#d39e00" stroke-width="4"/>
  <circle cx="64" cy="64" r="50" fill="white" stroke="#d39e00" stroke-width="2"/>
  <line x1="64" y1="64" x2="64" y2="30" stroke="#333" stroke-width="3" stroke-linecap="round"/>
  <line x1="64" y1="64" x2="85" y2="64" stroke="#333" stroke-width="2" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="4" fill="#333"/>
  <text x="64" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">12</text>
  <text x="100" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">3</text>
  <text x="64" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">6</text>
  <text x="28" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#333">9</text>
</svg>