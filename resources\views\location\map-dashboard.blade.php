@extends('layout')
@section('title')
    {{ get_label('location_map', 'Location Map') }}
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-style1">
                    <li class="breadcrumb-item">
                        <a href="{{ route('home.index') }}">{{ get_label('home', 'Home') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ get_label('location_map', 'Location Map') }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bx bx-map-pin me-2"></i>
                    {{ get_label('employee_location_map', 'Employee Location Map') }}
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" id="exportMapCsvBtn">
                        <i class="bx bx-download me-1"></i>
                        {{ get_label('export_csv', 'Export CSV') }}
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="toggleFiltersBtn">
                        <i class="bx bx-filter me-1"></i>
                        {{ get_label('toggle_filters', 'Toggle Filters') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="refreshMapBtn">
                        <i class="bx bx-refresh me-1"></i>
                        {{ get_label('refresh', 'Refresh') }}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card-body">
            <!-- Filters Panel -->
            <div id="filtersPanel" class="row mb-4" style="display: none;">
                <div class="col-md-3">
                    <label for="userFilter" class="form-label">{{ get_label('user', 'User') }}</label>
                    <select class="form-select" id="userFilter">
                        <option value="">{{ get_label('all_users', 'All Users') }}</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->first_name }} {{ $user->last_name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="actionFilter" class="form-label">{{ get_label('action', 'Action') }}</label>
                    <select class="form-select" id="actionFilter">
                        <option value="">{{ get_label('all_actions', 'All Actions') }}</option>
                        @foreach($actions as $action)
                            <option value="{{ $action }}">{{ ucfirst(str_replace('_', ' ', $action)) }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="startDate" class="form-label">{{ get_label('start_date', 'Start Date') }}</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-2">
                    <label for="endDate" class="form-label">{{ get_label('end_date', 'End Date') }}</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-primary w-100" id="applyMapFiltersBtn">
                        <i class="bx bx-filter me-1"></i>
                        {{ get_label('apply', 'Apply') }}
                    </button>
                </div>
            </div>

            <!-- Map Container -->
            <div id="locationMap" style="height: 600px; width: 100%; border-radius: 8px;"></div>
            
            <!-- Map Legend -->
            <div class="mt-3">
                <div class="d-flex flex-wrap gap-3">
                    <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 20px; height: 20px; background-color: #28a745; border-radius: 50%;"></div>
                        <small>{{ get_label('check_in', 'Check In') }}</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 20px; height: 20px; background-color: #ffc107; border-radius: 50%;"></div>
                        <small>{{ get_label('check_out', 'Check Out') }}</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 20px; height: 20px; background-color: #17a2b8; border-radius: 50%;"></div>
                        <small>{{ get_label('task_created', 'Task Created') }}</small>
                    </div>
                </div>
            </div>
            
            <!-- Map Statistics -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1" id="totalLocations">0</h4>
                            <small>{{ get_label('total_locations', 'Total Locations') }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1" id="checkInCount">0</h4>
                            <small>{{ get_label('check_ins', 'Check Ins') }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1" id="checkOutCount">0</h4>
                            <small>{{ get_label('check_outs', 'Check Outs') }}</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1" id="taskCreatedCount">0</h4>
                            <small>{{ get_label('tasks_created', 'Tasks Created') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
<script src="https://js.pusher.com/7.2.0/pusher.min.js"></script>
<script src="{{ asset('assets/js/location-realtime.js') }}"></script>

<script>
let map;
let markersCluster;
let currentData = [];

$(document).ready(function() {
    initializeMap();
    loadMapData();
    
    // Event listeners
    $('#toggleFiltersBtn').on('click', function() {
        $('#filtersPanel').toggle();
    });
    
    $('#applyMapFiltersBtn').on('click', function() {
        loadMapData();
    });
    
    $('#refreshMapBtn').on('click', function() {
        loadMapData();
    });

    // Export CSV from map
    $('#exportMapCsvBtn').on('click', function() {
        const params = {
            user_id: $('#userFilter').val(),
            action: $('#actionFilter').val(),
            start_date: $('#startDate').val(),
            end_date: $('#endDate').val(),
            export: 'csv'
        };
        const queryString = $.param(params);
        window.open(`{{ route('location.map.data') }}?${queryString}`, '_blank');
    });

    // Initialize real-time location tracking
    const locationRealtime = new LocationRealtime({{ session('workspace_id') }}, {
        key: '{{ config('chatify.pusher.key') }}',
        cluster: '{{ config('chatify.pusher.options.cluster') }}',
        encrypted: {{ config('chatify.pusher.options.encrypted') ? 'true' : 'false' }},
        authEndpoint: '/broadcasting/auth'
    });

    // Handle real-time location updates
    locationRealtime.onLocationCaptured(function(data) {
        // Show notification
        if (typeof toastr !== 'undefined') {
            toastr.info(`New location captured: ${data.user_name} - ${data.action_label}`, 'Real-time Update');
        }

        // Add new marker to map
        addRealtimeMarker(data);

        // Update statistics
        updateStatistics();
    });

    // Handle connection status
    locationRealtime.onConnectionStateChange(function(states) {
        if (states.current === 'connected') {
            console.log('Real-time location tracking connected');
        } else if (states.current === 'disconnected') {
            console.log('Real-time location tracking disconnected');
        }
    });
});

function initializeMap() {
    // Initialize map centered on a default location
    map = L.map('locationMap').setView([40.7128, -74.0060], 10); // Default to NYC
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Initialize marker cluster group
    markersCluster = L.markerClusterGroup({
        chunkedLoading: true,
        maxClusterRadius: 50
    });
    
    map.addLayer(markersCluster);
}

function loadMapData() {
    const params = {
        user_id: $('#userFilter').val(),
        action: $('#actionFilter').val(),
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val()
    };
    
    $.ajax({
        url: '{{ route("location.map.data") }}',
        type: 'GET',
        data: params,
        success: function(response) {
            if (response.error === false) {
                currentData = response.data;
                updateMapMarkers(response.data);
                updateStatistics(response.data);
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            toastr.error('Failed to load map data');
        }
    });
}

function updateMapMarkers(data) {
    // Clear existing markers
    markersCluster.clearLayers();
    
    if (data.length === 0) {
        return;
    }
    
    // Add new markers
    data.forEach(function(location) {
        const marker = createMarker(location);
        markersCluster.addLayer(marker);
    });
    
    // Fit map to show all markers
    if (markersCluster.getLayers().length > 0) {
        map.fitBounds(markersCluster.getBounds(), { padding: [20, 20] });
    }
}

function createMarker(location) {
    const color = getMarkerColor(location.action_raw);
    
    const marker = L.circleMarker([location.lat, location.lng], {
        radius: 8,
        fillColor: color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    });
    
    const popupContent = `
        <div class="p-2">
            <h6 class="mb-2">${location.user}</h6>
            <p class="mb-1"><strong>Task:</strong> ${location.task}</p>
            <p class="mb-1"><strong>Action:</strong> <span class="badge bg-${getActionBootstrapColor(location.action_raw)}">${location.action}</span></p>
            <p class="mb-1"><strong>Time:</strong> ${location.date} at ${location.time}</p>
            <p class="mb-1"><strong>Coordinates:</strong> ${location.coordinates}</p>
            <div class="mt-2">
                <a href="#" onclick="openInGoogleMaps(${location.lat}, ${location.lng})" class="btn btn-sm btn-outline-primary">
                    <i class="bx bx-map"></i> Google Maps
                </a>
            </div>
        </div>
    `;
    
    marker.bindPopup(popupContent);
    
    return marker;
}

function getMarkerColor(action) {
    switch(action) {
        case 'checkin': return '#28a745';
        case 'checkout': return '#ffc107';
        case 'task_created': return '#17a2b8';
        default: return '#6c757d';
    }
}

function getActionBootstrapColor(action) {
    switch(action) {
        case 'checkin': return 'success';
        case 'checkout': return 'warning';
        case 'task_created': return 'info';
        default: return 'secondary';
    }
}

function updateStatistics(data) {
    const stats = {
        total: data.length,
        checkin: data.filter(l => l.action_raw === 'checkin').length,
        checkout: data.filter(l => l.action_raw === 'checkout').length,
        task_created: data.filter(l => l.action_raw === 'task_created').length
    };
    
    $('#totalLocations').text(stats.total);
    $('#checkInCount').text(stats.checkin);
    $('#checkOutCount').text(stats.checkout);
    $('#taskCreatedCount').text(stats.task_created);
}

function openInGoogleMaps(lat, lng) {
    const url = `https://www.google.com/maps?q=${lat},${lng}`;
    window.open(url, '_blank');
}

function addRealtimeMarker(data) {
    // Add to current data
    currentData.push(data);

    // Create marker
    const color = getMarkerColor(data.action);
    const marker = L.circleMarker([data.latitude, data.longitude], {
        radius: 8,
        fillColor: color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    });

    // Create popup content
    const popupContent = `
        <div class="p-2">
            <h6 class="mb-2">${data.user_name}</h6>
            <p class="mb-1"><strong>Task:</strong> ${data.task_title}</p>
            <p class="mb-1"><strong>Action:</strong> <span class="badge bg-${getActionBootstrapColor(data.action)}">${data.action_label}</span></p>
            <p class="mb-1"><strong>Time:</strong> ${data.time}</p>
            <p class="mb-0"><strong>Coordinates:</strong> ${data.coordinates}</p>
        </div>
    `;

    marker.bindPopup(popupContent);

    // Add to cluster group
    markersCluster.addLayer(marker);

    // Animate marker (pulse effect)
    marker.setStyle({
        radius: 12
    });
    setTimeout(() => {
        marker.setStyle({
            radius: 8
        });
    }, 1000);
}

function getActionBootstrapColor(action) {
    switch(action) {
        case 'checkin': return 'success';
        case 'checkout': return 'warning';
        case 'task_created': return 'info';
        default: return 'secondary';
    }
}
</script>
@endsection
