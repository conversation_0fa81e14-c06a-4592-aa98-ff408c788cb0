@extends('layouts.app')

@section('title', 'Geofence Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-shield-alt text-primary me-2"></i>
                    Geofence Management
                </h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createZoneModal">
                    <i class="fas fa-plus me-1"></i>
                    Create Zone
                </button>
            </div>
        </div>
    </div>

    <!-- Map and Zones -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map me-2"></i>
                        Geofence Zones Map
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="geofenceMap" style="height: 500px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Active Zones
                    </h5>
                </div>
                <div class="card-body">
                    <div id="zonesList">
                        <!-- Zones will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Zones Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        All Geofence Zones
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="zonesTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Purpose</th>
                                    <th>Status</th>
                                    <th>Activity</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="zonesTableBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Zone Modal -->
<div class="modal fade" id="createZoneModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Geofence Zone</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="zoneForm">
                    <input type="hidden" id="zoneId" name="zone_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="zoneName" class="form-label">Zone Name *</label>
                                <input type="text" class="form-control" id="zoneName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="zoneType" class="form-label">Zone Type *</label>
                                <select class="form-select" id="zoneType" name="zone_type" required>
                                    <option value="circle">Circle</option>
                                    <option value="polygon">Polygon</option>
                                    <option value="rectangle">Rectangle</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="zoneDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="zoneDescription" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="zonePurpose" class="form-label">Purpose *</label>
                                <select class="form-select" id="zonePurpose" name="zone_purpose" required>
                                    <option value="office">Office</option>
                                    <option value="client_site">Client Site</option>
                                    <option value="restricted">Restricted Area</option>
                                    <option value="general">General</option>
                                    <option value="parking">Parking</option>
                                    <option value="meeting_point">Meeting Point</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="alertType" class="form-label">Alert Type *</label>
                                <select class="form-select" id="alertType" name="alert_type" required>
                                    <option value="entry">Entry Only</option>
                                    <option value="exit">Exit Only</option>
                                    <option value="both">Entry & Exit</option>
                                    <option value="dwell">Dwell Time</option>
                                    <option value="none">No Alerts</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="centerLatitude" class="form-label">Center Latitude *</label>
                                <input type="number" class="form-control" id="centerLatitude" name="center_latitude" step="any" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="centerLongitude" class="form-label">Center Longitude *</label>
                                <input type="number" class="form-control" id="centerLongitude" name="center_longitude" step="any" required>
                            </div>
                        </div>
                    </div>
                    
                    <div id="radiusField" class="mb-3">
                        <label for="radius" class="form-label">Radius (meters) *</label>
                        <input type="number" class="form-control" id="radius" name="radius" min="1">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifyManagers" name="notify_managers">
                                <label class="form-check-label" for="notifyManagers">
                                    Notify Managers
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifyAdmins" name="notify_admins">
                                <label class="form-check-label" for="notifyAdmins">
                                    Notify Admins
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifyUser" name="notify_user">
                                <label class="form-check-label" for="notifyUser">
                                    Notify User
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveZone()">Save Zone</button>
            </div>
        </div>
    </div>
</div>

<!-- Zone Details Modal -->
<div class="modal fade" id="zoneDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Zone Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="zoneDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
    .zone-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .zone-item:hover {
        background-color: #f8f9fa;
        border-color: #0d6efd;
    }
    
    .zone-item.active {
        background-color: #e7f3ff;
        border-color: #0d6efd;
    }
    
    .zone-status {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .zone-status.active {
        color: #198754;
    }
    
    .zone-status.inactive {
        color: #dc3545;
    }
</style>
@endpush

@push('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
let geofenceMap;
let zones = [];
let zoneMarkers = [];
let selectedZone = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    loadZones();
    
    // Zone type change handler
    document.getElementById('zoneType').addEventListener('change', function() {
        toggleRadiusField();
    });
});

function initializeMap() {
    geofenceMap = L.map('geofenceMap').setView([{{ config('app.default_latitude', 40.7128) }}, {{ config('app.default_longitude', -74.0060) }}], 10);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(geofenceMap);
    
    // Map click handler for setting zone center
    geofenceMap.on('click', function(e) {
        if (document.getElementById('createZoneModal').classList.contains('show')) {
            document.getElementById('centerLatitude').value = e.latlng.lat.toFixed(6);
            document.getElementById('centerLongitude').value = e.latlng.lng.toFixed(6);
        }
    });
}

function loadZones() {
    fetch('/geofence/zones')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.message);
            }
            
            zones = data.zones;
            updateZonesDisplay();
            updateZonesTable();
            updateZonesMap();
        })
        .catch(error => {
            console.error('Error loading zones:', error);
            alert('Failed to load geofence zones: ' + error.message);
        });
}

function updateZonesDisplay() {
    const zonesList = document.getElementById('zonesList');
    zonesList.innerHTML = '';
    
    zones.forEach(zone => {
        const zoneItem = document.createElement('div');
        zoneItem.className = 'zone-item';
        zoneItem.onclick = () => selectZone(zone);
        
        zoneItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6 class="mb-1">${zone.name}</h6>
                    <small class="text-muted">${zone.formatted_description}</small>
                </div>
                <span class="zone-status ${zone.is_active ? 'active' : 'inactive'}">
                    ${zone.is_active ? 'Active' : 'Inactive'}
                </span>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    Entries: ${zone.entry_count} | Exits: ${zone.exit_count}
                </small>
            </div>
        `;
        
        zonesList.appendChild(zoneItem);
    });
}

function updateZonesTable() {
    const tbody = document.getElementById('zonesTableBody');
    tbody.innerHTML = '';
    
    zones.forEach(zone => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${zone.name}</td>
            <td><span class="badge bg-info">${zone.zone_type}</span></td>
            <td><span class="badge bg-secondary">${zone.zone_purpose.replace('_', ' ')}</span></td>
            <td>
                <span class="badge ${zone.is_active ? 'bg-success' : 'bg-danger'}">
                    ${zone.is_active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td>
                <small>
                    <i class="fas fa-sign-in-alt text-success"></i> ${zone.entry_count}
                    <i class="fas fa-sign-out-alt text-danger ms-2"></i> ${zone.exit_count}
                </small>
            </td>
            <td><small>${zone.created_by}</small></td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editZone(${zone.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewZoneDetails(${zone.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-${zone.is_active ? 'warning' : 'success'}" onclick="toggleZoneStatus(${zone.id})">
                        <i class="fas fa-${zone.is_active ? 'pause' : 'play'}"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteZone(${zone.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
    });
}

function updateZonesMap() {
    // Clear existing markers
    zoneMarkers.forEach(marker => geofenceMap.removeLayer(marker));
    zoneMarkers = [];
    
    zones.forEach(zone => {
        if (zone.zone_type === 'circle') {
            const circle = L.circle([zone.center_latitude, zone.center_longitude], {
                radius: zone.radius,
                color: zone.is_active ? '#28a745' : '#dc3545',
                fillColor: zone.is_active ? '#28a745' : '#dc3545',
                fillOpacity: 0.2
            }).addTo(geofenceMap);
            
            circle.bindPopup(`
                <strong>${zone.name}</strong><br>
                Type: ${zone.zone_type}<br>
                Purpose: ${zone.zone_purpose}<br>
                Radius: ${zone.radius}m<br>
                Status: ${zone.is_active ? 'Active' : 'Inactive'}
            `);
            
            zoneMarkers.push(circle);
        }
        // Add polygon and rectangle support here if needed
    });
    
    // Fit map to show all zones
    if (zoneMarkers.length > 0) {
        const group = new L.featureGroup(zoneMarkers);
        geofenceMap.fitBounds(group.getBounds().pad(0.1));
    }
}

function selectZone(zone) {
    selectedZone = zone;
    
    // Update UI to show selected zone
    document.querySelectorAll('.zone-item').forEach(item => {
        item.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    // Center map on selected zone
    geofenceMap.setView([zone.center_latitude, zone.center_longitude], 15);
}

function toggleRadiusField() {
    const zoneType = document.getElementById('zoneType').value;
    const radiusField = document.getElementById('radiusField');
    
    if (zoneType === 'circle') {
        radiusField.style.display = 'block';
        document.getElementById('radius').required = true;
    } else {
        radiusField.style.display = 'none';
        document.getElementById('radius').required = false;
    }
}

function saveZone() {
    const form = document.getElementById('zoneForm');
    const formData = new FormData(form);
    const zoneId = document.getElementById('zoneId').value;
    
    const url = zoneId ? `/geofence/zones/${zoneId}` : '/geofence/zones';
    const method = zoneId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.message);
        }
        
        alert(data.message);
        bootstrap.Modal.getInstance(document.getElementById('createZoneModal')).hide();
        form.reset();
        loadZones();
    })
    .catch(error => {
        console.error('Error saving zone:', error);
        alert('Failed to save zone: ' + error.message);
    });
}

function editZone(zoneId) {
    const zone = zones.find(z => z.id === zoneId);
    if (!zone) return;
    
    // Populate form with zone data
    document.getElementById('zoneId').value = zone.id;
    document.getElementById('zoneName').value = zone.name;
    document.getElementById('zoneDescription').value = zone.description || '';
    document.getElementById('zoneType').value = zone.zone_type;
    document.getElementById('zonePurpose').value = zone.zone_purpose;
    document.getElementById('alertType').value = zone.alert_type;
    document.getElementById('centerLatitude').value = zone.center_latitude;
    document.getElementById('centerLongitude').value = zone.center_longitude;
    document.getElementById('radius').value = zone.radius || '';
    document.getElementById('notifyManagers').checked = zone.notify_managers;
    document.getElementById('notifyAdmins').checked = zone.notify_admins;
    document.getElementById('notifyUser').checked = zone.notify_user;
    
    toggleRadiusField();
    
    document.querySelector('#createZoneModal .modal-title').textContent = 'Edit Geofence Zone';
    new bootstrap.Modal(document.getElementById('createZoneModal')).show();
}

function viewZoneDetails(zoneId) {
    const zone = zones.find(z => z.id === zoneId);
    if (!zone) return;
    
    document.getElementById('zoneDetailsContent').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>Name:</strong> ${zone.name}<br>
                <strong>Type:</strong> ${zone.zone_type}<br>
                <strong>Purpose:</strong> ${zone.zone_purpose}<br>
                <strong>Alert Type:</strong> ${zone.alert_type}<br>
            </div>
            <div class="col-md-6">
                <strong>Status:</strong> ${zone.is_active ? 'Active' : 'Inactive'}<br>
                <strong>Entries:</strong> ${zone.entry_count}<br>
                <strong>Exits:</strong> ${zone.exit_count}<br>
                <strong>Last Activity:</strong> ${zone.last_activity || 'Never'}<br>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <strong>Description:</strong><br>
                ${zone.description || 'No description provided'}
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-12">
                <strong>Coordinates:</strong> ${zone.center_coordinates}<br>
                ${zone.radius ? `<strong>Radius:</strong> ${zone.radius} meters<br>` : ''}
                <strong>Created By:</strong> ${zone.created_by}<br>
                <strong>Created:</strong> ${zone.created_at}
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('zoneDetailsModal')).show();
}

function toggleZoneStatus(zoneId) {
    fetch(`/geofence/zones/${zoneId}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.message);
        }
        
        alert(data.message);
        loadZones();
    })
    .catch(error => {
        console.error('Error toggling zone status:', error);
        alert('Failed to update zone status: ' + error.message);
    });
}

function deleteZone(zoneId) {
    if (!confirm('Are you sure you want to delete this geofence zone?')) {
        return;
    }
    
    fetch(`/geofence/zones/${zoneId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.message);
        }
        
        alert(data.message);
        loadZones();
    })
    .catch(error => {
        console.error('Error deleting zone:', error);
        alert('Failed to delete zone: ' + error.message);
    });
}

// Reset form when modal is hidden
document.getElementById('createZoneModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('zoneForm').reset();
    document.getElementById('zoneId').value = '';
    document.querySelector('#createZoneModal .modal-title').textContent = 'Create Geofence Zone';
    toggleRadiusField();
});
</script>
@endpush
