{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "anhskohbo/no-captcha": "*", "aws/aws-sdk-php": "^3.296", "guzzlehttp/guzzle": "^7.2", "ixudra/curl": "^6.22", "knuckleswtf/scribe": "^5.2", "ladumor/laravel-pwa": "^0.0.4", "laminas/laminas-hydrator": "^2.0", "laravel/framework": "^10.0", "laravel/sanctum": "^3.0", "laravel/scout": "^10.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.2", "laraveldaily/laravel-invoices": "^3.3", "league/flysystem-aws-s3-v3": "^3.22", "maatwebsite/excel": "^3.1", "munafio/chatify": "^1.6", "netflie/whatsapp-cloud-api": "^2.2", "protonemedia/laravel-cross-eloquent-search": "^3.1", "psr/container": "^2.0", "ryangjchandler/laravel-comments": "^0.2.0", "silviolleite/laravelpwa": "*", "spatie/laravel-medialibrary": "^10.15", "spatie/laravel-permission": "^5.9", "srmklive/paypal": "^3.0", "stripe/stripe-php": "^13.13", "twilio/sdk": "^8.2", "unicodeveloper/laravel-paystack": "^1.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"exclude-from-classmap": ["vendor/munafio/chatify/src/ChatifyMessenger.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/app_helpers.php", "app/Overrides/ChatifyMessenger.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}