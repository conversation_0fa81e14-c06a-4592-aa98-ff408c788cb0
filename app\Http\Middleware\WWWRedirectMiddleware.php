<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

class WWWRedirectMiddleware
{
    /**
     * Handle an incoming request.
     * 
     * This middleware ensures consistent URL canonicalization by redirecting
     * between www and non-www versions of the site based on configuration.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip redirect for local development, API routes, and AJAX requests
        if ($this->shouldSkipRedirect($request)) {
            return $next($request);
        }

        $host = $request->getHost();
        $scheme = $request->getScheme();
        $forceWWW = config('app.force_www', false);
        
        // Check if we need to redirect
        $needsRedirect = false;
        $newHost = $host;
        
        if ($forceWWW && !$this->hasWWW($host)) {
            // Force WWW: redirect non-www to www
            $needsRedirect = true;
            $newHost = 'www.' . $host;
        } elseif (!$forceWWW && $this->hasWWW($host)) {
            // Force non-WWW: redirect www to non-www
            $needsRedirect = true;
            $newHost = $this->removeWWW($host);
        }
        
        if ($needsRedirect) {
            $newUrl = $scheme . '://' . $newHost . $request->getRequestUri();
            return redirect($newUrl, 301);
        }
        
        // Update APP_URL dynamically to match current request
        $this->updateAppUrl($request);
        
        return $next($request);
    }
    
    /**
     * Check if redirect should be skipped
     */
    private function shouldSkipRedirect(Request $request): bool
    {
        $host = $request->getHost();
        
        // Skip for localhost and local development
        if (in_array($host, ['localhost', '127.0.0.1']) || 
            str_contains($host, '.local') || 
            str_contains($host, '.test') ||
            str_contains($host, '.dev')) {
            return true;
        }
        
        // Skip for API routes
        if ($request->is('api/*')) {
            return true;
        }
        
        // Skip for AJAX requests
        if ($request->ajax()) {
            return true;
        }
        
        // Skip for asset requests
        if ($request->is('assets/*') || 
            $request->is('storage/*') || 
            $request->is('css/*') || 
            $request->is('js/*') || 
            $request->is('images/*')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if host has www prefix
     */
    private function hasWWW(string $host): bool
    {
        return str_starts_with(strtolower($host), 'www.');
    }
    
    /**
     * Remove www prefix from host
     */
    private function removeWWW(string $host): string
    {
        if ($this->hasWWW($host)) {
            return substr($host, 4);
        }
        return $host;
    }
    
    /**
     * Update APP_URL configuration to match current request
     * This ensures asset() function generates correct URLs
     */
    private function updateAppUrl(Request $request): void
    {
        $currentUrl = $request->getScheme() . '://' . $request->getHost();
        
        // Only update if different from current config
        if (config('app.url') !== $currentUrl) {
            Config::set('app.url', $currentUrl);
            
            // Also update asset URL if it's not explicitly set
            if (!config('app.asset_url')) {
                Config::set('app.asset_url', $currentUrl);
            }
        }
    }
}
