@extends('layouts.app')

@section('title', 'Location Heat Map')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-fire text-danger me-2"></i>
                        Location Heat Map
                    </h4>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#filtersModal">
                            <i class="fas fa-filter me-1"></i>
                            Filters
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="exportHeatMap()">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Map Container -->
                    <div id="heatMapContainer" style="height: 600px; position: relative;">
                        <div id="heatMap" style="height: 100%; width: 100%;"></div>
                        
                        <!-- Loading Overlay -->
                        <div id="mapLoading" class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-light bg-opacity-75" style="z-index: 1000;">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status"></div>
                                <div>Loading heat map data...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Locations</h6>
                            <h3 id="totalLocations">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Hot Spots</h6>
                            <h3 id="hotSpots">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-fire fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Date Range</h6>
                            <h6 id="dateRange">-</h6>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Max Intensity</h6>
                            <h3 id="maxIntensity">-</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-thermometer-full fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Modal -->
<div class="modal fade" id="filtersModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Heat Map Filters</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="filtersForm">
                    <div class="mb-3">
                        <label for="startDate" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="startDate" name="start_date">
                    </div>
                    <div class="mb-3">
                        <label for="endDate" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="endDate" name="end_date">
                    </div>
                    <div class="mb-3">
                        <label for="userId" class="form-label">Employee</label>
                        <select class="form-select" id="userId" name="user_id">
                            <option value="">All Employees</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="intensityRadius" class="form-label">Heat Radius</label>
                        <input type="range" class="form-range" id="intensityRadius" min="10" max="50" value="20">
                        <div class="d-flex justify-content-between">
                            <small>10px</small>
                            <small>50px</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
    .heat-map-legend {
        background: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        font-size: 12px;
    }
    
    .heat-map-legend .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .heat-map-legend .legend-color {
        width: 20px;
        height: 15px;
        margin-right: 8px;
        border-radius: 2px;
    }
    
    .leaflet-control-container .leaflet-top.leaflet-right {
        margin-top: 10px;
        margin-right: 10px;
    }
</style>
@endpush

@push('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://cdn.jsdelivr.net/npm/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
<script>
let heatMap;
let heatLayer;
let currentHeatData = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeHeatMap();
    loadHeatMapData();
    
    // Set default dates
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
});

function initializeHeatMap() {
    // Initialize map
    heatMap = L.map('heatMap').setView([{{ config('app.default_latitude', 40.7128) }}, {{ config('app.default_longitude', -74.0060) }}], 10);
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(heatMap);
    
    // Add legend
    addHeatMapLegend();
}

function addHeatMapLegend() {
    const legend = L.control({position: 'topright'});
    
    legend.onAdd = function(map) {
        const div = L.DomUtil.create('div', 'heat-map-legend');
        div.innerHTML = `
            <div class="legend-item">
                <div class="legend-color" style="background: #0000ff;"></div>
                <span>Low Activity</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #00ff00;"></div>
                <span>Medium Activity</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #ffff00;"></div>
                <span>High Activity</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #ff0000;"></div>
                <span>Very High Activity</span>
            </div>
        `;
        return div;
    };
    
    legend.addTo(heatMap);
}

function loadHeatMapData() {
    document.getElementById('mapLoading').style.display = 'flex';
    
    const formData = new FormData(document.getElementById('filtersForm'));
    const params = new URLSearchParams(formData);
    
    fetch(`/location/heat-map-data?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.message);
            }
            
            updateHeatMapDisplay(data);
            updateStatistics(data);
        })
        .catch(error => {
            console.error('Error loading heat map data:', error);
            alert('Failed to load heat map data: ' + error.message);
        })
        .finally(() => {
            document.getElementById('mapLoading').style.display = 'none';
        });
}

function updateHeatMapDisplay(data) {
    // Remove existing heat layer
    if (heatLayer) {
        heatMap.removeLayer(heatLayer);
    }
    
    // Prepare heat map data
    currentHeatData = data.heat_map_data.map(point => [
        point.lat,
        point.lng,
        point.intensity
    ]);
    
    if (currentHeatData.length > 0) {
        // Create heat layer
        const radius = document.getElementById('intensityRadius')?.value || 20;
        heatLayer = L.heatLayer(currentHeatData, {
            radius: parseInt(radius),
            blur: 15,
            maxZoom: 17,
            gradient: {
                0.0: 'blue',
                0.3: 'cyan',
                0.5: 'lime',
                0.7: 'yellow',
                1.0: 'red'
            }
        }).addTo(heatMap);
        
        // Fit map to heat data bounds
        const group = new L.featureGroup(currentHeatData.map(point => 
            L.marker([point[0], point[1]])
        ));
        heatMap.fitBounds(group.getBounds().pad(0.1));
    }
}

function updateStatistics(data) {
    document.getElementById('totalLocations').textContent = data.total_locations.toLocaleString();
    document.getElementById('hotSpots').textContent = data.heat_map_data.filter(p => p.intensity > 5).length;
    document.getElementById('dateRange').textContent = `${data.date_range.start} to ${data.date_range.end}`;
    document.getElementById('maxIntensity').textContent = Math.max(...data.heat_map_data.map(p => p.intensity), 0);
}

function applyFilters() {
    loadHeatMapData();
    bootstrap.Modal.getInstance(document.getElementById('filtersModal')).hide();
}

function exportHeatMap() {
    // Implementation for exporting heat map data
    const csvContent = "data:text/csv;charset=utf-8," + 
        "Latitude,Longitude,Intensity\n" +
        currentHeatData.map(row => row.join(",")).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "heat_map_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Update heat layer when radius changes
document.addEventListener('change', function(e) {
    if (e.target.id === 'intensityRadius' && heatLayer) {
        const radius = parseInt(e.target.value);
        heatMap.removeLayer(heatLayer);
        heatLayer = L.heatLayer(currentHeatData, {
            radius: radius,
            blur: 15,
            maxZoom: 17,
            gradient: {
                0.0: 'blue',
                0.3: 'cyan',
                0.5: 'lime',
                0.7: 'yellow',
                1.0: 'red'
            }
        }).addTo(heatMap);
    }
});
</script>
@endpush
