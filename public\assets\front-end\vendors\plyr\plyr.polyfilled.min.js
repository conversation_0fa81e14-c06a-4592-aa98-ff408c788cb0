"object"==typeof navigator&&function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define("Plyr",t):(e="undefined"!=typeof globalThis?globalThis:e||self).Plyr=t()}(this,(function(){"use strict";!function(){if("undefined"!=typeof window)try{var e=new window.CustomEvent("test",{cancelable:!0});if(e.preventDefault(),!0!==e.defaultPrevented)throw new Error("Could not prevent default")}catch(e){var t=function(e,t){var i,n;return(t=t||{}).bubbles=!!t.bubbles,t.cancelable=!!t.cancelable,(i=document.createEvent("CustomEvent")).initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n=i.preventDefault,i.preventDefault=function(){n.call(this);try{Object.defineProperty(this,"defaultPrevented",{get:function(){return!0}})}catch(e){this.defaultPrevented=!0}},i};t.prototype=window.Event.prototype,window.CustomEvent=t}}();var e=Math.ceil,t=Math.floor,i=function(i){return isNaN(i=+i)?0:(i>0?t:e)(i)},n=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},s=function(e){return function(t,s){var r,a,o=String(n(t)),l=i(s),c=o.length;return l<0||l>=c?e?"":void 0:(r=o.charCodeAt(l))<55296||r>56319||l+1===c||(a=o.charCodeAt(l+1))<56320||a>57343?e?o.charAt(l):r:e?o.slice(l,l+2):a-56320+(r-55296<<10)+65536}},r={codeAt:s(!1),charAt:s(!0)},a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(e,t){return e(t={exports:{}},t.exports),t.exports}var l=function(e){return e&&e.Math==Math&&e},c=l("object"==typeof globalThis&&globalThis)||l("object"==typeof window&&window)||l("object"==typeof self&&self)||l("object"==typeof a&&a)||function(){return this}()||Function("return this")(),u=function(e){try{return!!e()}catch(e){return!0}},h=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),d=function(e){return"object"==typeof e?null!==e:"function"==typeof e},p=c.document,m=d(p)&&d(p.createElement),f=function(e){return m?p.createElement(e):{}},g=!h&&!u((function(){return 7!=Object.defineProperty(f("div"),"a",{get:function(){return 7}}).a})),y=function(e){if(!d(e))throw TypeError(String(e)+" is not an object");return e},b=function(e,t){if(!d(e))return e;var i,n;if(t&&"function"==typeof(i=e.toString)&&!d(n=i.call(e)))return n;if("function"==typeof(i=e.valueOf)&&!d(n=i.call(e)))return n;if(!t&&"function"==typeof(i=e.toString)&&!d(n=i.call(e)))return n;throw TypeError("Can't convert object to primitive value")},v=Object.defineProperty,w={f:h?v:function(e,t,i){if(y(e),t=b(t,!0),y(i),g)try{return v(e,t,i)}catch(e){}if("get"in i||"set"in i)throw TypeError("Accessors not supported");return"value"in i&&(e[t]=i.value),e}},k=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},T=h?function(e,t,i){return w.f(e,t,k(1,i))}:function(e,t,i){return e[t]=i,e},S=function(e,t){try{T(c,e,t)}catch(i){c[e]=t}return t},A="__core-js_shared__",C=c[A]||S(A,{}),E=Function.toString;"function"!=typeof C.inspectSource&&(C.inspectSource=function(e){return E.call(e)});var P,L,x,I=C.inspectSource,O=c.WeakMap,_="function"==typeof O&&/native code/.test(I(O)),M={}.hasOwnProperty,N=function(e,t){return M.call(e,t)},j=o((function(e){(e.exports=function(e,t){return C[e]||(C[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.10.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),R=0,$=Math.random(),q=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++R+$).toString(36)},U=j("keys"),F=function(e){return U[e]||(U[e]=q(e))},D={},H=c.WeakMap;if(_){var B=C.state||(C.state=new H),V=B.get,W=B.has,z=B.set;P=function(e,t){return t.facade=e,z.call(B,e,t),t},L=function(e){return V.call(B,e)||{}},x=function(e){return W.call(B,e)}}else{var K=F("state");D[K]=!0,P=function(e,t){return t.facade=e,T(e,K,t),t},L=function(e){return N(e,K)?e[K]:{}},x=function(e){return N(e,K)}}var Y,G,X={set:P,get:L,has:x,enforce:function(e){return x(e)?L(e):P(e,{})},getterFor:function(e){return function(t){var i;if(!d(t)||(i=L(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return i}}},Q={}.propertyIsEnumerable,J=Object.getOwnPropertyDescriptor,Z={f:J&&!Q.call({1:2},1)?function(e){var t=J(this,e);return!!t&&t.enumerable}:Q},ee={}.toString,te=function(e){return ee.call(e).slice(8,-1)},ie="".split,ne=u((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==te(e)?ie.call(e,""):Object(e)}:Object,se=function(e){return ne(n(e))},re=Object.getOwnPropertyDescriptor,ae={f:h?re:function(e,t){if(e=se(e),t=b(t,!0),g)try{return re(e,t)}catch(e){}if(N(e,t))return k(!Z.f.call(e,t),e[t])}},oe=o((function(e){var t=X.get,i=X.enforce,n=String(String).split("String");(e.exports=function(e,t,s,r){var a,o=!!r&&!!r.unsafe,l=!!r&&!!r.enumerable,u=!!r&&!!r.noTargetGet;"function"==typeof s&&("string"!=typeof t||N(s,"name")||T(s,"name",t),(a=i(s)).source||(a.source=n.join("string"==typeof t?t:""))),e!==c?(o?!u&&e[t]&&(l=!0):delete e[t],l?e[t]=s:T(e,t,s)):l?e[t]=s:S(t,s)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||I(this)}))})),le=c,ce=function(e){return"function"==typeof e?e:void 0},ue=function(e,t){return arguments.length<2?ce(le[e])||ce(c[e]):le[e]&&le[e][t]||c[e]&&c[e][t]},he=Math.min,de=function(e){return e>0?he(i(e),9007199254740991):0},pe=Math.max,me=Math.min,fe=function(e){return function(t,n,s){var r,a=se(t),o=de(a.length),l=function(e,t){var n=i(e);return n<0?pe(n+t,0):me(n,t)}(s,o);if(e&&n!=n){for(;o>l;)if((r=a[l++])!=r)return!0}else for(;o>l;l++)if((e||l in a)&&a[l]===n)return e||l||0;return!e&&-1}},ge={includes:fe(!0),indexOf:fe(!1)}.indexOf,ye=function(e,t){var i,n=se(e),s=0,r=[];for(i in n)!N(D,i)&&N(n,i)&&r.push(i);for(;t.length>s;)N(n,i=t[s++])&&(~ge(r,i)||r.push(i));return r},be=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ve=be.concat("length","prototype"),we={f:Object.getOwnPropertyNames||function(e){return ye(e,ve)}},ke={f:Object.getOwnPropertySymbols},Te=ue("Reflect","ownKeys")||function(e){var t=we.f(y(e)),i=ke.f;return i?t.concat(i(e)):t},Se=function(e,t){for(var i=Te(t),n=w.f,s=ae.f,r=0;r<i.length;r++){var a=i[r];N(e,a)||n(e,a,s(t,a))}},Ae=/#|\.prototype\./,Ce=function(e,t){var i=Pe[Ee(e)];return i==xe||i!=Le&&("function"==typeof t?u(t):!!t)},Ee=Ce.normalize=function(e){return String(e).replace(Ae,".").toLowerCase()},Pe=Ce.data={},Le=Ce.NATIVE="N",xe=Ce.POLYFILL="P",Ie=Ce,Oe=ae.f,_e=function(e,t){var i,n,s,r,a,o=e.target,l=e.global,u=e.stat;if(i=l?c:u?c[o]||S(o,{}):(c[o]||{}).prototype)for(n in t){if(r=t[n],s=e.noTargetGet?(a=Oe(i,n))&&a.value:i[n],!Ie(l?n:o+(u?".":"#")+n,e.forced)&&void 0!==s){if(typeof r==typeof s)continue;Se(r,s)}(e.sham||s&&s.sham)&&T(r,"sham",!0),oe(i,n,r,e)}},Me=function(e){return Object(n(e))},Ne=!u((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),je=F("IE_PROTO"),Re=Object.prototype,$e=Ne?Object.getPrototypeOf:function(e){return e=Me(e),N(e,je)?e[je]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Re:null},qe="process"==te(c.process),Ue=ue("navigator","userAgent")||"",Fe=c.process,De=Fe&&Fe.versions,He=De&&De.v8;He?G=(Y=He.split("."))[0]+Y[1]:Ue&&(!(Y=Ue.match(/Edge\/(\d+)/))||Y[1]>=74)&&(Y=Ue.match(/Chrome\/(\d+)/))&&(G=Y[1]);var Be,Ve,We,ze=G&&+G,Ke=!!Object.getOwnPropertySymbols&&!u((function(){return!Symbol.sham&&(qe?38===ze:ze>37&&ze<41)})),Ye=Ke&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ge=j("wks"),Xe=c.Symbol,Qe=Ye?Xe:Xe&&Xe.withoutSetter||q,Je=function(e){return N(Ge,e)&&(Ke||"string"==typeof Ge[e])||(Ke&&N(Xe,e)?Ge[e]=Xe[e]:Ge[e]=Qe("Symbol."+e)),Ge[e]},Ze=Je("iterator"),et=!1;[].keys&&("next"in(We=[].keys())?(Ve=$e($e(We)))!==Object.prototype&&(Be=Ve):et=!0),(null==Be||u((function(){var e={};return Be[Ze].call(e)!==e})))&&(Be={}),N(Be,Ze)||T(Be,Ze,(function(){return this}));var tt,it={IteratorPrototype:Be,BUGGY_SAFARI_ITERATORS:et},nt=Object.keys||function(e){return ye(e,be)},st=h?Object.defineProperties:function(e,t){y(e);for(var i,n=nt(t),s=n.length,r=0;s>r;)w.f(e,i=n[r++],t[i]);return e},rt=ue("document","documentElement"),at=F("IE_PROTO"),ot=function(){},lt=function(e){return"<script>"+e+"</"+"script>"},ct=function(){try{tt=document.domain&&new ActiveXObject("htmlfile")}catch(e){}var e,t;ct=tt?function(e){e.write(lt("")),e.close();var t=e.parentWindow.Object;return e=null,t}(tt):((t=f("iframe")).style.display="none",rt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(lt("document.F=Object")),e.close(),e.F);for(var i=be.length;i--;)delete ct.prototype[be[i]];return ct()};D[at]=!0;var ut=Object.create||function(e,t){var i;return null!==e?(ot.prototype=y(e),i=new ot,ot.prototype=null,i[at]=e):i=ct(),void 0===t?i:st(i,t)},ht=w.f,dt=Je("toStringTag"),pt=function(e,t,i){e&&!N(e=i?e:e.prototype,dt)&&ht(e,dt,{configurable:!0,value:t})},mt={},ft=it.IteratorPrototype,gt=function(){return this},yt=function(e,t,i){var n=t+" Iterator";return e.prototype=ut(ft,{next:k(1,i)}),pt(e,n,!1),mt[n]=gt,e},bt=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,i={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(i,[]),t=i instanceof Array}catch(e){}return function(i,n){return y(i),function(e){if(!d(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(n),t?e.call(i,n):i.__proto__=n,i}}():void 0),vt=it.IteratorPrototype,wt=it.BUGGY_SAFARI_ITERATORS,kt=Je("iterator"),Tt="keys",St="values",At="entries",Ct=function(){return this},Et=function(e,t,i,n,s,r,a){yt(i,t,n);var o,l,c,u=function(e){if(e===s&&f)return f;if(!wt&&e in p)return p[e];switch(e){case Tt:case St:case At:return function(){return new i(this,e)}}return function(){return new i(this)}},h=t+" Iterator",d=!1,p=e.prototype,m=p[kt]||p["@@iterator"]||s&&p[s],f=!wt&&m||u(s),g="Array"==t&&p.entries||m;if(g&&(o=$e(g.call(new e)),vt!==Object.prototype&&o.next&&($e(o)!==vt&&(bt?bt(o,vt):"function"!=typeof o[kt]&&T(o,kt,Ct)),pt(o,h,!0))),s==St&&m&&m.name!==St&&(d=!0,f=function(){return m.call(this)}),p[kt]!==f&&T(p,kt,f),mt[t]=f,s)if(l={values:u(St),keys:r?f:u(Tt),entries:u(At)},a)for(c in l)(wt||d||!(c in p))&&oe(p,c,l[c]);else _e({target:t,proto:!0,forced:wt||d},l);return l},Pt=r.charAt,Lt="String Iterator",xt=X.set,It=X.getterFor(Lt);Et(String,"String",(function(e){xt(this,{type:Lt,string:String(e),index:0})}),(function(){var e,t=It(this),i=t.string,n=t.index;return n>=i.length?{value:void 0,done:!0}:(e=Pt(i,n),t.index+=e.length,{value:e,done:!1})}));var Ot=Je("iterator"),_t=!u((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,i="";return e.pathname="c%20d",t.forEach((function(e,n){t.delete("b"),i+=n+e})),!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[Ot]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==i||"x"!==new URL("http://x",void 0).host})),Mt=function(e,t,i){if(!(e instanceof t))throw TypeError("Incorrect "+(i?i+" ":"")+"invocation");return e},Nt=Object.assign,jt=Object.defineProperty,Rt=!Nt||u((function(){if(h&&1!==Nt({b:1},Nt(jt({},"a",{enumerable:!0,get:function(){jt(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},i=Symbol(),n="abcdefghijklmnopqrst";return e[i]=7,n.split("").forEach((function(e){t[e]=e})),7!=Nt({},e)[i]||nt(Nt({},t)).join("")!=n}))?function(e,t){for(var i=Me(e),n=arguments.length,s=1,r=ke.f,a=Z.f;n>s;)for(var o,l=ne(arguments[s++]),c=r?nt(l).concat(r(l)):nt(l),u=c.length,d=0;u>d;)o=c[d++],h&&!a.call(l,o)||(i[o]=l[o]);return i}:Nt,$t=function(e,t,i){if(function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function")}(e),void 0===t)return e;switch(i){case 0:return function(){return e.call(t)};case 1:return function(i){return e.call(t,i)};case 2:return function(i,n){return e.call(t,i,n)};case 3:return function(i,n,s){return e.call(t,i,n,s)}}return function(){return e.apply(t,arguments)}},qt=function(e,t,i,n){try{return n?t(y(i)[0],i[1]):t(i)}catch(t){throw function(e){var t=e.return;if(void 0!==t)y(t.call(e)).value}(e),t}},Ut=Je("iterator"),Ft=Array.prototype,Dt=function(e){return void 0!==e&&(mt.Array===e||Ft[Ut]===e)},Ht=function(e,t,i){var n=b(t);n in e?w.f(e,n,k(0,i)):e[n]=i},Bt={};Bt[Je("toStringTag")]="z";var Vt="[object z]"===String(Bt),Wt=Je("toStringTag"),zt="Arguments"==te(function(){return arguments}()),Kt=Vt?te:function(e){var t,i,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(i=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),Wt))?i:zt?te(t):"Object"==(n=te(t))&&"function"==typeof t.callee?"Arguments":n},Yt=Je("iterator"),Gt=function(e){if(null!=e)return e[Yt]||e["@@iterator"]||mt[Kt(e)]},Xt=function(e){var t,i,n,s,r,a,o=Me(e),l="function"==typeof this?this:Array,c=arguments.length,u=c>1?arguments[1]:void 0,h=void 0!==u,d=Gt(o),p=0;if(h&&(u=$t(u,c>2?arguments[2]:void 0,2)),null==d||l==Array&&Dt(d))for(i=new l(t=de(o.length));t>p;p++)a=h?u(o[p],p):o[p],Ht(i,p,a);else for(r=(s=d.call(o)).next,i=new l;!(n=r.call(s)).done;p++)a=h?qt(s,u,[n.value,p],!0):n.value,Ht(i,p,a);return i.length=p,i},Qt=2147483647,Jt=/[^\0-\u007E]/,Zt=/[.\u3002\uFF0E\uFF61]/g,ei="Overflow: input needs wider integers to process",ti=Math.floor,ii=String.fromCharCode,ni=function(e){return e+22+75*(e<26)},si=function(e,t,i){var n=0;for(e=i?ti(e/700):e>>1,e+=ti(e/t);e>455;n+=36)e=ti(e/35);return ti(n+36*e/(e+38))},ri=function(e){var t,i,n=[],s=(e=function(e){for(var t=[],i=0,n=e.length;i<n;){var s=e.charCodeAt(i++);if(s>=55296&&s<=56319&&i<n){var r=e.charCodeAt(i++);56320==(64512&r)?t.push(((1023&s)<<10)+(1023&r)+65536):(t.push(s),i--)}else t.push(s)}return t}(e)).length,r=128,a=0,o=72;for(t=0;t<e.length;t++)(i=e[t])<128&&n.push(ii(i));var l=n.length,c=l;for(l&&n.push("-");c<s;){var u=Qt;for(t=0;t<e.length;t++)(i=e[t])>=r&&i<u&&(u=i);var h=c+1;if(u-r>ti((Qt-a)/h))throw RangeError(ei);for(a+=(u-r)*h,r=u,t=0;t<e.length;t++){if((i=e[t])<r&&++a>Qt)throw RangeError(ei);if(i==r){for(var d=a,p=36;;p+=36){var m=p<=o?1:p>=o+26?26:p-o;if(d<m)break;var f=d-m,g=36-m;n.push(ii(ni(m+f%g))),d=ti(f/g)}n.push(ii(ni(d))),o=si(a,h,c==l),a=0,++c}}++a,++r}return n.join("")},ai=Je("unscopables"),oi=Array.prototype;null==oi[ai]&&w.f(oi,ai,{configurable:!0,value:ut(null)});var li=function(e){oi[ai][e]=!0},ci="Array Iterator",ui=X.set,hi=X.getterFor(ci);Et(Array,"Array",(function(e,t){ui(this,{type:ci,target:se(e),index:0,kind:t})}),(function(){var e=hi(this),t=e.target,i=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==i?{value:n,done:!1}:"values"==i?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}}),"values"),mt.Arguments=mt.Array,li("keys"),li("values"),li("entries");var di=function(e){var t=Gt(e);if("function"!=typeof t)throw TypeError(String(e)+" is not iterable");return y(t.call(e))},pi=ue("fetch"),mi=ue("Headers"),fi=Je("iterator"),gi="URLSearchParams",yi="URLSearchParamsIterator",bi=X.set,vi=X.getterFor(gi),wi=X.getterFor(yi),ki=/\+/g,Ti=Array(4),Si=function(e){return Ti[e-1]||(Ti[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},Ai=function(e){try{return decodeURIComponent(e)}catch(t){return e}},Ci=function(e){var t=e.replace(ki," "),i=4;try{return decodeURIComponent(t)}catch(e){for(;i;)t=t.replace(Si(i--),Ai);return t}},Ei=/[!'()~]|%20/g,Pi={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},Li=function(e){return Pi[e]},xi=function(e){return encodeURIComponent(e).replace(Ei,Li)},Ii=function(e,t){if(t)for(var i,n,s=t.split("&"),r=0;r<s.length;)(i=s[r++]).length&&(n=i.split("="),e.push({key:Ci(n.shift()),value:Ci(n.join("="))}))},Oi=function(e){this.entries.length=0,Ii(this.entries,e)},_i=function(e,t){if(e<t)throw TypeError("Not enough arguments")},Mi=yt((function(e,t){bi(this,{type:yi,iterator:di(vi(e).entries),kind:t})}),"Iterator",(function(){var e=wi(this),t=e.kind,i=e.iterator.next(),n=i.value;return i.done||(i.value="keys"===t?n.key:"values"===t?n.value:[n.key,n.value]),i})),Ni=function(){Mt(this,Ni,gi);var e,t,i,n,s,r,a,o,l,c=arguments.length>0?arguments[0]:void 0,u=this,h=[];if(bi(u,{type:gi,entries:h,updateURL:function(){},updateSearchParams:Oi}),void 0!==c)if(d(c))if("function"==typeof(e=Gt(c)))for(i=(t=e.call(c)).next;!(n=i.call(t)).done;){if((a=(r=(s=di(y(n.value))).next).call(s)).done||(o=r.call(s)).done||!r.call(s).done)throw TypeError("Expected sequence with length 2");h.push({key:a.value+"",value:o.value+""})}else for(l in c)N(c,l)&&h.push({key:l,value:c[l]+""});else Ii(h,"string"==typeof c?"?"===c.charAt(0)?c.slice(1):c:c+"")},ji=Ni.prototype;!function(e,t,i){for(var n in t)oe(e,n,t[n],i)}(ji,{append:function(e,t){_i(arguments.length,2);var i=vi(this);i.entries.push({key:e+"",value:t+""}),i.updateURL()},delete:function(e){_i(arguments.length,1);for(var t=vi(this),i=t.entries,n=e+"",s=0;s<i.length;)i[s].key===n?i.splice(s,1):s++;t.updateURL()},get:function(e){_i(arguments.length,1);for(var t=vi(this).entries,i=e+"",n=0;n<t.length;n++)if(t[n].key===i)return t[n].value;return null},getAll:function(e){_i(arguments.length,1);for(var t=vi(this).entries,i=e+"",n=[],s=0;s<t.length;s++)t[s].key===i&&n.push(t[s].value);return n},has:function(e){_i(arguments.length,1);for(var t=vi(this).entries,i=e+"",n=0;n<t.length;)if(t[n++].key===i)return!0;return!1},set:function(e,t){_i(arguments.length,1);for(var i,n=vi(this),s=n.entries,r=!1,a=e+"",o=t+"",l=0;l<s.length;l++)(i=s[l]).key===a&&(r?s.splice(l--,1):(r=!0,i.value=o));r||s.push({key:a,value:o}),n.updateURL()},sort:function(){var e,t,i,n=vi(this),s=n.entries,r=s.slice();for(s.length=0,i=0;i<r.length;i++){for(e=r[i],t=0;t<i;t++)if(s[t].key>e.key){s.splice(t,0,e);break}t===i&&s.push(e)}n.updateURL()},forEach:function(e){for(var t,i=vi(this).entries,n=$t(e,arguments.length>1?arguments[1]:void 0,3),s=0;s<i.length;)n((t=i[s++]).value,t.key,this)},keys:function(){return new Mi(this,"keys")},values:function(){return new Mi(this,"values")},entries:function(){return new Mi(this,"entries")}},{enumerable:!0}),oe(ji,fi,ji.entries),oe(ji,"toString",(function(){for(var e,t=vi(this).entries,i=[],n=0;n<t.length;)e=t[n++],i.push(xi(e.key)+"="+xi(e.value));return i.join("&")}),{enumerable:!0}),pt(Ni,gi),_e({global:!0,forced:!_t},{URLSearchParams:Ni}),_t||"function"!=typeof pi||"function"!=typeof mi||_e({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var t,i,n,s=[e];return arguments.length>1&&(d(t=arguments[1])&&(i=t.body,Kt(i)===gi&&((n=t.headers?new mi(t.headers):new mi).has("content-type")||n.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),t=ut(t,{body:k(0,String(i)),headers:k(0,n)}))),s.push(t)),pi.apply(this,s)}});var Ri,$i={URLSearchParams:Ni,getState:vi},qi=r.codeAt,Ui=c.URL,Fi=$i.URLSearchParams,Di=$i.getState,Hi=X.set,Bi=X.getterFor("URL"),Vi=Math.floor,Wi=Math.pow,zi="Invalid scheme",Ki="Invalid host",Yi="Invalid port",Gi=/[A-Za-z]/,Xi=/[\d+-.A-Za-z]/,Qi=/\d/,Ji=/^(0x|0X)/,Zi=/^[0-7]+$/,en=/^\d+$/,tn=/^[\dA-Fa-f]+$/,nn=/[\u0000\t\u000A\u000D #%/:?@[\\]]/,sn=/[\u0000\t\u000A\u000D #/:?@[\\]]/,rn=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,an=/[\t\u000A\u000D]/g,on=function(e,t){var i,n,s;if("["==t.charAt(0)){if("]"!=t.charAt(t.length-1))return Ki;if(!(i=cn(t.slice(1,-1))))return Ki;e.host=i}else if(yn(e)){if(t=function(e){var t,i,n=[],s=e.toLowerCase().replace(Zt,".").split(".");for(t=0;t<s.length;t++)i=s[t],n.push(Jt.test(i)?"xn--"+ri(i):i);return n.join(".")}(t),nn.test(t))return Ki;if(null===(i=ln(t)))return Ki;e.host=i}else{if(sn.test(t))return Ki;for(i="",n=Xt(t),s=0;s<n.length;s++)i+=fn(n[s],hn);e.host=i}},ln=function(e){var t,i,n,s,r,a,o,l=e.split(".");if(l.length&&""==l[l.length-1]&&l.pop(),(t=l.length)>4)return e;for(i=[],n=0;n<t;n++){if(""==(s=l[n]))return e;if(r=10,s.length>1&&"0"==s.charAt(0)&&(r=Ji.test(s)?16:8,s=s.slice(8==r?1:2)),""===s)a=0;else{if(!(10==r?en:8==r?Zi:tn).test(s))return e;a=parseInt(s,r)}i.push(a)}for(n=0;n<t;n++)if(a=i[n],n==t-1){if(a>=Wi(256,5-t))return null}else if(a>255)return null;for(o=i.pop(),n=0;n<i.length;n++)o+=i[n]*Wi(256,3-n);return o},cn=function(e){var t,i,n,s,r,a,o,l=[0,0,0,0,0,0,0,0],c=0,u=null,h=0,d=function(){return e.charAt(h)};if(":"==d()){if(":"!=e.charAt(1))return;h+=2,u=++c}for(;d();){if(8==c)return;if(":"!=d()){for(t=i=0;i<4&&tn.test(d());)t=16*t+parseInt(d(),16),h++,i++;if("."==d()){if(0==i)return;if(h-=i,c>6)return;for(n=0;d();){if(s=null,n>0){if(!("."==d()&&n<4))return;h++}if(!Qi.test(d()))return;for(;Qi.test(d());){if(r=parseInt(d(),10),null===s)s=r;else{if(0==s)return;s=10*s+r}if(s>255)return;h++}l[c]=256*l[c]+s,2!=++n&&4!=n||c++}if(4!=n)return;break}if(":"==d()){if(h++,!d())return}else if(d())return;l[c++]=t}else{if(null!==u)return;h++,u=++c}}if(null!==u)for(a=c-u,c=7;0!=c&&a>0;)o=l[c],l[c--]=l[u+a-1],l[u+--a]=o;else if(8!=c)return;return l},un=function(e){var t,i,n,s;if("number"==typeof e){for(t=[],i=0;i<4;i++)t.unshift(e%256),e=Vi(e/256);return t.join(".")}if("object"==typeof e){for(t="",n=function(e){for(var t=null,i=1,n=null,s=0,r=0;r<8;r++)0!==e[r]?(s>i&&(t=n,i=s),n=null,s=0):(null===n&&(n=r),++s);return s>i&&(t=n,i=s),t}(e),i=0;i<8;i++)s&&0===e[i]||(s&&(s=!1),n===i?(t+=i?":":"::",s=!0):(t+=e[i].toString(16),i<7&&(t+=":")));return"["+t+"]"}return e},hn={},dn=Rt({},hn,{" ":1,'"':1,"<":1,">":1,"`":1}),pn=Rt({},dn,{"#":1,"?":1,"{":1,"}":1}),mn=Rt({},pn,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),fn=function(e,t){var i=qi(e,0);return i>32&&i<127&&!N(t,e)?e:encodeURIComponent(e)},gn={ftp:21,file:null,http:80,https:443,ws:80,wss:443},yn=function(e){return N(gn,e.scheme)},bn=function(e){return""!=e.username||""!=e.password},vn=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},wn=function(e,t){var i;return 2==e.length&&Gi.test(e.charAt(0))&&(":"==(i=e.charAt(1))||!t&&"|"==i)},kn=function(e){var t;return e.length>1&&wn(e.slice(0,2))&&(2==e.length||"/"===(t=e.charAt(2))||"\\"===t||"?"===t||"#"===t)},Tn=function(e){var t=e.path,i=t.length;!i||"file"==e.scheme&&1==i&&wn(t[0],!0)||t.pop()},Sn=function(e){return"."===e||"%2e"===e.toLowerCase()},An={},Cn={},En={},Pn={},Ln={},xn={},In={},On={},_n={},Mn={},Nn={},jn={},Rn={},$n={},qn={},Un={},Fn={},Dn={},Hn={},Bn={},Vn={},Wn=function(e,t,i,n){var s,r,a,o,l,c=i||An,u=0,h="",d=!1,p=!1,m=!1;for(i||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,t=t.replace(rn,"")),t=t.replace(an,""),s=Xt(t);u<=s.length;){switch(r=s[u],c){case An:if(!r||!Gi.test(r)){if(i)return zi;c=En;continue}h+=r.toLowerCase(),c=Cn;break;case Cn:if(r&&(Xi.test(r)||"+"==r||"-"==r||"."==r))h+=r.toLowerCase();else{if(":"!=r){if(i)return zi;h="",c=En,u=0;continue}if(i&&(yn(e)!=N(gn,h)||"file"==h&&(bn(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=h,i)return void(yn(e)&&gn[e.scheme]==e.port&&(e.port=null));h="","file"==e.scheme?c=$n:yn(e)&&n&&n.scheme==e.scheme?c=Pn:yn(e)?c=On:"/"==s[u+1]?(c=Ln,u++):(e.cannotBeABaseURL=!0,e.path.push(""),c=Hn)}break;case En:if(!n||n.cannotBeABaseURL&&"#"!=r)return zi;if(n.cannotBeABaseURL&&"#"==r){e.scheme=n.scheme,e.path=n.path.slice(),e.query=n.query,e.fragment="",e.cannotBeABaseURL=!0,c=Vn;break}c="file"==n.scheme?$n:xn;continue;case Pn:if("/"!=r||"/"!=s[u+1]){c=xn;continue}c=_n,u++;break;case Ln:if("/"==r){c=Mn;break}c=Dn;continue;case xn:if(e.scheme=n.scheme,r==Ri)e.username=n.username,e.password=n.password,e.host=n.host,e.port=n.port,e.path=n.path.slice(),e.query=n.query;else if("/"==r||"\\"==r&&yn(e))c=In;else if("?"==r)e.username=n.username,e.password=n.password,e.host=n.host,e.port=n.port,e.path=n.path.slice(),e.query="",c=Bn;else{if("#"!=r){e.username=n.username,e.password=n.password,e.host=n.host,e.port=n.port,e.path=n.path.slice(),e.path.pop(),c=Dn;continue}e.username=n.username,e.password=n.password,e.host=n.host,e.port=n.port,e.path=n.path.slice(),e.query=n.query,e.fragment="",c=Vn}break;case In:if(!yn(e)||"/"!=r&&"\\"!=r){if("/"!=r){e.username=n.username,e.password=n.password,e.host=n.host,e.port=n.port,c=Dn;continue}c=Mn}else c=_n;break;case On:if(c=_n,"/"!=r||"/"!=h.charAt(u+1))continue;u++;break;case _n:if("/"!=r&&"\\"!=r){c=Mn;continue}break;case Mn:if("@"==r){d&&(h="%40"+h),d=!0,a=Xt(h);for(var f=0;f<a.length;f++){var g=a[f];if(":"!=g||m){var y=fn(g,mn);m?e.password+=y:e.username+=y}else m=!0}h=""}else if(r==Ri||"/"==r||"?"==r||"#"==r||"\\"==r&&yn(e)){if(d&&""==h)return"Invalid authority";u-=Xt(h).length+1,h="",c=Nn}else h+=r;break;case Nn:case jn:if(i&&"file"==e.scheme){c=Un;continue}if(":"!=r||p){if(r==Ri||"/"==r||"?"==r||"#"==r||"\\"==r&&yn(e)){if(yn(e)&&""==h)return Ki;if(i&&""==h&&(bn(e)||null!==e.port))return;if(o=on(e,h))return o;if(h="",c=Fn,i)return;continue}"["==r?p=!0:"]"==r&&(p=!1),h+=r}else{if(""==h)return Ki;if(o=on(e,h))return o;if(h="",c=Rn,i==jn)return}break;case Rn:if(!Qi.test(r)){if(r==Ri||"/"==r||"?"==r||"#"==r||"\\"==r&&yn(e)||i){if(""!=h){var b=parseInt(h,10);if(b>65535)return Yi;e.port=yn(e)&&b===gn[e.scheme]?null:b,h=""}if(i)return;c=Fn;continue}return Yi}h+=r;break;case $n:if(e.scheme="file","/"==r||"\\"==r)c=qn;else{if(!n||"file"!=n.scheme){c=Dn;continue}if(r==Ri)e.host=n.host,e.path=n.path.slice(),e.query=n.query;else if("?"==r)e.host=n.host,e.path=n.path.slice(),e.query="",c=Bn;else{if("#"!=r){kn(s.slice(u).join(""))||(e.host=n.host,e.path=n.path.slice(),Tn(e)),c=Dn;continue}e.host=n.host,e.path=n.path.slice(),e.query=n.query,e.fragment="",c=Vn}}break;case qn:if("/"==r||"\\"==r){c=Un;break}n&&"file"==n.scheme&&!kn(s.slice(u).join(""))&&(wn(n.path[0],!0)?e.path.push(n.path[0]):e.host=n.host),c=Dn;continue;case Un:if(r==Ri||"/"==r||"\\"==r||"?"==r||"#"==r){if(!i&&wn(h))c=Dn;else if(""==h){if(e.host="",i)return;c=Fn}else{if(o=on(e,h))return o;if("localhost"==e.host&&(e.host=""),i)return;h="",c=Fn}continue}h+=r;break;case Fn:if(yn(e)){if(c=Dn,"/"!=r&&"\\"!=r)continue}else if(i||"?"!=r)if(i||"#"!=r){if(r!=Ri&&(c=Dn,"/"!=r))continue}else e.fragment="",c=Vn;else e.query="",c=Bn;break;case Dn:if(r==Ri||"/"==r||"\\"==r&&yn(e)||!i&&("?"==r||"#"==r)){if(".."===(l=(l=h).toLowerCase())||"%2e."===l||".%2e"===l||"%2e%2e"===l?(Tn(e),"/"==r||"\\"==r&&yn(e)||e.path.push("")):Sn(h)?"/"==r||"\\"==r&&yn(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&wn(h)&&(e.host&&(e.host=""),h=h.charAt(0)+":"),e.path.push(h)),h="","file"==e.scheme&&(r==Ri||"?"==r||"#"==r))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==r?(e.query="",c=Bn):"#"==r&&(e.fragment="",c=Vn)}else h+=fn(r,pn);break;case Hn:"?"==r?(e.query="",c=Bn):"#"==r?(e.fragment="",c=Vn):r!=Ri&&(e.path[0]+=fn(r,hn));break;case Bn:i||"#"!=r?r!=Ri&&("'"==r&&yn(e)?e.query+="%27":e.query+="#"==r?"%23":fn(r,hn)):(e.fragment="",c=Vn);break;case Vn:r!=Ri&&(e.fragment+=fn(r,dn))}u++}},zn=function(e){var t,i,n=Mt(this,zn,"URL"),s=arguments.length>1?arguments[1]:void 0,r=String(e),a=Hi(n,{type:"URL"});if(void 0!==s)if(s instanceof zn)t=Bi(s);else if(i=Wn(t={},String(s)))throw TypeError(i);if(i=Wn(a,r,null,t))throw TypeError(i);var o=a.searchParams=new Fi,l=Di(o);l.updateSearchParams(a.query),l.updateURL=function(){a.query=String(o)||null},h||(n.href=Yn.call(n),n.origin=Gn.call(n),n.protocol=Xn.call(n),n.username=Qn.call(n),n.password=Jn.call(n),n.host=Zn.call(n),n.hostname=es.call(n),n.port=ts.call(n),n.pathname=is.call(n),n.search=ns.call(n),n.searchParams=ss.call(n),n.hash=rs.call(n))},Kn=zn.prototype,Yn=function(){var e=Bi(this),t=e.scheme,i=e.username,n=e.password,s=e.host,r=e.port,a=e.path,o=e.query,l=e.fragment,c=t+":";return null!==s?(c+="//",bn(e)&&(c+=i+(n?":"+n:"")+"@"),c+=un(s),null!==r&&(c+=":"+r)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?a[0]:a.length?"/"+a.join("/"):"",null!==o&&(c+="?"+o),null!==l&&(c+="#"+l),c},Gn=function(){var e=Bi(this),t=e.scheme,i=e.port;if("blob"==t)try{return new URL(t.path[0]).origin}catch(e){return"null"}return"file"!=t&&yn(e)?t+"://"+un(e.host)+(null!==i?":"+i:""):"null"},Xn=function(){return Bi(this).scheme+":"},Qn=function(){return Bi(this).username},Jn=function(){return Bi(this).password},Zn=function(){var e=Bi(this),t=e.host,i=e.port;return null===t?"":null===i?un(t):un(t)+":"+i},es=function(){var e=Bi(this).host;return null===e?"":un(e)},ts=function(){var e=Bi(this).port;return null===e?"":String(e)},is=function(){var e=Bi(this),t=e.path;return e.cannotBeABaseURL?t[0]:t.length?"/"+t.join("/"):""},ns=function(){var e=Bi(this).query;return e?"?"+e:""},ss=function(){return Bi(this).searchParams},rs=function(){var e=Bi(this).fragment;return e?"#"+e:""},as=function(e,t){return{get:e,set:t,configurable:!0,enumerable:!0}};if(h&&st(Kn,{href:as(Yn,(function(e){var t=Bi(this),i=String(e),n=Wn(t,i);if(n)throw TypeError(n);Di(t.searchParams).updateSearchParams(t.query)})),origin:as(Gn),protocol:as(Xn,(function(e){var t=Bi(this);Wn(t,String(e)+":",An)})),username:as(Qn,(function(e){var t=Bi(this),i=Xt(String(e));if(!vn(t)){t.username="";for(var n=0;n<i.length;n++)t.username+=fn(i[n],mn)}})),password:as(Jn,(function(e){var t=Bi(this),i=Xt(String(e));if(!vn(t)){t.password="";for(var n=0;n<i.length;n++)t.password+=fn(i[n],mn)}})),host:as(Zn,(function(e){var t=Bi(this);t.cannotBeABaseURL||Wn(t,String(e),Nn)})),hostname:as(es,(function(e){var t=Bi(this);t.cannotBeABaseURL||Wn(t,String(e),jn)})),port:as(ts,(function(e){var t=Bi(this);vn(t)||(""==(e=String(e))?t.port=null:Wn(t,e,Rn))})),pathname:as(is,(function(e){var t=Bi(this);t.cannotBeABaseURL||(t.path=[],Wn(t,e+"",Fn))})),search:as(ns,(function(e){var t=Bi(this);""==(e=String(e))?t.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),t.query="",Wn(t,e,Bn)),Di(t.searchParams).updateSearchParams(t.query)})),searchParams:as(ss),hash:as(rs,(function(e){var t=Bi(this);""!=(e=String(e))?("#"==e.charAt(0)&&(e=e.slice(1)),t.fragment="",Wn(t,e,Vn)):t.fragment=null}))}),oe(Kn,"toJSON",(function(){return Yn.call(this)}),{enumerable:!0}),oe(Kn,"toString",(function(){return Yn.call(this)}),{enumerable:!0}),Ui){var os=Ui.createObjectURL,ls=Ui.revokeObjectURL;os&&oe(zn,"createObjectURL",(function(e){return os.apply(Ui,arguments)})),ls&&oe(zn,"revokeObjectURL",(function(e){return ls.apply(Ui,arguments)}))}function cs(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function us(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function hs(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function ds(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function ps(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ds(Object(i),!0).forEach((function(t){hs(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ds(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}pt(zn,"URL"),_e({global:!0,forced:!_t,sham:!h},{URL:zn}),function(e){var t=function(){try{return!!Symbol.iterator}catch(e){return!1}}(),i=function(e){var i={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return t&&(i[Symbol.iterator]=function(){return i}),i},n=function(e){return encodeURIComponent(e).replace(/%20/g,"+")},s=function(e){return decodeURIComponent(String(e).replace(/\+/g," "))};(function(){try{var t=e.URLSearchParams;return"a=1"===new t("?a=1").toString()&&"function"==typeof t.prototype.set&&"function"==typeof t.prototype.entries}catch(e){return!1}})()||function(){var s=function(e){Object.defineProperty(this,"_entries",{writable:!0,value:{}});var t=typeof e;if("undefined"===t);else if("string"===t)""!==e&&this._fromString(e);else if(e instanceof s){var i=this;e.forEach((function(e,t){i.append(t,e)}))}else{if(null===e||"object"!==t)throw new TypeError("Unsupported input's type for URLSearchParams");if("[object Array]"===Object.prototype.toString.call(e))for(var n=0;n<e.length;n++){var r=e[n];if("[object Array]"!==Object.prototype.toString.call(r)&&2===r.length)throw new TypeError("Expected [string, any] as entry at index "+n+" of URLSearchParams's input");this.append(r[0],r[1])}else for(var a in e)e.hasOwnProperty(a)&&this.append(a,e[a])}},r=s.prototype;r.append=function(e,t){e in this._entries?this._entries[e].push(String(t)):this._entries[e]=[String(t)]},r.delete=function(e){delete this._entries[e]},r.get=function(e){return e in this._entries?this._entries[e][0]:null},r.getAll=function(e){return e in this._entries?this._entries[e].slice(0):[]},r.has=function(e){return e in this._entries},r.set=function(e,t){this._entries[e]=[String(t)]},r.forEach=function(e,t){var i;for(var n in this._entries)if(this._entries.hasOwnProperty(n)){i=this._entries[n];for(var s=0;s<i.length;s++)e.call(t,i[s],n,this)}},r.keys=function(){var e=[];return this.forEach((function(t,i){e.push(i)})),i(e)},r.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),i(e)},r.entries=function(){var e=[];return this.forEach((function(t,i){e.push([i,t])})),i(e)},t&&(r[Symbol.iterator]=r.entries),r.toString=function(){var e=[];return this.forEach((function(t,i){e.push(n(i)+"="+n(t))})),e.join("&")},e.URLSearchParams=s}();var r=e.URLSearchParams.prototype;"function"!=typeof r.sort&&(r.sort=function(){var e=this,t=[];this.forEach((function(i,n){t.push([n,i]),e._entries||e.delete(n)})),t.sort((function(e,t){return e[0]<t[0]?-1:e[0]>t[0]?1:0})),e._entries&&(e._entries={});for(var i=0;i<t.length;i++)this.append(t[i][0],t[i][1])}),"function"!=typeof r._fromString&&Object.defineProperty(r,"_fromString",{enumerable:!1,configurable:!1,writable:!1,value:function(e){if(this._entries)this._entries={};else{var t=[];this.forEach((function(e,i){t.push(i)}));for(var i=0;i<t.length;i++)this.delete(t[i])}var n,r=(e=e.replace(/^\?/,"")).split("&");for(i=0;i<r.length;i++)n=r[i].split("="),this.append(s(n[0]),n.length>1?s(n[1]):"")}})}(void 0!==a?a:"undefined"!=typeof window?window:"undefined"!=typeof self?self:a),function(e){if(function(){try{var t=new e.URL("b","http://a");return t.pathname="c d","http://a/c%20d"===t.href&&t.searchParams}catch(e){return!1}}()||function(){var t=e.URL,i=function(t,i){"string"!=typeof t&&(t=String(t)),i&&"string"!=typeof i&&(i=String(i));var n,s=document;if(i&&(void 0===e.location||i!==e.location.href)){i=i.toLowerCase(),(n=(s=document.implementation.createHTMLDocument("")).createElement("base")).href=i,s.head.appendChild(n);try{if(0!==n.href.indexOf(i))throw new Error(n.href)}catch(e){throw new Error("URL unable to set base "+i+" due to "+e)}}var r=s.createElement("a");r.href=t,n&&(s.body.appendChild(r),r.href=r.href);var a=s.createElement("input");if(a.type="url",a.value=t,":"===r.protocol||!/:/.test(r.href)||!a.checkValidity()&&!i)throw new TypeError("Invalid URL");Object.defineProperty(this,"_anchorElement",{value:r});var o=new e.URLSearchParams(this.search),l=!0,c=!0,u=this;["append","delete","set"].forEach((function(e){var t=o[e];o[e]=function(){t.apply(o,arguments),l&&(c=!1,u.search=o.toString(),c=!0)}})),Object.defineProperty(this,"searchParams",{value:o,enumerable:!0});var h=void 0;Object.defineProperty(this,"_updateSearchParams",{enumerable:!1,configurable:!1,writable:!1,value:function(){this.search!==h&&(h=this.search,c&&(l=!1,this.searchParams._fromString(this.search),l=!0))}})},n=i.prototype;["hash","host","hostname","port","protocol"].forEach((function(e){!function(e){Object.defineProperty(n,e,{get:function(){return this._anchorElement[e]},set:function(t){this._anchorElement[e]=t},enumerable:!0})}(e)})),Object.defineProperty(n,"search",{get:function(){return this._anchorElement.search},set:function(e){this._anchorElement.search=e,this._updateSearchParams()},enumerable:!0}),Object.defineProperties(n,{toString:{get:function(){var e=this;return function(){return e.href}}},href:{get:function(){return this._anchorElement.href.replace(/\?$/,"")},set:function(e){this._anchorElement.href=e,this._updateSearchParams()},enumerable:!0},pathname:{get:function(){return this._anchorElement.pathname.replace(/(^\/?)/,"/")},set:function(e){this._anchorElement.pathname=e},enumerable:!0},origin:{get:function(){var e={"http:":80,"https:":443,"ftp:":21}[this._anchorElement.protocol],t=this._anchorElement.port!=e&&""!==this._anchorElement.port;return this._anchorElement.protocol+"//"+this._anchorElement.hostname+(t?":"+this._anchorElement.port:"")},enumerable:!0},password:{get:function(){return""},set:function(e){},enumerable:!0},username:{get:function(){return""},set:function(e){},enumerable:!0}}),i.createObjectURL=function(e){return t.createObjectURL.apply(t,arguments)},i.revokeObjectURL=function(e){return t.revokeObjectURL.apply(t,arguments)},e.URL=i}(),void 0!==e.location&&!("origin"in e.location)){var t=function(){return e.location.protocol+"//"+e.location.hostname+(e.location.port?":"+e.location.port:"")};try{Object.defineProperty(e.location,"origin",{get:t,enumerable:!0})}catch(i){setInterval((function(){e.location.origin=t()}),100)}}}(void 0!==a?a:"undefined"!=typeof window?window:"undefined"!=typeof self?self:a);var ms={addCSS:!0,thumbWidth:15,watch:!0};function fs(e,t){return function(){return Array.from(document.querySelectorAll(t)).includes(this)}.call(e,t)}var gs=function(e){return null!=e?e.constructor:null},ys=function(e,t){return!!(e&&t&&e instanceof t)},bs=function(e){return null==e},vs=function(e){return gs(e)===Object},ws=function(e){return gs(e)===String},ks=function(e){return Array.isArray(e)},Ts=function(e){return ys(e,NodeList)},Ss=ws,As=ks,Cs=Ts,Es=function(e){return ys(e,Element)},Ps=function(e){return ys(e,Event)},Ls=function(e){return bs(e)||(ws(e)||ks(e)||Ts(e))&&!e.length||vs(e)&&!Object.keys(e).length};function xs(e,t){if(1>t){var i=function(e){var t="".concat(e).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}(t);return parseFloat(e.toFixed(i))}return Math.round(e/t)*t}var Is=function(){function e(t,i){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),Es(t)?this.element=t:Ss(t)&&(this.element=document.querySelector(t)),Es(this.element)&&Ls(this.element.rangeTouch)&&(this.config=ps({},ms,{},i),this.init())}return function(e,t,i){t&&us(e.prototype,t),i&&us(e,i)}(e,[{key:"init",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect="none",this.element.style.webKitUserSelect="none",this.element.style.touchAction="manipulation"),this.listeners(!0),this.element.rangeTouch=this)}},{key:"destroy",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect="",this.element.style.webKitUserSelect="",this.element.style.touchAction=""),this.listeners(!1),this.element.rangeTouch=null)}},{key:"listeners",value:function(e){var t=this,i=e?"addEventListener":"removeEventListener";["touchstart","touchmove","touchend"].forEach((function(e){t.element[i](e,(function(e){return t.set(e)}),!1)}))}},{key:"get",value:function(t){if(!e.enabled||!Ps(t))return null;var i,n=t.target,s=t.changedTouches[0],r=parseFloat(n.getAttribute("min"))||0,a=parseFloat(n.getAttribute("max"))||100,o=parseFloat(n.getAttribute("step"))||1,l=n.getBoundingClientRect(),c=100/l.width*(this.config.thumbWidth/2)/100;return 0>(i=100/l.width*(s.clientX-l.left))?i=0:100<i&&(i=100),50>i?i-=(100-2*i)*c:50<i&&(i+=2*(i-50)*c),r+xs(i/100*(a-r),o)}},{key:"set",value:function(t){e.enabled&&Ps(t)&&!t.target.disabled&&(t.preventDefault(),t.target.value=this.get(t),function(e,t){if(e&&t){var i=new Event(t,{bubbles:!0});e.dispatchEvent(i)}}(t.target,"touchend"===t.type?"change":"input"))}}],[{key:"setup",value:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=null;if(Ls(t)||Ss(t)?n=Array.from(document.querySelectorAll(Ss(t)?t:'input[type="range"]')):Es(t)?n=[t]:Cs(t)?n=Array.from(t):As(t)&&(n=t.filter(Es)),Ls(n))return null;var s=ps({},ms,{},i);if(Ss(t)&&s.watch){var r=new MutationObserver((function(i){Array.from(i).forEach((function(i){Array.from(i.addedNodes).forEach((function(i){Es(i)&&fs(i,t)&&new e(i,s)}))}))}));r.observe(document.body,{childList:!0,subtree:!0})}return n.map((function(t){return new e(t,i)}))}},{key:"enabled",get:function(){return"ontouchstart"in document.documentElement}}]),e}();const Os=e=>null!=e?e.constructor:null,_s=(e,t)=>Boolean(e&&t&&e instanceof t),Ms=e=>null==e,Ns=e=>Os(e)===Object,js=e=>Os(e)===String,Rs=e=>Os(e)===Function,$s=e=>Array.isArray(e),qs=e=>_s(e,NodeList),Us=e=>Ms(e)||(js(e)||$s(e)||qs(e))&&!e.length||Ns(e)&&!Object.keys(e).length;var Fs=Ms,Ds=Ns,Hs=e=>Os(e)===Number&&!Number.isNaN(e),Bs=js,Vs=e=>Os(e)===Boolean,Ws=Rs,zs=$s,Ks=qs,Ys=e=>null!==e&&"object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument,Gs=e=>_s(e,Event),Xs=e=>_s(e,KeyboardEvent),Qs=e=>_s(e,TextTrack)||!Ms(e)&&js(e.kind),Js=e=>_s(e,Promise)&&Rs(e.then),Zs=e=>{if(_s(e,window.URL))return!0;if(!js(e))return!1;let t=e;e.startsWith("http://")&&e.startsWith("https://")||(t=`http://${e}`);try{return!Us(new URL(t).hostname)}catch(e){return!1}},er=Us;const tr=(()=>{const e=document.createElement("span"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"},i=Object.keys(t).find((t=>void 0!==e.style[t]));return!!Bs(i)&&t[i]})();function ir(e,t){setTimeout((()=>{try{e.hidden=!0,e.offsetHeight,e.hidden=!1}catch(e){}}),t)}const nr={isIE:Boolean(window.document.documentMode),isEdge:window.navigator.userAgent.includes("Edge"),isWebkit:"WebkitAppearance"in document.documentElement.style&&!/Edge/.test(navigator.userAgent),isIPhone:/(iPhone|iPod)/gi.test(navigator.platform),isIos:"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1||/(iPad|iPhone|iPod)/gi.test(navigator.platform)};function sr(e,t){return t.split(".").reduce(((e,t)=>e&&e[t]),e)}function rr(e={},...t){if(!t.length)return e;const i=t.shift();return Ds(i)?(Object.keys(i).forEach((t=>{Ds(i[t])?(Object.keys(e).includes(t)||Object.assign(e,{[t]:{}}),rr(e[t],i[t])):Object.assign(e,{[t]:i[t]})})),rr(e,...t)):e}function ar(e,t){const i=e.length?e:[e];Array.from(i).reverse().forEach(((e,i)=>{const n=i>0?t.cloneNode(!0):t,s=e.parentNode,r=e.nextSibling;n.appendChild(e),r?s.insertBefore(n,r):s.appendChild(n)}))}function or(e,t){Ys(e)&&!er(t)&&Object.entries(t).filter((([,e])=>!Fs(e))).forEach((([t,i])=>e.setAttribute(t,i)))}function lr(e,t,i){const n=document.createElement(e);return Ds(t)&&or(n,t),Bs(i)&&(n.innerText=i),n}function cr(e,t,i,n){Ys(t)&&t.appendChild(lr(e,i,n))}function ur(e){Ks(e)||zs(e)?Array.from(e).forEach(ur):Ys(e)&&Ys(e.parentNode)&&e.parentNode.removeChild(e)}function hr(e){if(!Ys(e))return;let{length:t}=e.childNodes;for(;t>0;)e.removeChild(e.lastChild),t-=1}function dr(e,t){return Ys(t)&&Ys(t.parentNode)&&Ys(e)?(t.parentNode.replaceChild(e,t),e):null}function pr(e,t){if(!Bs(e)||er(e))return{};const i={},n=rr({},t);return e.split(",").forEach((e=>{const t=e.trim(),s=t.replace(".",""),r=t.replace(/[[\]]/g,"").split("="),[a]=r,o=r.length>1?r[1].replace(/["']/g,""):"";switch(t.charAt(0)){case".":Bs(n.class)?i.class=`${n.class} ${s}`:i.class=s;break;case"#":i.id=t.replace("#","");break;case"[":i[a]=o}})),rr(n,i)}function mr(e,t){if(!Ys(e))return;let i=t;Vs(i)||(i=!e.hidden),e.hidden=i}function fr(e,t,i){if(Ks(e))return Array.from(e).map((e=>fr(e,t,i)));if(Ys(e)){let n="toggle";return void 0!==i&&(n=i?"add":"remove"),e.classList[n](t),e.classList.contains(t)}return!1}function gr(e,t){return Ys(e)&&e.classList.contains(t)}function yr(e,t){const{prototype:i}=Element;return(i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(){return Array.from(document.querySelectorAll(t)).includes(this)}).call(e,t)}function br(e){return this.elements.container.querySelectorAll(e)}function vr(e){return this.elements.container.querySelector(e)}function wr(e=null,t=!1){Ys(e)&&(e.focus({preventScroll:!0}),t&&fr(e,this.config.classNames.tabFocus))}const kr={"audio/ogg":"vorbis","audio/wav":"1","video/webm":"vp8, vorbis","video/mp4":"avc1.42E01E, mp4a.40.2","video/ogg":"theora"},Tr={audio:"canPlayType"in document.createElement("audio"),video:"canPlayType"in document.createElement("video"),check(e,t,i){const n=nr.isIPhone&&i&&Tr.playsinline,s=Tr[e]||"html5"!==t;return{api:s,ui:s&&Tr.rangeInput&&("video"!==e||!nr.isIPhone||n)}},pip:!(nr.isIPhone||!Ws(lr("video").webkitSetPresentationMode)&&(!document.pictureInPictureEnabled||lr("video").disablePictureInPicture)),airplay:Ws(window.WebKitPlaybackTargetAvailabilityEvent),playsinline:"playsInline"in document.createElement("video"),mime(e){if(er(e))return!1;const[t]=e.split("/");let i=e;if(!this.isHTML5||t!==this.type)return!1;Object.keys(kr).includes(i)&&(i+=`; codecs="${kr[e]}"`);try{return Boolean(i&&this.media.canPlayType(i).replace(/no/,""))}catch(e){return!1}},textTracks:"textTracks"in document.createElement("video"),rangeInput:(()=>{const e=document.createElement("input");return e.type="range","range"===e.type})(),touch:"ontouchstart"in document.documentElement,transitions:!1!==tr,reducedMotion:"matchMedia"in window&&window.matchMedia("(prefers-reduced-motion)").matches},Sr=(()=>{let e=!1;try{const t=Object.defineProperty({},"passive",{get:()=>(e=!0,null)});window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}return e})();function Ar(e,t,i,n=!1,s=!0,r=!1){if(!e||!("addEventListener"in e)||er(t)||!Ws(i))return;const a=t.split(" ");let o=r;Sr&&(o={passive:s,capture:r}),a.forEach((t=>{this&&this.eventListeners&&n&&this.eventListeners.push({element:e,type:t,callback:i,options:o}),e[n?"addEventListener":"removeEventListener"](t,i,o)}))}function Cr(e,t="",i,n=!0,s=!1){Ar.call(this,e,t,i,!0,n,s)}function Er(e,t="",i,n=!0,s=!1){Ar.call(this,e,t,i,!1,n,s)}function Pr(e,t="",i,n=!0,s=!1){const r=(...a)=>{Er(e,t,r,n,s),i.apply(this,a)};Ar.call(this,e,t,r,!0,n,s)}function Lr(e,t="",i=!1,n={}){if(!Ys(e)||er(t))return;const s=new CustomEvent(t,{bubbles:i,detail:{...n,plyr:this}});e.dispatchEvent(s)}function xr(){this&&this.eventListeners&&(this.eventListeners.forEach((e=>{const{element:t,type:i,callback:n,options:s}=e;t.removeEventListener(i,n,s)})),this.eventListeners=[])}function Ir(){return new Promise((e=>this.ready?setTimeout(e,0):Cr.call(this,this.elements.container,"ready",e))).then((()=>{}))}function Or(e){Js(e)&&e.then(null,(()=>{}))}function _r(e){return zs(e)?e.filter(((t,i)=>e.indexOf(t)===i)):e}function Mr(e,t){return zs(e)&&e.length?e.reduce(((e,i)=>Math.abs(i-t)<Math.abs(e-t)?i:e)):null}function Nr(e){return!(!window||!window.CSS)&&window.CSS.supports(e)}const jr=[[1,1],[4,3],[3,4],[5,4],[4,5],[3,2],[2,3],[16,10],[10,16],[16,9],[9,16],[21,9],[9,21],[32,9],[9,32]].reduce(((e,[t,i])=>({...e,[t/i]:[t,i]})),{});function Rr(e){if(!(zs(e)||Bs(e)&&e.includes(":")))return!1;return(zs(e)?e:e.split(":")).map(Number).every(Hs)}function $r(e){if(!zs(e)||!e.every(Hs))return null;const[t,i]=e,n=(e,t)=>0===t?e:n(t,e%t),s=n(t,i);return[t/s,i/s]}function qr(e){const t=e=>Rr(e)?e.split(":").map(Number):null;let i=t(e);if(null===i&&(i=t(this.config.ratio)),null===i&&!er(this.embed)&&zs(this.embed.ratio)&&({ratio:i}=this.embed),null===i&&this.isHTML5){const{videoWidth:e,videoHeight:t}=this.media;i=[e,t]}return $r(i)}function Ur(e){if(!this.isVideo)return{};const{wrapper:t}=this.elements,i=qr.call(this,e);if(!zs(i))return{};const[n,s]=$r(i),r=100/n*s;if(Nr(`aspect-ratio: ${n}/${s}`)?t.style.aspectRatio=`${n}/${s}`:t.style.paddingBottom=`${r}%`,this.isVimeo&&!this.config.vimeo.premium&&this.supported.ui){const e=100/this.media.offsetWidth*parseInt(window.getComputedStyle(this.media).paddingBottom,10),i=(e-r)/(e/50);this.fullscreen.active?t.style.paddingBottom=null:this.media.style.transform=`translateY(-${i}%)`}else this.isHTML5&&t.classList.add(this.config.classNames.videoFixedRatio);return{padding:r,ratio:i}}function Fr(e,t,i=.05){const n=e/t,s=Mr(Object.keys(jr),n);return Math.abs(s-n)<=i?jr[s]:[e,t]}const Dr={getSources(){if(!this.isHTML5)return[];return Array.from(this.media.querySelectorAll("source")).filter((e=>{const t=e.getAttribute("type");return!!er(t)||Tr.mime.call(this,t)}))},getQualityOptions(){return this.config.quality.forced?this.config.quality.options:Dr.getSources.call(this).map((e=>Number(e.getAttribute("size")))).filter(Boolean)},setup(){if(!this.isHTML5)return;const e=this;e.options.speed=e.config.speed.options,er(this.config.ratio)||Ur.call(e),Object.defineProperty(e.media,"quality",{get(){const t=Dr.getSources.call(e).find((t=>t.getAttribute("src")===e.source));return t&&Number(t.getAttribute("size"))},set(t){if(e.quality!==t){if(e.config.quality.forced&&Ws(e.config.quality.onChange))e.config.quality.onChange(t);else{const i=Dr.getSources.call(e).find((e=>Number(e.getAttribute("size"))===t));if(!i)return;const{currentTime:n,paused:s,preload:r,readyState:a,playbackRate:o}=e.media;e.media.src=i.getAttribute("src"),("none"!==r||a)&&(e.once("loadedmetadata",(()=>{e.speed=o,e.currentTime=n,s||Or(e.play())})),e.media.load())}Lr.call(e,e.media,"qualitychange",!1,{quality:t})}}})},cancelRequests(){this.isHTML5&&(ur(Dr.getSources.call(this)),this.media.setAttribute("src",this.config.blankVideo),this.media.load(),this.debug.log("Cancelled network requests"))}};function Hr(e,...t){return er(e)?e:e.toString().replace(/{(\d+)}/g,((e,i)=>t[i].toString()))}const Br=(e="",t="",i="")=>e.replace(new RegExp(t.toString().replace(/([.*+?^=!:${}()|[\]/\\])/g,"\\$1"),"g"),i.toString()),Vr=(e="")=>e.toString().replace(/\w\S*/g,(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()));function Wr(e=""){let t=e.toString();return t=function(e=""){let t=e.toString();return t=Br(t,"-"," "),t=Br(t,"_"," "),t=Vr(t),Br(t," ","")}(t),t.charAt(0).toLowerCase()+t.slice(1)}function zr(e){const t=document.createElement("div");return t.appendChild(e),t.innerHTML}const Kr={pip:"PIP",airplay:"AirPlay",html5:"HTML5",vimeo:"Vimeo",youtube:"YouTube"},Yr={get(e="",t={}){if(er(e)||er(t))return"";let i=sr(t.i18n,e);if(er(i))return Object.keys(Kr).includes(e)?Kr[e]:"";const n={"{seektime}":t.seekTime,"{title}":t.title};return Object.entries(n).forEach((([e,t])=>{i=Br(i,e,t)})),i}};class Gr{constructor(e){cs(this,"get",(e=>{if(!Gr.supported||!this.enabled)return null;const t=window.localStorage.getItem(this.key);if(er(t))return null;const i=JSON.parse(t);return Bs(e)&&e.length?i[e]:i})),cs(this,"set",(e=>{if(!Gr.supported||!this.enabled)return;if(!Ds(e))return;let t=this.get();er(t)&&(t={}),rr(t,e);try{window.localStorage.setItem(this.key,JSON.stringify(t))}catch(e){}})),this.enabled=e.config.storage.enabled,this.key=e.config.storage.key}static get supported(){try{if(!("localStorage"in window))return!1;const e="___test";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}}function Xr(e,t="text"){return new Promise(((i,n)=>{try{const n=new XMLHttpRequest;if(!("withCredentials"in n))return;n.addEventListener("load",(()=>{if("text"===t)try{i(JSON.parse(n.responseText))}catch(e){i(n.responseText)}else i(n.response)})),n.addEventListener("error",(()=>{throw new Error(n.status)})),n.open("GET",e,!0),n.responseType=t,n.send()}catch(e){n(e)}}))}function Qr(e,t){if(!Bs(e))return;const i=Bs(t);let n=!1;const s=()=>null!==document.getElementById(t),r=(e,t)=>{e.innerHTML=t,i&&s()||document.body.insertAdjacentElement("afterbegin",e)};if(!i||!s()){const s=Gr.supported,a=document.createElement("div");if(a.setAttribute("hidden",""),i&&a.setAttribute("id",t),s){const e=window.localStorage.getItem(`cache-${t}`);if(n=null!==e,n){const t=JSON.parse(e);r(a,t.content)}}Xr(e).then((e=>{if(!er(e)){if(s)try{window.localStorage.setItem(`cache-${t}`,JSON.stringify({content:e}))}catch(e){}r(a,e)}})).catch((()=>{}))}}const Jr=e=>Math.trunc(e/60/60%60,10);function Zr(e=0,t=!1,i=!1){if(!Hs(e))return Zr(void 0,t,i);const n=e=>`0${e}`.slice(-2);let s=Jr(e);const r=(a=e,Math.trunc(a/60%60,10));var a;const o=(e=>Math.trunc(e%60,10))(e);return s=t||s>0?`${s}:`:"",`${i&&e>0?"-":""}${s}${n(r)}:${n(o)}`}const ea={getIconUrl(){const e=new URL(this.config.iconUrl,window.location),t=window.location.host?window.location.host:window.top.location.host,i=e.host!==t||nr.isIE&&!window.svg4everybody;return{url:this.config.iconUrl,cors:i}},findElements(){try{return this.elements.controls=vr.call(this,this.config.selectors.controls.wrapper),this.elements.buttons={play:br.call(this,this.config.selectors.buttons.play),pause:vr.call(this,this.config.selectors.buttons.pause),restart:vr.call(this,this.config.selectors.buttons.restart),rewind:vr.call(this,this.config.selectors.buttons.rewind),fastForward:vr.call(this,this.config.selectors.buttons.fastForward),mute:vr.call(this,this.config.selectors.buttons.mute),pip:vr.call(this,this.config.selectors.buttons.pip),airplay:vr.call(this,this.config.selectors.buttons.airplay),settings:vr.call(this,this.config.selectors.buttons.settings),captions:vr.call(this,this.config.selectors.buttons.captions),fullscreen:vr.call(this,this.config.selectors.buttons.fullscreen)},this.elements.progress=vr.call(this,this.config.selectors.progress),this.elements.inputs={seek:vr.call(this,this.config.selectors.inputs.seek),volume:vr.call(this,this.config.selectors.inputs.volume)},this.elements.display={buffer:vr.call(this,this.config.selectors.display.buffer),currentTime:vr.call(this,this.config.selectors.display.currentTime),duration:vr.call(this,this.config.selectors.display.duration)},Ys(this.elements.progress)&&(this.elements.display.seekTooltip=this.elements.progress.querySelector(`.${this.config.classNames.tooltip}`)),!0}catch(e){return this.debug.warn("It looks like there is a problem with your custom controls HTML",e),this.toggleNativeControls(!0),!1}},createIcon(e,t){const i="http://www.w3.org/2000/svg",n=ea.getIconUrl.call(this),s=`${n.cors?"":n.url}#${this.config.iconPrefix}`,r=document.createElementNS(i,"svg");or(r,rr(t,{"aria-hidden":"true",focusable:"false"}));const a=document.createElementNS(i,"use"),o=`${s}-${e}`;return"href"in a&&a.setAttributeNS("http://www.w3.org/1999/xlink","href",o),a.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",o),r.appendChild(a),r},createLabel(e,t={}){const i=Yr.get(e,this.config);return lr("span",{...t,class:[t.class,this.config.classNames.hidden].filter(Boolean).join(" ")},i)},createBadge(e){if(er(e))return null;const t=lr("span",{class:this.config.classNames.menu.value});return t.appendChild(lr("span",{class:this.config.classNames.menu.badge},e)),t},createButton(e,t){const i=rr({},t);let n=Wr(e);const s={element:"button",toggle:!1,label:null,icon:null,labelPressed:null,iconPressed:null};switch(["element","icon","label"].forEach((e=>{Object.keys(i).includes(e)&&(s[e]=i[e],delete i[e])})),"button"!==s.element||Object.keys(i).includes("type")||(i.type="button"),Object.keys(i).includes("class")?i.class.split(" ").some((e=>e===this.config.classNames.control))||rr(i,{class:`${i.class} ${this.config.classNames.control}`}):i.class=this.config.classNames.control,e){case"play":s.toggle=!0,s.label="play",s.labelPressed="pause",s.icon="play",s.iconPressed="pause";break;case"mute":s.toggle=!0,s.label="mute",s.labelPressed="unmute",s.icon="volume",s.iconPressed="muted";break;case"captions":s.toggle=!0,s.label="enableCaptions",s.labelPressed="disableCaptions",s.icon="captions-off",s.iconPressed="captions-on";break;case"fullscreen":s.toggle=!0,s.label="enterFullscreen",s.labelPressed="exitFullscreen",s.icon="enter-fullscreen",s.iconPressed="exit-fullscreen";break;case"play-large":i.class+=` ${this.config.classNames.control}--overlaid`,n="play",s.label="play",s.icon="play";break;default:er(s.label)&&(s.label=n),er(s.icon)&&(s.icon=e)}const r=lr(s.element);return s.toggle?(r.appendChild(ea.createIcon.call(this,s.iconPressed,{class:"icon--pressed"})),r.appendChild(ea.createIcon.call(this,s.icon,{class:"icon--not-pressed"})),r.appendChild(ea.createLabel.call(this,s.labelPressed,{class:"label--pressed"})),r.appendChild(ea.createLabel.call(this,s.label,{class:"label--not-pressed"}))):(r.appendChild(ea.createIcon.call(this,s.icon)),r.appendChild(ea.createLabel.call(this,s.label))),rr(i,pr(this.config.selectors.buttons[n],i)),or(r,i),"play"===n?(zs(this.elements.buttons[n])||(this.elements.buttons[n]=[]),this.elements.buttons[n].push(r)):this.elements.buttons[n]=r,r},createRange(e,t){const i=lr("input",rr(pr(this.config.selectors.inputs[e]),{type:"range",min:0,max:100,step:.01,value:0,autocomplete:"off",role:"slider","aria-label":Yr.get(e,this.config),"aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":0},t));return this.elements.inputs[e]=i,ea.updateRangeFill.call(this,i),Is.setup(i),i},createProgress(e,t){const i=lr("progress",rr(pr(this.config.selectors.display[e]),{min:0,max:100,value:0,role:"progressbar","aria-hidden":!0},t));if("volume"!==e){i.appendChild(lr("span",null,"0"));const t={played:"played",buffer:"buffered"}[e],n=t?Yr.get(t,this.config):"";i.innerText=`% ${n.toLowerCase()}`}return this.elements.display[e]=i,i},createTime(e,t){const i=pr(this.config.selectors.display[e],t),n=lr("div",rr(i,{class:`${i.class?i.class:""} ${this.config.classNames.display.time} `.trim(),"aria-label":Yr.get(e,this.config)}),"00:00");return this.elements.display[e]=n,n},bindMenuItemShortcuts(e,t){Cr.call(this,e,"keydown keyup",(i=>{if(![32,38,39,40].includes(i.which))return;if(i.preventDefault(),i.stopPropagation(),"keydown"===i.type)return;const n=yr(e,'[role="menuitemradio"]');if(!n&&[32,39].includes(i.which))ea.showMenuPanel.call(this,t,!0);else{let t;32!==i.which&&(40===i.which||n&&39===i.which?(t=e.nextElementSibling,Ys(t)||(t=e.parentNode.firstElementChild)):(t=e.previousElementSibling,Ys(t)||(t=e.parentNode.lastElementChild)),wr.call(this,t,!0))}}),!1),Cr.call(this,e,"keyup",(e=>{13===e.which&&ea.focusFirstMenuItem.call(this,null,!0)}))},createMenuItem({value:e,list:t,type:i,title:n,badge:s=null,checked:r=!1}){const a=pr(this.config.selectors.inputs[i]),o=lr("button",rr(a,{type:"button",role:"menuitemradio",class:`${this.config.classNames.control} ${a.class?a.class:""}`.trim(),"aria-checked":r,value:e})),l=lr("span");l.innerHTML=n,Ys(s)&&l.appendChild(s),o.appendChild(l),Object.defineProperty(o,"checked",{enumerable:!0,get:()=>"true"===o.getAttribute("aria-checked"),set(e){e&&Array.from(o.parentNode.children).filter((e=>yr(e,'[role="menuitemradio"]'))).forEach((e=>e.setAttribute("aria-checked","false"))),o.setAttribute("aria-checked",e?"true":"false")}}),this.listeners.bind(o,"click keyup",(t=>{if(!Xs(t)||32===t.which){switch(t.preventDefault(),t.stopPropagation(),o.checked=!0,i){case"language":this.currentTrack=Number(e);break;case"quality":this.quality=e;break;case"speed":this.speed=parseFloat(e)}ea.showMenuPanel.call(this,"home",Xs(t))}}),i,!1),ea.bindMenuItemShortcuts.call(this,o,i),t.appendChild(o)},formatTime(e=0,t=!1){if(!Hs(e))return e;return Zr(e,Jr(this.duration)>0,t)},updateTimeDisplay(e=null,t=0,i=!1){Ys(e)&&Hs(t)&&(e.innerText=ea.formatTime(t,i))},updateVolume(){this.supported.ui&&(Ys(this.elements.inputs.volume)&&ea.setRange.call(this,this.elements.inputs.volume,this.muted?0:this.volume),Ys(this.elements.buttons.mute)&&(this.elements.buttons.mute.pressed=this.muted||0===this.volume))},setRange(e,t=0){Ys(e)&&(e.value=t,ea.updateRangeFill.call(this,e))},updateProgress(e){if(!this.supported.ui||!Gs(e))return;let t=0;const i=(e,t)=>{const i=Hs(t)?t:0,n=Ys(e)?e:this.elements.display.buffer;if(Ys(n)){n.value=i;const e=n.getElementsByTagName("span")[0];Ys(e)&&(e.childNodes[0].nodeValue=i)}};if(e)switch(e.type){case"timeupdate":case"seeking":case"seeked":t=function(e,t){return 0===e||0===t||Number.isNaN(e)||Number.isNaN(t)?0:(e/t*100).toFixed(2)}(this.currentTime,this.duration),"timeupdate"===e.type&&ea.setRange.call(this,this.elements.inputs.seek,t);break;case"playing":case"progress":i(this.elements.display.buffer,100*this.buffered)}},updateRangeFill(e){const t=Gs(e)?e.target:e;if(Ys(t)&&"range"===t.getAttribute("type")){if(yr(t,this.config.selectors.inputs.seek)){t.setAttribute("aria-valuenow",this.currentTime);const e=ea.formatTime(this.currentTime),i=ea.formatTime(this.duration),n=Yr.get("seekLabel",this.config);t.setAttribute("aria-valuetext",n.replace("{currentTime}",e).replace("{duration}",i))}else if(yr(t,this.config.selectors.inputs.volume)){const e=100*t.value;t.setAttribute("aria-valuenow",e),t.setAttribute("aria-valuetext",`${e.toFixed(1)}%`)}else t.setAttribute("aria-valuenow",t.value);nr.isWebkit&&t.style.setProperty("--value",t.value/t.max*100+"%")}},updateSeekTooltip(e){if(!this.config.tooltips.seek||!Ys(this.elements.inputs.seek)||!Ys(this.elements.display.seekTooltip)||0===this.duration)return;const t=`${this.config.classNames.tooltip}--visible`,i=e=>fr(this.elements.display.seekTooltip,t,e);if(this.touch)return void i(!1);let n=0;const s=this.elements.progress.getBoundingClientRect();if(Gs(e))n=100/s.width*(e.pageX-s.left);else{if(!gr(this.elements.display.seekTooltip,t))return;n=parseFloat(this.elements.display.seekTooltip.style.left,10)}n<0?n=0:n>100&&(n=100),ea.updateTimeDisplay.call(this,this.elements.display.seekTooltip,this.duration/100*n),this.elements.display.seekTooltip.style.left=`${n}%`,Gs(e)&&["mouseenter","mouseleave"].includes(e.type)&&i("mouseenter"===e.type)},timeUpdate(e){const t=!Ys(this.elements.display.duration)&&this.config.invertTime;ea.updateTimeDisplay.call(this,this.elements.display.currentTime,t?this.duration-this.currentTime:this.currentTime,t),e&&"timeupdate"===e.type&&this.media.seeking||ea.updateProgress.call(this,e)},durationUpdate(){if(!this.supported.ui||!this.config.invertTime&&this.currentTime)return;if(this.duration>=2**32)return mr(this.elements.display.currentTime,!0),void mr(this.elements.progress,!0);Ys(this.elements.inputs.seek)&&this.elements.inputs.seek.setAttribute("aria-valuemax",this.duration);const e=Ys(this.elements.display.duration);!e&&this.config.displayDuration&&this.paused&&ea.updateTimeDisplay.call(this,this.elements.display.currentTime,this.duration),e&&ea.updateTimeDisplay.call(this,this.elements.display.duration,this.duration),ea.updateSeekTooltip.call(this)},toggleMenuButton(e,t){mr(this.elements.settings.buttons[e],!t)},updateSetting(e,t,i){const n=this.elements.settings.panels[e];let s=null,r=t;if("captions"===e)s=this.currentTrack;else{if(s=er(i)?this[e]:i,er(s)&&(s=this.config[e].default),!er(this.options[e])&&!this.options[e].includes(s))return void this.debug.warn(`Unsupported value of '${s}' for ${e}`);if(!this.config[e].options.includes(s))return void this.debug.warn(`Disabled value of '${s}' for ${e}`)}if(Ys(r)||(r=n&&n.querySelector('[role="menu"]')),!Ys(r))return;this.elements.settings.buttons[e].querySelector(`.${this.config.classNames.menu.value}`).innerHTML=ea.getLabel.call(this,e,s);const a=r&&r.querySelector(`[value="${s}"]`);Ys(a)&&(a.checked=!0)},getLabel(e,t){switch(e){case"speed":return 1===t?Yr.get("normal",this.config):`${t}&times;`;case"quality":if(Hs(t)){const e=Yr.get(`qualityLabel.${t}`,this.config);return e.length?e:`${t}p`}return Vr(t);case"captions":return na.getLabel.call(this);default:return null}},setQualityMenu(e){if(!Ys(this.elements.settings.panels.quality))return;const t="quality",i=this.elements.settings.panels.quality.querySelector('[role="menu"]');zs(e)&&(this.options.quality=_r(e).filter((e=>this.config.quality.options.includes(e))));const n=!er(this.options.quality)&&this.options.quality.length>1;if(ea.toggleMenuButton.call(this,t,n),hr(i),ea.checkMenu.call(this),!n)return;const s=e=>{const t=Yr.get(`qualityBadge.${e}`,this.config);return t.length?ea.createBadge.call(this,t):null};this.options.quality.sort(((e,t)=>{const i=this.config.quality.options;return i.indexOf(e)>i.indexOf(t)?1:-1})).forEach((e=>{ea.createMenuItem.call(this,{value:e,list:i,type:t,title:ea.getLabel.call(this,"quality",e),badge:s(e)})})),ea.updateSetting.call(this,t,i)},setCaptionsMenu(){if(!Ys(this.elements.settings.panels.captions))return;const e="captions",t=this.elements.settings.panels.captions.querySelector('[role="menu"]'),i=na.getTracks.call(this),n=Boolean(i.length);if(ea.toggleMenuButton.call(this,e,n),hr(t),ea.checkMenu.call(this),!n)return;const s=i.map(((e,i)=>({value:i,checked:this.captions.toggled&&this.currentTrack===i,title:na.getLabel.call(this,e),badge:e.language&&ea.createBadge.call(this,e.language.toUpperCase()),list:t,type:"language"})));s.unshift({value:-1,checked:!this.captions.toggled,title:Yr.get("disabled",this.config),list:t,type:"language"}),s.forEach(ea.createMenuItem.bind(this)),ea.updateSetting.call(this,e,t)},setSpeedMenu(){if(!Ys(this.elements.settings.panels.speed))return;const e="speed",t=this.elements.settings.panels.speed.querySelector('[role="menu"]');this.options.speed=this.options.speed.filter((e=>e>=this.minimumSpeed&&e<=this.maximumSpeed));const i=!er(this.options.speed)&&this.options.speed.length>1;ea.toggleMenuButton.call(this,e,i),hr(t),ea.checkMenu.call(this),i&&(this.options.speed.forEach((i=>{ea.createMenuItem.call(this,{value:i,list:t,type:e,title:ea.getLabel.call(this,"speed",i)})})),ea.updateSetting.call(this,e,t))},checkMenu(){const{buttons:e}=this.elements.settings,t=!er(e)&&Object.values(e).some((e=>!e.hidden));mr(this.elements.settings.menu,!t)},focusFirstMenuItem(e,t=!1){if(this.elements.settings.popup.hidden)return;let i=e;Ys(i)||(i=Object.values(this.elements.settings.panels).find((e=>!e.hidden)));const n=i.querySelector('[role^="menuitem"]');wr.call(this,n,t)},toggleMenu(e){const{popup:t}=this.elements.settings,i=this.elements.buttons.settings;if(!Ys(t)||!Ys(i))return;const{hidden:n}=t;let s=n;if(Vs(e))s=e;else if(Xs(e)&&27===e.which)s=!1;else if(Gs(e)){const n=Ws(e.composedPath)?e.composedPath()[0]:e.target,r=t.contains(n);if(r||!r&&e.target!==i&&s)return}i.setAttribute("aria-expanded",s),mr(t,!s),fr(this.elements.container,this.config.classNames.menu.open,s),s&&Xs(e)?ea.focusFirstMenuItem.call(this,null,!0):s||n||wr.call(this,i,Xs(e))},getMenuSize(e){const t=e.cloneNode(!0);t.style.position="absolute",t.style.opacity=0,t.removeAttribute("hidden"),e.parentNode.appendChild(t);const i=t.scrollWidth,n=t.scrollHeight;return ur(t),{width:i,height:n}},showMenuPanel(e="",t=!1){const i=this.elements.container.querySelector(`#plyr-settings-${this.id}-${e}`);if(!Ys(i))return;const n=i.parentNode,s=Array.from(n.children).find((e=>!e.hidden));if(Tr.transitions&&!Tr.reducedMotion){n.style.width=`${s.scrollWidth}px`,n.style.height=`${s.scrollHeight}px`;const e=ea.getMenuSize.call(this,i),t=e=>{e.target===n&&["width","height"].includes(e.propertyName)&&(n.style.width="",n.style.height="",Er.call(this,n,tr,t))};Cr.call(this,n,tr,t),n.style.width=`${e.width}px`,n.style.height=`${e.height}px`}mr(s,!0),mr(i,!1),ea.focusFirstMenuItem.call(this,i,t)},setDownloadUrl(){const e=this.elements.buttons.download;Ys(e)&&e.setAttribute("href",this.download)},create(e){const{bindMenuItemShortcuts:t,createButton:i,createProgress:n,createRange:s,createTime:r,setQualityMenu:a,setSpeedMenu:o,showMenuPanel:l}=ea;this.elements.controls=null,zs(this.config.controls)&&this.config.controls.includes("play-large")&&this.elements.container.appendChild(i.call(this,"play-large"));const c=lr("div",pr(this.config.selectors.controls.wrapper));this.elements.controls=c;const u={class:"plyr__controls__item"};return _r(zs(this.config.controls)?this.config.controls:[]).forEach((a=>{if("restart"===a&&c.appendChild(i.call(this,"restart",u)),"rewind"===a&&c.appendChild(i.call(this,"rewind",u)),"play"===a&&c.appendChild(i.call(this,"play",u)),"fast-forward"===a&&c.appendChild(i.call(this,"fast-forward",u)),"progress"===a){const t=lr("div",{class:`${u.class} plyr__progress__container`}),i=lr("div",pr(this.config.selectors.progress));if(i.appendChild(s.call(this,"seek",{id:`plyr-seek-${e.id}`})),i.appendChild(n.call(this,"buffer")),this.config.tooltips.seek){const e=lr("span",{class:this.config.classNames.tooltip},"00:00");i.appendChild(e),this.elements.display.seekTooltip=e}this.elements.progress=i,t.appendChild(this.elements.progress),c.appendChild(t)}if("current-time"===a&&c.appendChild(r.call(this,"currentTime",u)),"duration"===a&&c.appendChild(r.call(this,"duration",u)),"mute"===a||"volume"===a){let{volume:t}=this.elements;if(Ys(t)&&c.contains(t)||(t=lr("div",rr({},u,{class:`${u.class} plyr__volume`.trim()})),this.elements.volume=t,c.appendChild(t)),"mute"===a&&t.appendChild(i.call(this,"mute")),"volume"===a&&!nr.isIos){const i={max:1,step:.05,value:this.config.volume};t.appendChild(s.call(this,"volume",rr(i,{id:`plyr-volume-${e.id}`})))}}if("captions"===a&&c.appendChild(i.call(this,"captions",u)),"settings"===a&&!er(this.config.settings)){const n=lr("div",rr({},u,{class:`${u.class} plyr__menu`.trim(),hidden:""}));n.appendChild(i.call(this,"settings",{"aria-haspopup":!0,"aria-controls":`plyr-settings-${e.id}`,"aria-expanded":!1}));const s=lr("div",{class:"plyr__menu__container",id:`plyr-settings-${e.id}`,hidden:""}),r=lr("div"),a=lr("div",{id:`plyr-settings-${e.id}-home`}),o=lr("div",{role:"menu"});a.appendChild(o),r.appendChild(a),this.elements.settings.panels.home=a,this.config.settings.forEach((i=>{const n=lr("button",rr(pr(this.config.selectors.buttons.settings),{type:"button",class:`${this.config.classNames.control} ${this.config.classNames.control}--forward`,role:"menuitem","aria-haspopup":!0,hidden:""}));t.call(this,n,i),Cr.call(this,n,"click",(()=>{l.call(this,i,!1)}));const s=lr("span",null,Yr.get(i,this.config)),a=lr("span",{class:this.config.classNames.menu.value});a.innerHTML=e[i],s.appendChild(a),n.appendChild(s),o.appendChild(n);const c=lr("div",{id:`plyr-settings-${e.id}-${i}`,hidden:""}),u=lr("button",{type:"button",class:`${this.config.classNames.control} ${this.config.classNames.control}--back`});u.appendChild(lr("span",{"aria-hidden":!0},Yr.get(i,this.config))),u.appendChild(lr("span",{class:this.config.classNames.hidden},Yr.get("menuBack",this.config))),Cr.call(this,c,"keydown",(e=>{37===e.which&&(e.preventDefault(),e.stopPropagation(),l.call(this,"home",!0))}),!1),Cr.call(this,u,"click",(()=>{l.call(this,"home",!1)})),c.appendChild(u),c.appendChild(lr("div",{role:"menu"})),r.appendChild(c),this.elements.settings.buttons[i]=n,this.elements.settings.panels[i]=c})),s.appendChild(r),n.appendChild(s),c.appendChild(n),this.elements.settings.popup=s,this.elements.settings.menu=n}if("pip"===a&&Tr.pip&&c.appendChild(i.call(this,"pip",u)),"airplay"===a&&Tr.airplay&&c.appendChild(i.call(this,"airplay",u)),"download"===a){const e=rr({},u,{element:"a",href:this.download,target:"_blank"});this.isHTML5&&(e.download="");const{download:t}=this.config.urls;!Zs(t)&&this.isEmbed&&rr(e,{icon:`logo-${this.provider}`,label:this.provider}),c.appendChild(i.call(this,"download",e))}"fullscreen"===a&&c.appendChild(i.call(this,"fullscreen",u))})),this.isHTML5&&a.call(this,Dr.getQualityOptions.call(this)),o.call(this),c},inject(){if(this.config.loadSprite){const e=ea.getIconUrl.call(this);e.cors&&Qr(e.url,"sprite-plyr")}this.id=Math.floor(1e4*Math.random());let e=null;this.elements.controls=null;const t={id:this.id,seektime:this.config.seekTime,title:this.config.title};let i=!0;Ws(this.config.controls)&&(this.config.controls=this.config.controls.call(this,t)),this.config.controls||(this.config.controls=[]),Ys(this.config.controls)||Bs(this.config.controls)?e=this.config.controls:(e=ea.create.call(this,{id:this.id,seektime:this.config.seekTime,speed:this.speed,quality:this.quality,captions:na.getLabel.call(this)}),i=!1);let n;i&&Bs(this.config.controls)&&(e=(e=>{let i=e;return Object.entries(t).forEach((([e,t])=>{i=Br(i,`{${e}}`,t)})),i})(e)),Bs(this.config.selectors.controls.container)&&(n=document.querySelector(this.config.selectors.controls.container)),Ys(n)||(n=this.elements.container);if(n[Ys(e)?"insertAdjacentElement":"insertAdjacentHTML"]("afterbegin",e),Ys(this.elements.controls)||ea.findElements.call(this),!er(this.elements.buttons)){const e=e=>{const t=this.config.classNames.controlPressed;Object.defineProperty(e,"pressed",{enumerable:!0,get:()=>gr(e,t),set(i=!1){fr(e,t,i)}})};Object.values(this.elements.buttons).filter(Boolean).forEach((t=>{zs(t)||Ks(t)?Array.from(t).filter(Boolean).forEach(e):e(t)}))}if(nr.isEdge&&ir(n),this.config.tooltips.controls){const{classNames:e,selectors:t}=this.config,i=`${t.controls.wrapper} ${t.labels} .${e.hidden}`,n=br.call(this,i);Array.from(n).forEach((e=>{fr(e,this.config.classNames.hidden,!1),fr(e,this.config.classNames.tooltip,!0)}))}}};function ta(e,t=!0){let i=e;if(t){const e=document.createElement("a");e.href=i,i=e.href}try{return new URL(i)}catch(e){return null}}function ia(e){const t=new URLSearchParams;return Ds(e)&&Object.entries(e).forEach((([e,i])=>{t.set(e,i)})),t}const na={setup(){if(!this.supported.ui)return;if(!this.isVideo||this.isYouTube||this.isHTML5&&!Tr.textTracks)return void(zs(this.config.controls)&&this.config.controls.includes("settings")&&this.config.settings.includes("captions")&&ea.setCaptionsMenu.call(this));var e,t;if(Ys(this.elements.captions)||(this.elements.captions=lr("div",pr(this.config.selectors.captions)),e=this.elements.captions,t=this.elements.wrapper,Ys(e)&&Ys(t)&&t.parentNode.insertBefore(e,t.nextSibling)),nr.isIE&&window.URL){const e=this.media.querySelectorAll("track");Array.from(e).forEach((e=>{const t=e.getAttribute("src"),i=ta(t);null!==i&&i.hostname!==window.location.href.hostname&&["http:","https:"].includes(i.protocol)&&Xr(t,"blob").then((t=>{e.setAttribute("src",window.URL.createObjectURL(t))})).catch((()=>{ur(e)}))}))}const i=_r((navigator.languages||[navigator.language||navigator.userLanguage||"en"]).map((e=>e.split("-")[0])));let n=(this.storage.get("language")||this.config.captions.language||"auto").toLowerCase();"auto"===n&&([n]=i);let s=this.storage.get("captions");if(Vs(s)||({active:s}=this.config.captions),Object.assign(this.captions,{toggled:!1,active:s,language:n,languages:i}),this.isHTML5){const e=this.config.captions.update?"addtrack removetrack":"removetrack";Cr.call(this,this.media.textTracks,e,na.update.bind(this))}setTimeout(na.update.bind(this),0)},update(){const e=na.getTracks.call(this,!0),{active:t,language:i,meta:n,currentTrackNode:s}=this.captions,r=Boolean(e.find((e=>e.language===i)));this.isHTML5&&this.isVideo&&e.filter((e=>!n.get(e))).forEach((e=>{this.debug.log("Track added",e),n.set(e,{default:"showing"===e.mode}),"showing"===e.mode&&(e.mode="hidden"),Cr.call(this,e,"cuechange",(()=>na.updateCues.call(this)))})),(r&&this.language!==i||!e.includes(s))&&(na.setLanguage.call(this,i),na.toggle.call(this,t&&r)),this.elements&&fr(this.elements.container,this.config.classNames.captions.enabled,!er(e)),zs(this.config.controls)&&this.config.controls.includes("settings")&&this.config.settings.includes("captions")&&ea.setCaptionsMenu.call(this)},toggle(e,t=!0){if(!this.supported.ui)return;const{toggled:i}=this.captions,n=this.config.classNames.captions.active,s=Fs(e)?!i:e;if(s!==i){if(t||(this.captions.active=s,this.storage.set({captions:s})),!this.language&&s&&!t){const e=na.getTracks.call(this),t=na.findTrack.call(this,[this.captions.language,...this.captions.languages],!0);return this.captions.language=t.language,void na.set.call(this,e.indexOf(t))}this.elements.buttons.captions&&(this.elements.buttons.captions.pressed=s),fr(this.elements.container,n,s),this.captions.toggled=s,ea.updateSetting.call(this,"captions"),Lr.call(this,this.media,s?"captionsenabled":"captionsdisabled")}setTimeout((()=>{s&&this.captions.toggled&&(this.captions.currentTrackNode.mode="hidden")}))},set(e,t=!0){const i=na.getTracks.call(this);if(-1!==e)if(Hs(e))if(e in i){if(this.captions.currentTrack!==e){this.captions.currentTrack=e;const n=i[e],{language:s}=n||{};this.captions.currentTrackNode=n,ea.updateSetting.call(this,"captions"),t||(this.captions.language=s,this.storage.set({language:s})),this.isVimeo&&this.embed.enableTextTrack(s),Lr.call(this,this.media,"languagechange")}na.toggle.call(this,!0,t),this.isHTML5&&this.isVideo&&na.updateCues.call(this)}else this.debug.warn("Track not found",e);else this.debug.warn("Invalid caption argument",e);else na.toggle.call(this,!1,t)},setLanguage(e,t=!0){if(!Bs(e))return void this.debug.warn("Invalid language argument",e);const i=e.toLowerCase();this.captions.language=i;const n=na.getTracks.call(this),s=na.findTrack.call(this,[i]);na.set.call(this,n.indexOf(s),t)},getTracks(e=!1){return Array.from((this.media||{}).textTracks||[]).filter((t=>!this.isHTML5||e||this.captions.meta.has(t))).filter((e=>["captions","subtitles"].includes(e.kind)))},findTrack(e,t=!1){const i=na.getTracks.call(this),n=e=>Number((this.captions.meta.get(e)||{}).default),s=Array.from(i).sort(((e,t)=>n(t)-n(e)));let r;return e.every((e=>(r=s.find((t=>t.language===e)),!r))),r||(t?s[0]:void 0)},getCurrentTrack(){return na.getTracks.call(this)[this.currentTrack]},getLabel(e){let t=e;return!Qs(t)&&Tr.textTracks&&this.captions.toggled&&(t=na.getCurrentTrack.call(this)),Qs(t)?er(t.label)?er(t.language)?Yr.get("enabled",this.config):e.language.toUpperCase():t.label:Yr.get("disabled",this.config)},updateCues(e){if(!this.supported.ui)return;if(!Ys(this.elements.captions))return void this.debug.warn("No captions element to render to");if(!Fs(e)&&!Array.isArray(e))return void this.debug.warn("updateCues: Invalid input",e);let t=e;if(!t){const e=na.getCurrentTrack.call(this);t=Array.from((e||{}).activeCues||[]).map((e=>e.getCueAsHTML())).map(zr)}const i=t.map((e=>e.trim())).join("\n");if(i!==this.elements.captions.innerHTML){hr(this.elements.captions);const e=lr("span",pr(this.config.selectors.caption));e.innerHTML=i,this.elements.captions.appendChild(e),Lr.call(this,this.media,"cuechange")}}},sa={enabled:!0,title:"",debug:!1,autoplay:!1,autopause:!0,playsinline:!0,seekTime:10,volume:1,muted:!1,duration:null,displayDuration:!0,invertTime:!0,toggleInvert:!0,ratio:null,clickToPlay:!0,hideControls:!0,resetOnEnd:!1,disableContextMenu:!0,loadSprite:!0,iconPrefix:"plyr",iconUrl:"https://cdn.plyr.io/3.6.9/plyr.svg",blankVideo:"https://cdn.plyr.io/static/blank.mp4",quality:{default:576,options:[4320,2880,2160,1440,1080,720,576,480,360,240],forced:!1,onChange:null},loop:{active:!1},speed:{selected:1,options:[.5,.75,1,1.25,1.5,1.75,2,4]},keyboard:{focused:!0,global:!1},tooltips:{controls:!1,seek:!0},captions:{active:!1,language:"auto",update:!1},fullscreen:{enabled:!0,fallback:!0,iosNative:!1},storage:{enabled:!0,key:"plyr"},controls:["play-large","play","progress","current-time","mute","volume","captions","settings","pip","airplay","fullscreen"],settings:["captions","quality","speed"],i18n:{restart:"Restart",rewind:"Rewind {seektime}s",play:"Play",pause:"Pause",fastForward:"Forward {seektime}s",seek:"Seek",seekLabel:"{currentTime} of {duration}",played:"Played",buffered:"Buffered",currentTime:"Current time",duration:"Duration",volume:"Volume",mute:"Mute",unmute:"Unmute",enableCaptions:"Enable captions",disableCaptions:"Disable captions",download:"Download",enterFullscreen:"Enter fullscreen",exitFullscreen:"Exit fullscreen",frameTitle:"Player for {title}",captions:"Captions",settings:"Settings",pip:"PIP",menuBack:"Go back to previous menu",speed:"Speed",normal:"Normal",quality:"Quality",loop:"Loop",start:"Start",end:"End",all:"All",reset:"Reset",disabled:"Disabled",enabled:"Enabled",advertisement:"Ad",qualityBadge:{2160:"4K",1440:"HD",1080:"HD",720:"HD",576:"SD",480:"SD"}},urls:{download:null,vimeo:{sdk:"https://player.vimeo.com/api/player.js",iframe:"https://player.vimeo.com/video/{0}?{1}",api:"https://vimeo.com/api/oembed.json?url={0}"},youtube:{sdk:"https://www.youtube.com/iframe_api",api:"https://noembed.com/embed?url=https://www.youtube.com/watch?v={0}"},googleIMA:{sdk:"https://imasdk.googleapis.com/js/sdkloader/ima3.js"}},listeners:{seek:null,play:null,pause:null,restart:null,rewind:null,fastForward:null,mute:null,volume:null,captions:null,download:null,fullscreen:null,pip:null,airplay:null,speed:null,quality:null,loop:null,language:null},events:["ended","progress","stalled","playing","waiting","canplay","canplaythrough","loadstart","loadeddata","loadedmetadata","timeupdate","volumechange","play","pause","error","seeking","seeked","emptied","ratechange","cuechange","download","enterfullscreen","exitfullscreen","captionsenabled","captionsdisabled","languagechange","controlshidden","controlsshown","ready","statechange","qualitychange","adsloaded","adscontentpause","adscontentresume","adstarted","adsmidpoint","adscomplete","adsallcomplete","adsimpression","adsclick"],selectors:{editable:"input, textarea, select, [contenteditable]",container:".plyr",controls:{container:null,wrapper:".plyr__controls"},labels:"[data-plyr]",buttons:{play:'[data-plyr="play"]',pause:'[data-plyr="pause"]',restart:'[data-plyr="restart"]',rewind:'[data-plyr="rewind"]',fastForward:'[data-plyr="fast-forward"]',mute:'[data-plyr="mute"]',captions:'[data-plyr="captions"]',download:'[data-plyr="download"]',fullscreen:'[data-plyr="fullscreen"]',pip:'[data-plyr="pip"]',airplay:'[data-plyr="airplay"]',settings:'[data-plyr="settings"]',loop:'[data-plyr="loop"]'},inputs:{seek:'[data-plyr="seek"]',volume:'[data-plyr="volume"]',speed:'[data-plyr="speed"]',language:'[data-plyr="language"]',quality:'[data-plyr="quality"]'},display:{currentTime:".plyr__time--current",duration:".plyr__time--duration",buffer:".plyr__progress__buffer",loop:".plyr__progress__loop",volume:".plyr__volume--display"},progress:".plyr__progress",captions:".plyr__captions",caption:".plyr__caption"},classNames:{type:"plyr--{0}",provider:"plyr--{0}",video:"plyr__video-wrapper",embed:"plyr__video-embed",videoFixedRatio:"plyr__video-wrapper--fixed-ratio",embedContainer:"plyr__video-embed__container",poster:"plyr__poster",posterEnabled:"plyr__poster-enabled",ads:"plyr__ads",control:"plyr__control",controlPressed:"plyr__control--pressed",playing:"plyr--playing",paused:"plyr--paused",stopped:"plyr--stopped",loading:"plyr--loading",hover:"plyr--hover",tooltip:"plyr__tooltip",cues:"plyr__cues",hidden:"plyr__sr-only",hideControls:"plyr--hide-controls",isIos:"plyr--is-ios",isTouch:"plyr--is-touch",uiSupported:"plyr--full-ui",noTransition:"plyr--no-transition",display:{time:"plyr__time"},menu:{value:"plyr__menu__value",badge:"plyr__badge",open:"plyr--menu-open"},captions:{enabled:"plyr--captions-enabled",active:"plyr--captions-active"},fullscreen:{enabled:"plyr--fullscreen-enabled",fallback:"plyr--fullscreen-fallback"},pip:{supported:"plyr--pip-supported",active:"plyr--pip-active"},airplay:{supported:"plyr--airplay-supported",active:"plyr--airplay-active"},tabFocus:"plyr__tab-focus",previewThumbnails:{thumbContainer:"plyr__preview-thumb",thumbContainerShown:"plyr__preview-thumb--is-shown",imageContainer:"plyr__preview-thumb__image-container",timeContainer:"plyr__preview-thumb__time-container",scrubbingContainer:"plyr__preview-scrubbing",scrubbingContainerShown:"plyr__preview-scrubbing--is-shown"}},attributes:{embed:{provider:"data-plyr-provider",id:"data-plyr-embed-id",hash:"data-plyr-embed-hash"}},ads:{enabled:!1,publisherId:"",tagUrl:""},previewThumbnails:{enabled:!1,src:""},vimeo:{byline:!1,portrait:!1,title:!1,speed:!0,transparent:!1,customControls:!0,referrerPolicy:null,premium:!1},youtube:{rel:0,showinfo:0,iv_load_policy:3,modestbranding:1,customControls:!0,noCookie:!1}},ra="picture-in-picture",aa="inline",oa={html5:"html5",youtube:"youtube",vimeo:"vimeo"},la="audio",ca="video";const ua=()=>{};class ha{constructor(e=!1){this.enabled=window.console&&e,this.enabled&&this.log("Debugging enabled")}get log(){return this.enabled?Function.prototype.bind.call(console.log,console):ua}get warn(){return this.enabled?Function.prototype.bind.call(console.warn,console):ua}get error(){return this.enabled?Function.prototype.bind.call(console.error,console):ua}}class da{constructor(e){cs(this,"onChange",(()=>{if(!this.enabled)return;const e=this.player.elements.buttons.fullscreen;Ys(e)&&(e.pressed=this.active);const t=this.target===this.player.media?this.target:this.player.elements.container;Lr.call(this.player,t,this.active?"enterfullscreen":"exitfullscreen",!0)})),cs(this,"toggleFallback",((e=!1)=>{if(e?this.scrollPosition={x:window.scrollX||0,y:window.scrollY||0}:window.scrollTo(this.scrollPosition.x,this.scrollPosition.y),document.body.style.overflow=e?"hidden":"",fr(this.target,this.player.config.classNames.fullscreen.fallback,e),nr.isIos){let t=document.head.querySelector('meta[name="viewport"]');const i="viewport-fit=cover";t||(t=document.createElement("meta"),t.setAttribute("name","viewport"));const n=Bs(t.content)&&t.content.includes(i);e?(this.cleanupViewport=!n,n||(t.content+=`,${i}`)):this.cleanupViewport&&(t.content=t.content.split(",").filter((e=>e.trim()!==i)).join(","))}this.onChange()})),cs(this,"trapFocus",(e=>{if(nr.isIos||!this.active||"Tab"!==e.key||9!==e.keyCode)return;const t=document.activeElement,i=br.call(this.player,"a[href], button:not(:disabled), input:not(:disabled), [tabindex]"),[n]=i,s=i[i.length-1];t!==s||e.shiftKey?t===n&&e.shiftKey&&(s.focus(),e.preventDefault()):(n.focus(),e.preventDefault())})),cs(this,"update",(()=>{if(this.enabled){let e;e=this.forceFallback?"Fallback (forced)":da.native?"Native":"Fallback",this.player.debug.log(`${e} fullscreen enabled`)}else this.player.debug.log("Fullscreen not supported and fallback disabled");fr(this.player.elements.container,this.player.config.classNames.fullscreen.enabled,this.enabled)})),cs(this,"enter",(()=>{this.enabled&&(nr.isIos&&this.player.config.fullscreen.iosNative?this.player.isVimeo?this.player.embed.requestFullscreen():this.target.webkitEnterFullscreen():!da.native||this.forceFallback?this.toggleFallback(!0):this.prefix?er(this.prefix)||this.target[`${this.prefix}Request${this.property}`]():this.target.requestFullscreen({navigationUI:"hide"}))})),cs(this,"exit",(()=>{if(this.enabled)if(nr.isIos&&this.player.config.fullscreen.iosNative)this.target.webkitExitFullscreen(),Or(this.player.play());else if(!da.native||this.forceFallback)this.toggleFallback(!1);else if(this.prefix){if(!er(this.prefix)){const e="moz"===this.prefix?"Cancel":"Exit";document[`${this.prefix}${e}${this.property}`]()}}else(document.cancelFullScreen||document.exitFullscreen).call(document)})),cs(this,"toggle",(()=>{this.active?this.exit():this.enter()})),this.player=e,this.prefix=da.prefix,this.property=da.property,this.scrollPosition={x:0,y:0},this.forceFallback="force"===e.config.fullscreen.fallback,this.player.elements.fullscreen=e.config.fullscreen.container&&function(e,t){const{prototype:i}=Element;return(i.closest||function(){let e=this;do{if(yr.matches(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}).call(e,t)}(this.player.elements.container,e.config.fullscreen.container),Cr.call(this.player,document,"ms"===this.prefix?"MSFullscreenChange":`${this.prefix}fullscreenchange`,(()=>{this.onChange()})),Cr.call(this.player,this.player.elements.container,"dblclick",(e=>{Ys(this.player.elements.controls)&&this.player.elements.controls.contains(e.target)||this.player.listeners.proxy(e,this.toggle,"fullscreen")})),Cr.call(this,this.player.elements.container,"keydown",(e=>this.trapFocus(e))),this.update()}static get native(){return!!(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled)}get usingNative(){return da.native&&!this.forceFallback}static get prefix(){if(Ws(document.exitFullscreen))return"";let e="";return["webkit","moz","ms"].some((t=>!(!Ws(document[`${t}ExitFullscreen`])&&!Ws(document[`${t}CancelFullScreen`]))&&(e=t,!0))),e}static get property(){return"moz"===this.prefix?"FullScreen":"Fullscreen"}get enabled(){return(da.native||this.player.config.fullscreen.fallback)&&this.player.config.fullscreen.enabled&&this.player.supported.ui&&this.player.isVideo}get active(){if(!this.enabled)return!1;if(!da.native||this.forceFallback)return gr(this.target,this.player.config.classNames.fullscreen.fallback);const e=this.prefix?this.target.getRootNode()[`${this.prefix}${this.property}Element`]:this.target.getRootNode().fullscreenElement;return e&&e.shadowRoot?e===this.target.getRootNode().host:e===this.target}get target(){return nr.isIos&&this.player.config.fullscreen.iosNative?this.player.media:this.player.elements.fullscreen||this.player.elements.container}}function pa(e,t=1){return new Promise(((i,n)=>{const s=new Image,r=()=>{delete s.onload,delete s.onerror,(s.naturalWidth>=t?i:n)(s)};Object.assign(s,{onload:r,onerror:r,src:e})}))}const ma={addStyleHook(){fr(this.elements.container,this.config.selectors.container.replace(".",""),!0),fr(this.elements.container,this.config.classNames.uiSupported,this.supported.ui)},toggleNativeControls(e=!1){e&&this.isHTML5?this.media.setAttribute("controls",""):this.media.removeAttribute("controls")},build(){if(this.listeners.media(),!this.supported.ui)return this.debug.warn(`Basic support only for ${this.provider} ${this.type}`),void ma.toggleNativeControls.call(this,!0);Ys(this.elements.controls)||(ea.inject.call(this),this.listeners.controls()),ma.toggleNativeControls.call(this),this.isHTML5&&na.setup.call(this),this.volume=null,this.muted=null,this.loop=null,this.quality=null,this.speed=null,ea.updateVolume.call(this),ea.timeUpdate.call(this),ea.durationUpdate.call(this),ma.checkPlaying.call(this),fr(this.elements.container,this.config.classNames.pip.supported,Tr.pip&&this.isHTML5&&this.isVideo),fr(this.elements.container,this.config.classNames.airplay.supported,Tr.airplay&&this.isHTML5),fr(this.elements.container,this.config.classNames.isIos,nr.isIos),fr(this.elements.container,this.config.classNames.isTouch,this.touch),this.ready=!0,setTimeout((()=>{Lr.call(this,this.media,"ready")}),0),ma.setTitle.call(this),this.poster&&ma.setPoster.call(this,this.poster,!1).catch((()=>{})),this.config.duration&&ea.durationUpdate.call(this)},setTitle(){let e=Yr.get("play",this.config);if(Bs(this.config.title)&&!er(this.config.title)&&(e+=`, ${this.config.title}`),Array.from(this.elements.buttons.play||[]).forEach((t=>{t.setAttribute("aria-label",e)})),this.isEmbed){const e=vr.call(this,"iframe");if(!Ys(e))return;const t=er(this.config.title)?"video":this.config.title,i=Yr.get("frameTitle",this.config);e.setAttribute("title",i.replace("{title}",t))}},togglePoster(e){fr(this.elements.container,this.config.classNames.posterEnabled,e)},setPoster(e,t=!0){return t&&this.poster?Promise.reject(new Error("Poster already set")):(this.media.setAttribute("data-poster",e),this.elements.poster.removeAttribute("hidden"),Ir.call(this).then((()=>pa(e))).catch((t=>{throw e===this.poster&&ma.togglePoster.call(this,!1),t})).then((()=>{if(e!==this.poster)throw new Error("setPoster cancelled by later call to setPoster")})).then((()=>(Object.assign(this.elements.poster.style,{backgroundImage:`url('${e}')`,backgroundSize:""}),ma.togglePoster.call(this,!0),e))))},checkPlaying(e){fr(this.elements.container,this.config.classNames.playing,this.playing),fr(this.elements.container,this.config.classNames.paused,this.paused),fr(this.elements.container,this.config.classNames.stopped,this.stopped),Array.from(this.elements.buttons.play||[]).forEach((e=>{Object.assign(e,{pressed:this.playing}),e.setAttribute("aria-label",Yr.get(this.playing?"pause":"play",this.config))})),Gs(e)&&"timeupdate"===e.type||ma.toggleControls.call(this)},checkLoading(e){this.loading=["stalled","waiting"].includes(e.type),clearTimeout(this.timers.loading),this.timers.loading=setTimeout((()=>{fr(this.elements.container,this.config.classNames.loading,this.loading),ma.toggleControls.call(this)}),this.loading?250:0)},toggleControls(e){const{controls:t}=this.elements;if(t&&this.config.hideControls){const i=this.touch&&this.lastSeekTime+2e3>Date.now();this.toggleControls(Boolean(e||this.loading||this.paused||t.pressed||t.hover||i))}},migrateStyles(){Object.values({...this.media.style}).filter((e=>!er(e)&&Bs(e)&&e.startsWith("--plyr"))).forEach((e=>{this.elements.container.style.setProperty(e,this.media.style.getPropertyValue(e)),this.media.style.removeProperty(e)})),er(this.media.style)&&this.media.removeAttribute("style")}};class fa{constructor(e){cs(this,"firstTouch",(()=>{const{player:e}=this,{elements:t}=e;e.touch=!0,fr(t.container,e.config.classNames.isTouch,!0)})),cs(this,"setTabFocus",(e=>{const{player:t}=this,{elements:i}=t;if(clearTimeout(this.focusTimer),"keydown"===e.type&&9!==e.which)return;"keydown"===e.type&&(this.lastKeyDown=e.timeStamp);const n=e.timeStamp-this.lastKeyDown<=20;("focus"!==e.type||n)&&((()=>{const e=t.config.classNames.tabFocus;fr(br.call(t,`.${e}`),e,!1)})(),"focusout"!==e.type&&(this.focusTimer=setTimeout((()=>{const e=document.activeElement;i.container.contains(e)&&fr(document.activeElement,t.config.classNames.tabFocus,!0)}),10)))})),cs(this,"global",((e=!0)=>{const{player:t}=this;t.config.keyboard.global&&Ar.call(t,window,"keydown keyup",this.handleKey,e,!1),Ar.call(t,document.body,"click",this.toggleMenu,e),Pr.call(t,document.body,"touchstart",this.firstTouch),Ar.call(t,document.body,"keydown focus blur focusout",this.setTabFocus,e,!1,!0)})),cs(this,"container",(()=>{const{player:e}=this,{config:t,elements:i,timers:n}=e;!t.keyboard.global&&t.keyboard.focused&&Cr.call(e,i.container,"keydown keyup",this.handleKey,!1),Cr.call(e,i.container,"mousemove mouseleave touchstart touchmove enterfullscreen exitfullscreen",(t=>{const{controls:s}=i;s&&"enterfullscreen"===t.type&&(s.pressed=!1,s.hover=!1);let r=0;["touchstart","touchmove","mousemove"].includes(t.type)&&(ma.toggleControls.call(e,!0),r=e.touch?3e3:2e3),clearTimeout(n.controls),n.controls=setTimeout((()=>ma.toggleControls.call(e,!1)),r)}));const s=()=>{if(!e.isVimeo||e.config.vimeo.premium)return;const t=i.wrapper,{active:n}=e.fullscreen,[s,r]=qr.call(e),a=Nr(`aspect-ratio: ${s} / ${r}`);if(!n)return void(a?(t.style.width=null,t.style.height=null):(t.style.maxWidth=null,t.style.margin=null));const[o,l]=[Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)],c=o/l>s/r;a?(t.style.width=c?"auto":"100%",t.style.height=c?"100%":"auto"):(t.style.maxWidth=c?l/r*s+"px":null,t.style.margin=c?"0 auto":null)},r=()=>{clearTimeout(n.resized),n.resized=setTimeout(s,50)};Cr.call(e,i.container,"enterfullscreen exitfullscreen",(t=>{const{target:n}=e.fullscreen;if(n!==i.container)return;if(!e.isEmbed&&er(e.config.ratio))return;s();("enterfullscreen"===t.type?Cr:Er).call(e,window,"resize",r)}))})),cs(this,"media",(()=>{const{player:e}=this,{elements:t}=e;if(Cr.call(e,e.media,"timeupdate seeking seeked",(t=>ea.timeUpdate.call(e,t))),Cr.call(e,e.media,"durationchange loadeddata loadedmetadata",(t=>ea.durationUpdate.call(e,t))),Cr.call(e,e.media,"ended",(()=>{e.isHTML5&&e.isVideo&&e.config.resetOnEnd&&(e.restart(),e.pause())})),Cr.call(e,e.media,"progress playing seeking seeked",(t=>ea.updateProgress.call(e,t))),Cr.call(e,e.media,"volumechange",(t=>ea.updateVolume.call(e,t))),Cr.call(e,e.media,"playing play pause ended emptied timeupdate",(t=>ma.checkPlaying.call(e,t))),Cr.call(e,e.media,"waiting canplay seeked playing",(t=>ma.checkLoading.call(e,t))),e.supported.ui&&e.config.clickToPlay&&!e.isAudio){const i=vr.call(e,`.${e.config.classNames.video}`);if(!Ys(i))return;Cr.call(e,t.container,"click",(n=>{([t.container,i].includes(n.target)||i.contains(n.target))&&(e.touch&&e.config.hideControls||(e.ended?(this.proxy(n,e.restart,"restart"),this.proxy(n,(()=>{Or(e.play())}),"play")):this.proxy(n,(()=>{Or(e.togglePlay())}),"play")))}))}e.supported.ui&&e.config.disableContextMenu&&Cr.call(e,t.wrapper,"contextmenu",(e=>{e.preventDefault()}),!1),Cr.call(e,e.media,"volumechange",(()=>{e.storage.set({volume:e.volume,muted:e.muted})})),Cr.call(e,e.media,"ratechange",(()=>{ea.updateSetting.call(e,"speed"),e.storage.set({speed:e.speed})})),Cr.call(e,e.media,"qualitychange",(t=>{ea.updateSetting.call(e,"quality",null,t.detail.quality)})),Cr.call(e,e.media,"ready qualitychange",(()=>{ea.setDownloadUrl.call(e)}));const i=e.config.events.concat(["keyup","keydown"]).join(" ");Cr.call(e,e.media,i,(i=>{let{detail:n={}}=i;"error"===i.type&&(n=e.media.error),Lr.call(e,t.container,i.type,!0,n)}))})),cs(this,"proxy",((e,t,i)=>{const{player:n}=this,s=n.config.listeners[i];let r=!0;Ws(s)&&(r=s.call(n,e)),!1!==r&&Ws(t)&&t.call(n,e)})),cs(this,"bind",((e,t,i,n,s=!0)=>{const{player:r}=this,a=r.config.listeners[n],o=Ws(a);Cr.call(r,e,t,(e=>this.proxy(e,i,n)),s&&!o)})),cs(this,"controls",(()=>{const{player:e}=this,{elements:t}=e,i=nr.isIE?"change":"input";if(t.buttons.play&&Array.from(t.buttons.play).forEach((t=>{this.bind(t,"click",(()=>{Or(e.togglePlay())}),"play")})),this.bind(t.buttons.restart,"click",e.restart,"restart"),this.bind(t.buttons.rewind,"click",(()=>{e.lastSeekTime=Date.now(),e.rewind()}),"rewind"),this.bind(t.buttons.fastForward,"click",(()=>{e.lastSeekTime=Date.now(),e.forward()}),"fastForward"),this.bind(t.buttons.mute,"click",(()=>{e.muted=!e.muted}),"mute"),this.bind(t.buttons.captions,"click",(()=>e.toggleCaptions())),this.bind(t.buttons.download,"click",(()=>{Lr.call(e,e.media,"download")}),"download"),this.bind(t.buttons.fullscreen,"click",(()=>{e.fullscreen.toggle()}),"fullscreen"),this.bind(t.buttons.pip,"click",(()=>{e.pip="toggle"}),"pip"),this.bind(t.buttons.airplay,"click",e.airplay,"airplay"),this.bind(t.buttons.settings,"click",(t=>{t.stopPropagation(),t.preventDefault(),ea.toggleMenu.call(e,t)}),null,!1),this.bind(t.buttons.settings,"keyup",(t=>{const i=t.which;[13,32].includes(i)&&(13!==i?(t.preventDefault(),t.stopPropagation(),ea.toggleMenu.call(e,t)):ea.focusFirstMenuItem.call(e,null,!0))}),null,!1),this.bind(t.settings.menu,"keydown",(t=>{27===t.which&&ea.toggleMenu.call(e,t)})),this.bind(t.inputs.seek,"mousedown mousemove",(e=>{const i=t.progress.getBoundingClientRect(),n=100/i.width*(e.pageX-i.left);e.currentTarget.setAttribute("seek-value",n)})),this.bind(t.inputs.seek,"mousedown mouseup keydown keyup touchstart touchend",(t=>{const i=t.currentTarget,n=t.keyCode?t.keyCode:t.which,s="play-on-seeked";if(Xs(t)&&39!==n&&37!==n)return;e.lastSeekTime=Date.now();const r=i.hasAttribute(s),a=["mouseup","touchend","keyup"].includes(t.type);r&&a?(i.removeAttribute(s),Or(e.play())):!a&&e.playing&&(i.setAttribute(s,""),e.pause())})),nr.isIos){const t=br.call(e,'input[type="range"]');Array.from(t).forEach((e=>this.bind(e,i,(e=>ir(e.target)))))}this.bind(t.inputs.seek,i,(t=>{const i=t.currentTarget;let n=i.getAttribute("seek-value");er(n)&&(n=i.value),i.removeAttribute("seek-value"),e.currentTime=n/i.max*e.duration}),"seek"),this.bind(t.progress,"mouseenter mouseleave mousemove",(t=>ea.updateSeekTooltip.call(e,t))),this.bind(t.progress,"mousemove touchmove",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startMove(t)})),this.bind(t.progress,"mouseleave touchend click",(()=>{const{previewThumbnails:t}=e;t&&t.loaded&&t.endMove(!1,!0)})),this.bind(t.progress,"mousedown touchstart",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startScrubbing(t)})),this.bind(t.progress,"mouseup touchend",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.endScrubbing(t)})),nr.isWebkit&&Array.from(br.call(e,'input[type="range"]')).forEach((t=>{this.bind(t,"input",(t=>ea.updateRangeFill.call(e,t.target)))})),e.config.toggleInvert&&!Ys(t.display.duration)&&this.bind(t.display.currentTime,"click",(()=>{0!==e.currentTime&&(e.config.invertTime=!e.config.invertTime,ea.timeUpdate.call(e))})),this.bind(t.inputs.volume,i,(t=>{e.volume=t.target.value}),"volume"),this.bind(t.controls,"mouseenter mouseleave",(i=>{t.controls.hover=!e.touch&&"mouseenter"===i.type})),t.fullscreen&&Array.from(t.fullscreen.children).filter((e=>!e.contains(t.container))).forEach((i=>{this.bind(i,"mouseenter mouseleave",(i=>{t.controls&&(t.controls.hover=!e.touch&&"mouseenter"===i.type)}))})),this.bind(t.controls,"mousedown mouseup touchstart touchend touchcancel",(e=>{t.controls.pressed=["mousedown","touchstart"].includes(e.type)})),this.bind(t.controls,"focusin",(()=>{const{config:i,timers:n}=e;fr(t.controls,i.classNames.noTransition,!0),ma.toggleControls.call(e,!0),setTimeout((()=>{fr(t.controls,i.classNames.noTransition,!1)}),0);const s=this.touch?3e3:4e3;clearTimeout(n.controls),n.controls=setTimeout((()=>ma.toggleControls.call(e,!1)),s)})),this.bind(t.inputs.volume,"wheel",(t=>{const i=t.webkitDirectionInvertedFromDevice,[n,s]=[t.deltaX,-t.deltaY].map((e=>i?-e:e)),r=Math.sign(Math.abs(n)>Math.abs(s)?n:s);e.increaseVolume(r/50);const{volume:a}=e.media;(1===r&&a<1||-1===r&&a>0)&&t.preventDefault()}),"volume",!1)})),this.player=e,this.lastKey=null,this.focusTimer=null,this.lastKeyDown=null,this.handleKey=this.handleKey.bind(this),this.toggleMenu=this.toggleMenu.bind(this),this.setTabFocus=this.setTabFocus.bind(this),this.firstTouch=this.firstTouch.bind(this)}handleKey(e){const{player:t}=this,{elements:i}=t,n=e.keyCode?e.keyCode:e.which,s="keydown"===e.type,r=s&&n===this.lastKey;if(e.altKey||e.ctrlKey||e.metaKey||e.shiftKey)return;if(!Hs(n))return;if(s){const s=document.activeElement;if(Ys(s)){const{editable:n}=t.config.selectors,{seek:r}=i.inputs;if(s!==r&&yr(s,n))return;if(32===e.which&&yr(s,'button, [role^="menuitem"]'))return}switch([32,37,38,39,40,48,49,50,51,52,53,54,56,57,67,70,73,75,76,77,79].includes(n)&&(e.preventDefault(),e.stopPropagation()),n){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:r||(t.currentTime=t.duration/10*(n-48));break;case 32:case 75:r||Or(t.togglePlay());break;case 38:t.increaseVolume(.1);break;case 40:t.decreaseVolume(.1);break;case 77:r||(t.muted=!t.muted);break;case 39:t.forward();break;case 37:t.rewind();break;case 70:t.fullscreen.toggle();break;case 67:r||t.toggleCaptions();break;case 76:t.loop=!t.loop}27===n&&!t.fullscreen.usingNative&&t.fullscreen.active&&t.fullscreen.toggle(),this.lastKey=n}else this.lastKey=null}toggleMenu(e){ea.toggleMenu.call(this.player,e)}}var ga=o((function(e,t){e.exports=function(){var e=function(){},t={},i={},n={};function s(e,t){e=e.push?e:[e];var s,r,a,o=[],l=e.length,c=l;for(s=function(e,i){i.length&&o.push(e),--c||t(o)};l--;)r=e[l],(a=i[r])?s(r,a):(n[r]=n[r]||[]).push(s)}function r(e,t){if(e){var s=n[e];if(i[e]=t,s)for(;s.length;)s[0](e,t),s.splice(0,1)}}function a(t,i){t.call&&(t={success:t}),i.length?(t.error||e)(i):(t.success||e)(t)}function o(t,i,n,s){var r,a,l=document,c=n.async,u=(n.numRetries||0)+1,h=n.before||e,d=t.replace(/[\?|#].*$/,""),p=t.replace(/^(css|img)!/,"");s=s||0,/(^css!|\.css$)/.test(d)?((a=l.createElement("link")).rel="stylesheet",a.href=p,(r="hideFocus"in a)&&a.relList&&(r=0,a.rel="preload",a.as="style")):/(^img!|\.(png|gif|jpg|svg|webp)$)/.test(d)?(a=l.createElement("img")).src=p:((a=l.createElement("script")).src=t,a.async=void 0===c||c),a.onload=a.onerror=a.onbeforeload=function(e){var l=e.type[0];if(r)try{a.sheet.cssText.length||(l="e")}catch(e){18!=e.code&&(l="e")}if("e"==l){if((s+=1)<u)return o(t,i,n,s)}else if("preload"==a.rel&&"style"==a.as)return a.rel="stylesheet";i(t,l,e.defaultPrevented)},!1!==h(t,a)&&l.head.appendChild(a)}function l(e,t,i){var n,s,r=(e=e.push?e:[e]).length,a=r,l=[];for(n=function(e,i,n){if("e"==i&&l.push(e),"b"==i){if(!n)return;l.push(e)}--r||t(l)},s=0;s<a;s++)o(e[s],n,i)}function c(e,i,n){var s,o;if(i&&i.trim&&(s=i),o=(s?n:i)||{},s){if(s in t)throw"LoadJS";t[s]=!0}function c(t,i){l(e,(function(e){a(o,e),t&&a({success:t,error:i},e),r(s,e)}),o)}if(o.returnPromise)return new Promise(c);c()}return c.ready=function(e,t){return s(e,(function(e){a(t,e)})),c},c.done=function(e){r(e,[])},c.reset=function(){t={},i={},n={}},c.isDefined=function(e){return e in t},c}()}));function ya(e){return new Promise(((t,i)=>{ga(e,{success:t,error:i})}))}function ba(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,Lr.call(this,this.media,e?"play":"pause"))}const va={setup(){const e=this;fr(e.elements.wrapper,e.config.classNames.embed,!0),e.options.speed=e.config.speed.options,Ur.call(e),Ds(window.Vimeo)?va.ready.call(e):ya(e.config.urls.vimeo.sdk).then((()=>{va.ready.call(e)})).catch((t=>{e.debug.warn("Vimeo SDK (player.js) failed to load",t)}))},ready(){const e=this,t=e.config.vimeo,{premium:i,referrerPolicy:n,...s}=t;let r=e.media.getAttribute("src"),a="";er(r)?(r=e.media.getAttribute(e.config.attributes.embed.id),a=e.media.getAttribute(e.config.attributes.embed.hash)):a=function(e){const t=e.match(/^.*(?:vimeo.com\/|video\/)(?:\d+)(?:\?.*&*h=|\/)+(?<hash>[\d,a-f]+)/);return t?t.groups.hash:null}(r);const o=a?{h:a}:{};i&&Object.assign(s,{controls:!1,sidedock:!1});const l=ia({loop:e.config.loop.active,autoplay:e.autoplay,muted:e.muted,gesture:"media",playsinline:!this.config.fullscreen.iosNative,...o,...s}),c=er(u=r)?null:Hs(Number(u))?u:u.match(/^.*(vimeo.com\/|video\/)(\d+).*/)?RegExp.$2:u;var u;const h=lr("iframe"),d=Hr(e.config.urls.vimeo.iframe,c,l);if(h.setAttribute("src",d),h.setAttribute("allowfullscreen",""),h.setAttribute("allow",["autoplay","fullscreen","picture-in-picture","encrypted-media","accelerometer","gyroscope"].join("; ")),er(n)||h.setAttribute("referrerPolicy",n),i||!t.customControls)h.setAttribute("data-poster",e.poster),e.media=dr(h,e.media);else{const t=lr("div",{class:e.config.classNames.embedContainer,"data-poster":e.poster});t.appendChild(h),e.media=dr(t,e.media)}t.customControls||Xr(Hr(e.config.urls.vimeo.api,d)).then((t=>{!er(t)&&t.thumbnail_url&&ma.setPoster.call(e,t.thumbnail_url).catch((()=>{}))})),e.embed=new window.Vimeo.Player(h,{autopause:e.config.autopause,muted:e.muted}),e.media.paused=!0,e.media.currentTime=0,e.supported.ui&&e.embed.disableTextTrack(),e.media.play=()=>(ba.call(e,!0),e.embed.play()),e.media.pause=()=>(ba.call(e,!1),e.embed.pause()),e.media.stop=()=>{e.pause(),e.currentTime=0};let{currentTime:p}=e.media;Object.defineProperty(e.media,"currentTime",{get:()=>p,set(t){const{embed:i,media:n,paused:s,volume:r}=e,a=s&&!i.hasPlayed;n.seeking=!0,Lr.call(e,n,"seeking"),Promise.resolve(a&&i.setVolume(0)).then((()=>i.setCurrentTime(t))).then((()=>a&&i.pause())).then((()=>a&&i.setVolume(r))).catch((()=>{}))}});let m=e.config.speed.selected;Object.defineProperty(e.media,"playbackRate",{get:()=>m,set(t){e.embed.setPlaybackRate(t).then((()=>{m=t,Lr.call(e,e.media,"ratechange")})).catch((()=>{e.options.speed=[1]}))}});let{volume:f}=e.config;Object.defineProperty(e.media,"volume",{get:()=>f,set(t){e.embed.setVolume(t).then((()=>{f=t,Lr.call(e,e.media,"volumechange")}))}});let{muted:g}=e.config;Object.defineProperty(e.media,"muted",{get:()=>g,set(t){const i=!!Vs(t)&&t;e.embed.setVolume(i?0:e.config.volume).then((()=>{g=i,Lr.call(e,e.media,"volumechange")}))}});let y,{loop:b}=e.config;Object.defineProperty(e.media,"loop",{get:()=>b,set(t){const i=Vs(t)?t:e.config.loop.active;e.embed.setLoop(i).then((()=>{b=i}))}}),e.embed.getVideoUrl().then((t=>{y=t,ea.setDownloadUrl.call(e)})).catch((e=>{this.debug.warn(e)})),Object.defineProperty(e.media,"currentSrc",{get:()=>y}),Object.defineProperty(e.media,"ended",{get:()=>e.currentTime===e.duration}),Promise.all([e.embed.getVideoWidth(),e.embed.getVideoHeight()]).then((t=>{const[i,n]=t;e.embed.ratio=Fr(i,n),Ur.call(this)})),e.embed.setAutopause(e.config.autopause).then((t=>{e.config.autopause=t})),e.embed.getVideoTitle().then((t=>{e.config.title=t,ma.setTitle.call(this)})),e.embed.getCurrentTime().then((t=>{p=t,Lr.call(e,e.media,"timeupdate")})),e.embed.getDuration().then((t=>{e.media.duration=t,Lr.call(e,e.media,"durationchange")})),e.embed.getTextTracks().then((t=>{e.media.textTracks=t,na.setup.call(e)})),e.embed.on("cuechange",(({cues:t=[]})=>{const i=t.map((e=>function(e){const t=document.createDocumentFragment(),i=document.createElement("div");return t.appendChild(i),i.innerHTML=e,t.firstChild.innerText}(e.text)));na.updateCues.call(e,i)})),e.embed.on("loaded",(()=>{if(e.embed.getPaused().then((t=>{ba.call(e,!t),t||Lr.call(e,e.media,"playing")})),Ys(e.embed.element)&&e.supported.ui){e.embed.element.setAttribute("tabindex",-1)}})),e.embed.on("bufferstart",(()=>{Lr.call(e,e.media,"waiting")})),e.embed.on("bufferend",(()=>{Lr.call(e,e.media,"playing")})),e.embed.on("play",(()=>{ba.call(e,!0),Lr.call(e,e.media,"playing")})),e.embed.on("pause",(()=>{ba.call(e,!1)})),e.embed.on("timeupdate",(t=>{e.media.seeking=!1,p=t.seconds,Lr.call(e,e.media,"timeupdate")})),e.embed.on("progress",(t=>{e.media.buffered=t.percent,Lr.call(e,e.media,"progress"),1===parseInt(t.percent,10)&&Lr.call(e,e.media,"canplaythrough"),e.embed.getDuration().then((t=>{t!==e.media.duration&&(e.media.duration=t,Lr.call(e,e.media,"durationchange"))}))})),e.embed.on("seeked",(()=>{e.media.seeking=!1,Lr.call(e,e.media,"seeked")})),e.embed.on("ended",(()=>{e.media.paused=!0,Lr.call(e,e.media,"ended")})),e.embed.on("error",(t=>{e.media.error=t,Lr.call(e,e.media,"error")})),t.customControls&&setTimeout((()=>ma.build.call(e)),0)}};function wa(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,Lr.call(this,this.media,e?"play":"pause"))}function ka(e){return e.noCookie?"https://www.youtube-nocookie.com":"http:"===window.location.protocol?"http://www.youtube.com":void 0}const Ta={setup(){if(fr(this.elements.wrapper,this.config.classNames.embed,!0),Ds(window.YT)&&Ws(window.YT.Player))Ta.ready.call(this);else{const e=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=()=>{Ws(e)&&e(),Ta.ready.call(this)},ya(this.config.urls.youtube.sdk).catch((e=>{this.debug.warn("YouTube API failed to load",e)}))}},getTitle(e){Xr(Hr(this.config.urls.youtube.api,e)).then((e=>{if(Ds(e)){const{title:t,height:i,width:n}=e;this.config.title=t,ma.setTitle.call(this),this.embed.ratio=Fr(n,i)}Ur.call(this)})).catch((()=>{Ur.call(this)}))},ready(){const e=this,t=e.config.youtube,i=e.media&&e.media.getAttribute("id");if(!er(i)&&i.startsWith("youtube-"))return;let n=e.media.getAttribute("src");er(n)&&(n=e.media.getAttribute(this.config.attributes.embed.id));const s=er(r=n)?null:r.match(/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/)?RegExp.$2:r;var r;const a=lr("div",{id:`${e.provider}-${Math.floor(1e4*Math.random())}`,"data-poster":t.customControls?e.poster:void 0});if(e.media=dr(a,e.media),t.customControls){const t=e=>`https://i.ytimg.com/vi/${s}/${e}default.jpg`;pa(t("maxres"),121).catch((()=>pa(t("sd"),121))).catch((()=>pa(t("hq")))).then((t=>ma.setPoster.call(e,t.src))).then((t=>{t.includes("maxres")||(e.elements.poster.style.backgroundSize="cover")})).catch((()=>{}))}e.embed=new window.YT.Player(e.media,{videoId:s,host:ka(t),playerVars:rr({},{autoplay:e.config.autoplay?1:0,hl:e.config.hl,controls:e.supported.ui&&t.customControls?0:1,disablekb:1,playsinline:e.config.fullscreen.iosNative?0:1,cc_load_policy:e.captions.active?1:0,cc_lang_pref:e.config.captions.language,widget_referrer:window?window.location.href:null},t),events:{onError(t){if(!e.media.error){const i=t.data,n={2:"The request contains an invalid parameter value. For example, this error occurs if you specify a video ID that does not have 11 characters, or if the video ID contains invalid characters, such as exclamation points or asterisks.",5:"The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.",100:"The video requested was not found. This error occurs when a video has been removed (for any reason) or has been marked as private.",101:"The owner of the requested video does not allow it to be played in embedded players.",150:"The owner of the requested video does not allow it to be played in embedded players."}[i]||"An unknown error occured";e.media.error={code:i,message:n},Lr.call(e,e.media,"error")}},onPlaybackRateChange(t){const i=t.target;e.media.playbackRate=i.getPlaybackRate(),Lr.call(e,e.media,"ratechange")},onReady(i){if(Ws(e.media.play))return;const n=i.target;Ta.getTitle.call(e,s),e.media.play=()=>{wa.call(e,!0),n.playVideo()},e.media.pause=()=>{wa.call(e,!1),n.pauseVideo()},e.media.stop=()=>{n.stopVideo()},e.media.duration=n.getDuration(),e.media.paused=!0,e.media.currentTime=0,Object.defineProperty(e.media,"currentTime",{get:()=>Number(n.getCurrentTime()),set(t){e.paused&&!e.embed.hasPlayed&&e.embed.mute(),e.media.seeking=!0,Lr.call(e,e.media,"seeking"),n.seekTo(t)}}),Object.defineProperty(e.media,"playbackRate",{get:()=>n.getPlaybackRate(),set(e){n.setPlaybackRate(e)}});let{volume:r}=e.config;Object.defineProperty(e.media,"volume",{get:()=>r,set(t){r=t,n.setVolume(100*r),Lr.call(e,e.media,"volumechange")}});let{muted:a}=e.config;Object.defineProperty(e.media,"muted",{get:()=>a,set(t){const i=Vs(t)?t:a;a=i,n[i?"mute":"unMute"](),n.setVolume(100*r),Lr.call(e,e.media,"volumechange")}}),Object.defineProperty(e.media,"currentSrc",{get:()=>n.getVideoUrl()}),Object.defineProperty(e.media,"ended",{get:()=>e.currentTime===e.duration});const o=n.getAvailablePlaybackRates();e.options.speed=o.filter((t=>e.config.speed.options.includes(t))),e.supported.ui&&t.customControls&&e.media.setAttribute("tabindex",-1),Lr.call(e,e.media,"timeupdate"),Lr.call(e,e.media,"durationchange"),clearInterval(e.timers.buffering),e.timers.buffering=setInterval((()=>{e.media.buffered=n.getVideoLoadedFraction(),(null===e.media.lastBuffered||e.media.lastBuffered<e.media.buffered)&&Lr.call(e,e.media,"progress"),e.media.lastBuffered=e.media.buffered,1===e.media.buffered&&(clearInterval(e.timers.buffering),Lr.call(e,e.media,"canplaythrough"))}),200),t.customControls&&setTimeout((()=>ma.build.call(e)),50)},onStateChange(i){const n=i.target;clearInterval(e.timers.playing);switch(e.media.seeking&&[1,2].includes(i.data)&&(e.media.seeking=!1,Lr.call(e,e.media,"seeked")),i.data){case-1:Lr.call(e,e.media,"timeupdate"),e.media.buffered=n.getVideoLoadedFraction(),Lr.call(e,e.media,"progress");break;case 0:wa.call(e,!1),e.media.loop?(n.stopVideo(),n.playVideo()):Lr.call(e,e.media,"ended");break;case 1:t.customControls&&!e.config.autoplay&&e.media.paused&&!e.embed.hasPlayed?e.media.pause():(wa.call(e,!0),Lr.call(e,e.media,"playing"),e.timers.playing=setInterval((()=>{Lr.call(e,e.media,"timeupdate")}),50),e.media.duration!==n.getDuration()&&(e.media.duration=n.getDuration(),Lr.call(e,e.media,"durationchange")));break;case 2:e.muted||e.embed.unMute(),wa.call(e,!1);break;case 3:Lr.call(e,e.media,"waiting")}Lr.call(e,e.elements.container,"statechange",!1,{code:i.data})}}})}},Sa={setup(){this.media?(fr(this.elements.container,this.config.classNames.type.replace("{0}",this.type),!0),fr(this.elements.container,this.config.classNames.provider.replace("{0}",this.provider),!0),this.isEmbed&&fr(this.elements.container,this.config.classNames.type.replace("{0}","video"),!0),this.isVideo&&(this.elements.wrapper=lr("div",{class:this.config.classNames.video}),ar(this.media,this.elements.wrapper),this.elements.poster=lr("div",{class:this.config.classNames.poster}),this.elements.wrapper.appendChild(this.elements.poster)),this.isHTML5?Dr.setup.call(this):this.isYouTube?Ta.setup.call(this):this.isVimeo&&va.setup.call(this)):this.debug.warn("No media element found!")}};class Aa{constructor(e){cs(this,"load",(()=>{this.enabled&&(Ds(window.google)&&Ds(window.google.ima)?this.ready():ya(this.player.config.urls.googleIMA.sdk).then((()=>{this.ready()})).catch((()=>{this.trigger("error",new Error("Google IMA SDK failed to load"))})))})),cs(this,"ready",(()=>{var e;this.enabled||((e=this).manager&&e.manager.destroy(),e.elements.displayContainer&&e.elements.displayContainer.destroy(),e.elements.container.remove()),this.startSafetyTimer(12e3,"ready()"),this.managerPromise.then((()=>{this.clearSafetyTimer("onAdsManagerLoaded()")})),this.listeners(),this.setupIMA()})),cs(this,"setupIMA",(()=>{this.elements.container=lr("div",{class:this.player.config.classNames.ads}),this.player.elements.container.appendChild(this.elements.container),google.ima.settings.setVpaidMode(google.ima.ImaSdkSettings.VpaidMode.ENABLED),google.ima.settings.setLocale(this.player.config.ads.language),google.ima.settings.setDisableCustomPlaybackForIOS10Plus(this.player.config.playsinline),this.elements.displayContainer=new google.ima.AdDisplayContainer(this.elements.container,this.player.media),this.loader=new google.ima.AdsLoader(this.elements.displayContainer),this.loader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,(e=>this.onAdsManagerLoaded(e)),!1),this.loader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e)),!1),this.requestAds()})),cs(this,"requestAds",(()=>{const{container:e}=this.player.elements;try{const t=new google.ima.AdsRequest;t.adTagUrl=this.tagUrl,t.linearAdSlotWidth=e.offsetWidth,t.linearAdSlotHeight=e.offsetHeight,t.nonLinearAdSlotWidth=e.offsetWidth,t.nonLinearAdSlotHeight=e.offsetHeight,t.forceNonLinearFullSlot=!1,t.setAdWillPlayMuted(!this.player.muted),this.loader.requestAds(t)}catch(e){this.onAdError(e)}})),cs(this,"pollCountdown",((e=!1)=>{if(!e)return clearInterval(this.countdownTimer),void this.elements.container.removeAttribute("data-badge-text");this.countdownTimer=setInterval((()=>{const e=Zr(Math.max(this.manager.getRemainingTime(),0)),t=`${Yr.get("advertisement",this.player.config)} - ${e}`;this.elements.container.setAttribute("data-badge-text",t)}),100)})),cs(this,"onAdsManagerLoaded",(e=>{if(!this.enabled)return;const t=new google.ima.AdsRenderingSettings;t.restoreCustomPlaybackStateOnAdBreakComplete=!0,t.enablePreloading=!0,this.manager=e.getAdsManager(this.player,t),this.cuePoints=this.manager.getCuePoints(),this.manager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e))),Object.keys(google.ima.AdEvent.Type).forEach((e=>{this.manager.addEventListener(google.ima.AdEvent.Type[e],(e=>this.onAdEvent(e)))})),this.trigger("loaded")})),cs(this,"addCuePoints",(()=>{er(this.cuePoints)||this.cuePoints.forEach((e=>{if(0!==e&&-1!==e&&e<this.player.duration){const t=this.player.elements.progress;if(Ys(t)){const i=100/this.player.duration*e,n=lr("span",{class:this.player.config.classNames.cues});n.style.left=`${i.toString()}%`,t.appendChild(n)}}}))})),cs(this,"onAdEvent",(e=>{const{container:t}=this.player.elements,i=e.getAd(),n=e.getAdData();switch((e=>{Lr.call(this.player,this.player.media,`ads${e.replace(/_/g,"").toLowerCase()}`)})(e.type),e.type){case google.ima.AdEvent.Type.LOADED:this.trigger("loaded"),this.pollCountdown(!0),i.isLinear()||(i.width=t.offsetWidth,i.height=t.offsetHeight);break;case google.ima.AdEvent.Type.STARTED:this.manager.setVolume(this.player.volume);break;case google.ima.AdEvent.Type.ALL_ADS_COMPLETED:this.player.ended?this.loadAds():this.loader.contentComplete();break;case google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED:this.pauseContent();break;case google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED:this.pollCountdown(),this.resumeContent();break;case google.ima.AdEvent.Type.LOG:n.adError&&this.player.debug.warn(`Non-fatal ad error: ${n.adError.getMessage()}`)}})),cs(this,"onAdError",(e=>{this.cancel(),this.player.debug.warn("Ads error",e)})),cs(this,"listeners",(()=>{const{container:e}=this.player.elements;let t;this.player.on("canplay",(()=>{this.addCuePoints()})),this.player.on("ended",(()=>{this.loader.contentComplete()})),this.player.on("timeupdate",(()=>{t=this.player.currentTime})),this.player.on("seeked",(()=>{const e=this.player.currentTime;er(this.cuePoints)||this.cuePoints.forEach(((i,n)=>{t<i&&i<e&&(this.manager.discardAdBreak(),this.cuePoints.splice(n,1))}))})),window.addEventListener("resize",(()=>{this.manager&&this.manager.resize(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL)}))})),cs(this,"play",(()=>{const{container:e}=this.player.elements;this.managerPromise||this.resumeContent(),this.managerPromise.then((()=>{this.manager.setVolume(this.player.volume),this.elements.displayContainer.initialize();try{this.initialized||(this.manager.init(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL),this.manager.start()),this.initialized=!0}catch(e){this.onAdError(e)}})).catch((()=>{}))})),cs(this,"resumeContent",(()=>{this.elements.container.style.zIndex="",this.playing=!1,Or(this.player.media.play())})),cs(this,"pauseContent",(()=>{this.elements.container.style.zIndex=3,this.playing=!0,this.player.media.pause()})),cs(this,"cancel",(()=>{this.initialized&&this.resumeContent(),this.trigger("error"),this.loadAds()})),cs(this,"loadAds",(()=>{this.managerPromise.then((()=>{this.manager&&this.manager.destroy(),this.managerPromise=new Promise((e=>{this.on("loaded",e),this.player.debug.log(this.manager)})),this.initialized=!1,this.requestAds()})).catch((()=>{}))})),cs(this,"trigger",((e,...t)=>{const i=this.events[e];zs(i)&&i.forEach((e=>{Ws(e)&&e.apply(this,t)}))})),cs(this,"on",((e,t)=>(zs(this.events[e])||(this.events[e]=[]),this.events[e].push(t),this))),cs(this,"startSafetyTimer",((e,t)=>{this.player.debug.log(`Safety timer invoked from: ${t}`),this.safetyTimer=setTimeout((()=>{this.cancel(),this.clearSafetyTimer("startSafetyTimer()")}),e)})),cs(this,"clearSafetyTimer",(e=>{Fs(this.safetyTimer)||(this.player.debug.log(`Safety timer cleared from: ${e}`),clearTimeout(this.safetyTimer),this.safetyTimer=null)})),this.player=e,this.config=e.config.ads,this.playing=!1,this.initialized=!1,this.elements={container:null,displayContainer:null},this.manager=null,this.loader=null,this.cuePoints=null,this.events={},this.safetyTimer=null,this.countdownTimer=null,this.managerPromise=new Promise(((e,t)=>{this.on("loaded",e),this.on("error",t)})),this.load()}get enabled(){const{config:e}=this;return this.player.isHTML5&&this.player.isVideo&&e.enabled&&(!er(e.publisherId)||Zs(e.tagUrl))}get tagUrl(){const{config:e}=this;if(Zs(e.tagUrl))return e.tagUrl;return`https://go.aniview.com/api/adserver6/vast/?${ia({AV_PUBLISHERID:"58c25bb0073ef448b1087ad6",AV_CHANNELID:"5a0458dc28a06145e4519d21",AV_URL:window.location.hostname,cb:Date.now(),AV_WIDTH:640,AV_HEIGHT:480,AV_CDIM2:e.publisherId})}`}}const Ca=e=>{const t=[];return e.split(/\r\n\r\n|\n\n|\r\r/).forEach((e=>{const i={};e.split(/\r\n|\n|\r/).forEach((e=>{if(Hs(i.startTime)){if(!er(e.trim())&&er(i.text)){const t=e.trim().split("#xywh=");[i.text]=t,t[1]&&([i.x,i.y,i.w,i.h]=t[1].split(","))}}else{const t=e.match(/([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})( ?--> ?)([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})/);t&&(i.startTime=60*Number(t[1]||0)*60+60*Number(t[2])+Number(t[3])+Number(`0.${t[4]}`),i.endTime=60*Number(t[6]||0)*60+60*Number(t[7])+Number(t[8])+Number(`0.${t[9]}`))}})),i.text&&t.push(i)})),t},Ea=(e,t)=>{const i={};return e>t.width/t.height?(i.width=t.width,i.height=1/e*t.width):(i.height=t.height,i.width=e*t.height),i};class Pa{constructor(e){cs(this,"load",(()=>{this.player.elements.display.seekTooltip&&(this.player.elements.display.seekTooltip.hidden=this.enabled),this.enabled&&this.getThumbnails().then((()=>{this.enabled&&(this.render(),this.determineContainerAutoSizing(),this.loaded=!0)}))})),cs(this,"getThumbnails",(()=>new Promise((e=>{const{src:t}=this.player.config.previewThumbnails;if(er(t))throw new Error("Missing previewThumbnails.src config attribute");const i=()=>{this.thumbnails.sort(((e,t)=>e.height-t.height)),this.player.debug.log("Preview thumbnails",this.thumbnails),e()};if(Ws(t))t((e=>{this.thumbnails=e,i()}));else{const e=(Bs(t)?[t]:t).map((e=>this.getThumbnail(e)));Promise.all(e).then(i)}})))),cs(this,"getThumbnail",(e=>new Promise((t=>{Xr(e).then((i=>{const n={frames:Ca(i),height:null,urlPrefix:""};n.frames[0].text.startsWith("/")||n.frames[0].text.startsWith("http://")||n.frames[0].text.startsWith("https://")||(n.urlPrefix=e.substring(0,e.lastIndexOf("/")+1));const s=new Image;s.onload=()=>{n.height=s.naturalHeight,n.width=s.naturalWidth,this.thumbnails.push(n),t()},s.src=n.urlPrefix+n.frames[0].text}))})))),cs(this,"startMove",(e=>{if(this.loaded&&Gs(e)&&["touchmove","mousemove"].includes(e.type)&&this.player.media.duration){if("touchmove"===e.type)this.seekTime=this.player.media.duration*(this.player.elements.inputs.seek.value/100);else{const t=this.player.elements.progress.getBoundingClientRect(),i=100/t.width*(e.pageX-t.left);this.seekTime=this.player.media.duration*(i/100),this.seekTime<0&&(this.seekTime=0),this.seekTime>this.player.media.duration-1&&(this.seekTime=this.player.media.duration-1),this.mousePosX=e.pageX,this.elements.thumb.time.innerText=Zr(this.seekTime)}this.showImageAtCurrentTime()}})),cs(this,"endMove",(()=>{this.toggleThumbContainer(!1,!0)})),cs(this,"startScrubbing",(e=>{(Fs(e.button)||!1===e.button||0===e.button)&&(this.mouseDown=!0,this.player.media.duration&&(this.toggleScrubbingContainer(!0),this.toggleThumbContainer(!1,!0),this.showImageAtCurrentTime()))})),cs(this,"endScrubbing",(()=>{this.mouseDown=!1,Math.ceil(this.lastTime)===Math.ceil(this.player.media.currentTime)?this.toggleScrubbingContainer(!1):Pr.call(this.player,this.player.media,"timeupdate",(()=>{this.mouseDown||this.toggleScrubbingContainer(!1)}))})),cs(this,"listeners",(()=>{this.player.on("play",(()=>{this.toggleThumbContainer(!1,!0)})),this.player.on("seeked",(()=>{this.toggleThumbContainer(!1)})),this.player.on("timeupdate",(()=>{this.lastTime=this.player.media.currentTime}))})),cs(this,"render",(()=>{this.elements.thumb.container=lr("div",{class:this.player.config.classNames.previewThumbnails.thumbContainer}),this.elements.thumb.imageContainer=lr("div",{class:this.player.config.classNames.previewThumbnails.imageContainer}),this.elements.thumb.container.appendChild(this.elements.thumb.imageContainer);const e=lr("div",{class:this.player.config.classNames.previewThumbnails.timeContainer});this.elements.thumb.time=lr("span",{},"00:00"),e.appendChild(this.elements.thumb.time),this.elements.thumb.container.appendChild(e),Ys(this.player.elements.progress)&&this.player.elements.progress.appendChild(this.elements.thumb.container),this.elements.scrubbing.container=lr("div",{class:this.player.config.classNames.previewThumbnails.scrubbingContainer}),this.player.elements.wrapper.appendChild(this.elements.scrubbing.container)})),cs(this,"destroy",(()=>{this.elements.thumb.container&&this.elements.thumb.container.remove(),this.elements.scrubbing.container&&this.elements.scrubbing.container.remove()})),cs(this,"showImageAtCurrentTime",(()=>{this.mouseDown?this.setScrubbingContainerSize():this.setThumbContainerSizeAndPos();const e=this.thumbnails[0].frames.findIndex((e=>this.seekTime>=e.startTime&&this.seekTime<=e.endTime)),t=e>=0;let i=0;this.mouseDown||this.toggleThumbContainer(t),t&&(this.thumbnails.forEach(((t,n)=>{this.loadedImages.includes(t.frames[e].text)&&(i=n)})),e!==this.showingThumb&&(this.showingThumb=e,this.loadImage(i)))})),cs(this,"loadImage",((e=0)=>{const t=this.showingThumb,i=this.thumbnails[e],{urlPrefix:n}=i,s=i.frames[t],r=i.frames[t].text,a=n+r;if(this.currentImageElement&&this.currentImageElement.dataset.filename===r)this.showImage(this.currentImageElement,s,e,t,r,!1),this.currentImageElement.dataset.index=t,this.removeOldImages(this.currentImageElement);else{this.loadingImage&&this.usingSprites&&(this.loadingImage.onload=null);const i=new Image;i.src=a,i.dataset.index=t,i.dataset.filename=r,this.showingThumbFilename=r,this.player.debug.log(`Loading image: ${a}`),i.onload=()=>this.showImage(i,s,e,t,r,!0),this.loadingImage=i,this.removeOldImages(i)}})),cs(this,"showImage",((e,t,i,n,s,r=!0)=>{this.player.debug.log(`Showing thumb: ${s}. num: ${n}. qual: ${i}. newimg: ${r}`),this.setImageSizeAndOffset(e,t),r&&(this.currentImageContainer.appendChild(e),this.currentImageElement=e,this.loadedImages.includes(s)||this.loadedImages.push(s)),this.preloadNearby(n,!0).then(this.preloadNearby(n,!1)).then(this.getHigherQuality(i,e,t,s))})),cs(this,"removeOldImages",(e=>{Array.from(this.currentImageContainer.children).forEach((t=>{if("img"!==t.tagName.toLowerCase())return;const i=this.usingSprites?500:1e3;if(t.dataset.index!==e.dataset.index&&!t.dataset.deleting){t.dataset.deleting=!0;const{currentImageContainer:e}=this;setTimeout((()=>{e.removeChild(t),this.player.debug.log(`Removing thumb: ${t.dataset.filename}`)}),i)}}))})),cs(this,"preloadNearby",((e,t=!0)=>new Promise((i=>{setTimeout((()=>{const n=this.thumbnails[0].frames[e].text;if(this.showingThumbFilename===n){let s;s=t?this.thumbnails[0].frames.slice(e):this.thumbnails[0].frames.slice(0,e).reverse();let r=!1;s.forEach((e=>{const t=e.text;if(t!==n&&!this.loadedImages.includes(t)){r=!0,this.player.debug.log(`Preloading thumb filename: ${t}`);const{urlPrefix:e}=this.thumbnails[0],n=e+t,s=new Image;s.src=n,s.onload=()=>{this.player.debug.log(`Preloaded thumb filename: ${t}`),this.loadedImages.includes(t)||this.loadedImages.push(t),i()}}})),r||i()}}),300)})))),cs(this,"getHigherQuality",((e,t,i,n)=>{if(e<this.thumbnails.length-1){let s=t.naturalHeight;this.usingSprites&&(s=i.h),s<this.thumbContainerHeight&&setTimeout((()=>{this.showingThumbFilename===n&&(this.player.debug.log(`Showing higher quality thumb for: ${n}`),this.loadImage(e+1))}),300)}})),cs(this,"toggleThumbContainer",((e=!1,t=!1)=>{const i=this.player.config.classNames.previewThumbnails.thumbContainerShown;this.elements.thumb.container.classList.toggle(i,e),!e&&t&&(this.showingThumb=null,this.showingThumbFilename=null)})),cs(this,"toggleScrubbingContainer",((e=!1)=>{const t=this.player.config.classNames.previewThumbnails.scrubbingContainerShown;this.elements.scrubbing.container.classList.toggle(t,e),e||(this.showingThumb=null,this.showingThumbFilename=null)})),cs(this,"determineContainerAutoSizing",(()=>{(this.elements.thumb.imageContainer.clientHeight>20||this.elements.thumb.imageContainer.clientWidth>20)&&(this.sizeSpecifiedInCSS=!0)})),cs(this,"setThumbContainerSizeAndPos",(()=>{if(this.sizeSpecifiedInCSS){if(this.elements.thumb.imageContainer.clientHeight>20&&this.elements.thumb.imageContainer.clientWidth<20){const e=Math.floor(this.elements.thumb.imageContainer.clientHeight*this.thumbAspectRatio);this.elements.thumb.imageContainer.style.width=`${e}px`}else if(this.elements.thumb.imageContainer.clientHeight<20&&this.elements.thumb.imageContainer.clientWidth>20){const e=Math.floor(this.elements.thumb.imageContainer.clientWidth/this.thumbAspectRatio);this.elements.thumb.imageContainer.style.height=`${e}px`}}else{const e=Math.floor(this.thumbContainerHeight*this.thumbAspectRatio);this.elements.thumb.imageContainer.style.height=`${this.thumbContainerHeight}px`,this.elements.thumb.imageContainer.style.width=`${e}px`}this.setThumbContainerPos()})),cs(this,"setThumbContainerPos",(()=>{const e=this.player.elements.progress.getBoundingClientRect(),t=this.player.elements.container.getBoundingClientRect(),{container:i}=this.elements.thumb,n=t.left-e.left+10,s=t.right-e.left-i.clientWidth-10;let r=this.mousePosX-e.left-i.clientWidth/2;r<n&&(r=n),r>s&&(r=s),i.style.left=`${r}px`})),cs(this,"setScrubbingContainerSize",(()=>{const{width:e,height:t}=Ea(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});this.elements.scrubbing.container.style.width=`${e}px`,this.elements.scrubbing.container.style.height=`${t}px`})),cs(this,"setImageSizeAndOffset",((e,t)=>{if(!this.usingSprites)return;const i=this.thumbContainerHeight/t.h;e.style.height=e.naturalHeight*i+"px",e.style.width=e.naturalWidth*i+"px",e.style.left=`-${t.x*i}px`,e.style.top=`-${t.y*i}px`})),this.player=e,this.thumbnails=[],this.loaded=!1,this.lastMouseMoveTime=Date.now(),this.mouseDown=!1,this.loadedImages=[],this.elements={thumb:{},scrubbing:{}},this.load()}get enabled(){return this.player.isHTML5&&this.player.isVideo&&this.player.config.previewThumbnails.enabled}get currentImageContainer(){return this.mouseDown?this.elements.scrubbing.container:this.elements.thumb.imageContainer}get usingSprites(){return Object.keys(this.thumbnails[0].frames[0]).includes("w")}get thumbAspectRatio(){return this.usingSprites?this.thumbnails[0].frames[0].w/this.thumbnails[0].frames[0].h:this.thumbnails[0].width/this.thumbnails[0].height}get thumbContainerHeight(){if(this.mouseDown){const{height:e}=Ea(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});return e}return this.sizeSpecifiedInCSS?this.elements.thumb.imageContainer.clientHeight:Math.floor(this.player.media.clientWidth/this.thumbAspectRatio/4)}get currentImageElement(){return this.mouseDown?this.currentScrubbingImageElement:this.currentThumbnailImageElement}set currentImageElement(e){this.mouseDown?this.currentScrubbingImageElement=e:this.currentThumbnailImageElement=e}}const La={insertElements(e,t){Bs(t)?cr(e,this.media,{src:t}):zs(t)&&t.forEach((t=>{cr(e,this.media,t)}))},change(e){sr(e,"sources.length")?(Dr.cancelRequests.call(this),this.destroy.call(this,(()=>{this.options.quality=[],ur(this.media),this.media=null,Ys(this.elements.container)&&this.elements.container.removeAttribute("class");const{sources:t,type:i}=e,[{provider:n=oa.html5,src:s}]=t,r="html5"===n?i:"div",a="html5"===n?{}:{src:s};Object.assign(this,{provider:n,type:i,supported:Tr.check(i,n,this.config.playsinline),media:lr(r,a)}),this.elements.container.appendChild(this.media),Vs(e.autoplay)&&(this.config.autoplay=e.autoplay),this.isHTML5&&(this.config.crossorigin&&this.media.setAttribute("crossorigin",""),this.config.autoplay&&this.media.setAttribute("autoplay",""),er(e.poster)||(this.poster=e.poster),this.config.loop.active&&this.media.setAttribute("loop",""),this.config.muted&&this.media.setAttribute("muted",""),this.config.playsinline&&this.media.setAttribute("playsinline","")),ma.addStyleHook.call(this),this.isHTML5&&La.insertElements.call(this,"source",t),this.config.title=e.title,Sa.setup.call(this),this.isHTML5&&Object.keys(e).includes("tracks")&&La.insertElements.call(this,"track",e.tracks),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&ma.build.call(this),this.isHTML5&&this.media.load(),er(e.previewThumbnails)||(Object.assign(this.config.previewThumbnails,e.previewThumbnails),this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new Pa(this))),this.fullscreen.update()}),!0)):this.debug.warn("Invalid source format")}};class xa{constructor(e,t){if(cs(this,"play",(()=>Ws(this.media.play)?(this.ads&&this.ads.enabled&&this.ads.managerPromise.then((()=>this.ads.play())).catch((()=>Or(this.media.play()))),this.media.play()):null)),cs(this,"pause",(()=>this.playing&&Ws(this.media.pause)?this.media.pause():null)),cs(this,"togglePlay",(e=>(Vs(e)?e:!this.playing)?this.play():this.pause())),cs(this,"stop",(()=>{this.isHTML5?(this.pause(),this.restart()):Ws(this.media.stop)&&this.media.stop()})),cs(this,"restart",(()=>{this.currentTime=0})),cs(this,"rewind",(e=>{this.currentTime-=Hs(e)?e:this.config.seekTime})),cs(this,"forward",(e=>{this.currentTime+=Hs(e)?e:this.config.seekTime})),cs(this,"increaseVolume",(e=>{const t=this.media.muted?0:this.volume;this.volume=t+(Hs(e)?e:0)})),cs(this,"decreaseVolume",(e=>{this.increaseVolume(-e)})),cs(this,"airplay",(()=>{Tr.airplay&&this.media.webkitShowPlaybackTargetPicker()})),cs(this,"toggleControls",(e=>{if(this.supported.ui&&!this.isAudio){const t=gr(this.elements.container,this.config.classNames.hideControls),i=void 0===e?void 0:!e,n=fr(this.elements.container,this.config.classNames.hideControls,i);if(n&&zs(this.config.controls)&&this.config.controls.includes("settings")&&!er(this.config.settings)&&ea.toggleMenu.call(this,!1),n!==t){const e=n?"controlshidden":"controlsshown";Lr.call(this,this.media,e)}return!n}return!1})),cs(this,"on",((e,t)=>{Cr.call(this,this.elements.container,e,t)})),cs(this,"once",((e,t)=>{Pr.call(this,this.elements.container,e,t)})),cs(this,"off",((e,t)=>{Er(this.elements.container,e,t)})),cs(this,"destroy",((e,t=!1)=>{if(!this.ready)return;const i=()=>{document.body.style.overflow="",this.embed=null,t?(Object.keys(this.elements).length&&(ur(this.elements.buttons.play),ur(this.elements.captions),ur(this.elements.controls),ur(this.elements.wrapper),this.elements.buttons.play=null,this.elements.captions=null,this.elements.controls=null,this.elements.wrapper=null),Ws(e)&&e()):(xr.call(this),Dr.cancelRequests.call(this),dr(this.elements.original,this.elements.container),Lr.call(this,this.elements.original,"destroyed",!0),Ws(e)&&e.call(this.elements.original),this.ready=!1,setTimeout((()=>{this.elements=null,this.media=null}),200))};this.stop(),clearTimeout(this.timers.loading),clearTimeout(this.timers.controls),clearTimeout(this.timers.resized),this.isHTML5?(ma.toggleNativeControls.call(this,!0),i()):this.isYouTube?(clearInterval(this.timers.buffering),clearInterval(this.timers.playing),null!==this.embed&&Ws(this.embed.destroy)&&this.embed.destroy(),i()):this.isVimeo&&(null!==this.embed&&this.embed.unload().then(i),setTimeout(i,200))})),cs(this,"supports",(e=>Tr.mime.call(this,e))),this.timers={},this.ready=!1,this.loading=!1,this.failed=!1,this.touch=Tr.touch,this.media=e,Bs(this.media)&&(this.media=document.querySelectorAll(this.media)),(window.jQuery&&this.media instanceof jQuery||Ks(this.media)||zs(this.media))&&(this.media=this.media[0]),this.config=rr({},sa,xa.defaults,t||{},(()=>{try{return JSON.parse(this.media.getAttribute("data-plyr-config"))}catch(e){return{}}})()),this.elements={container:null,fullscreen:null,captions:null,buttons:{},display:{},progress:{},inputs:{},settings:{popup:null,menu:null,panels:{},buttons:{}}},this.captions={active:null,currentTrack:-1,meta:new WeakMap},this.fullscreen={active:!1},this.options={speed:[],quality:[]},this.debug=new ha(this.config.debug),this.debug.log("Config",this.config),this.debug.log("Support",Tr),Fs(this.media)||!Ys(this.media))return void this.debug.error("Setup failed: no suitable element passed");if(this.media.plyr)return void this.debug.warn("Target already setup");if(!this.config.enabled)return void this.debug.error("Setup failed: disabled by config");if(!Tr.check().api)return void this.debug.error("Setup failed: no support");const i=this.media.cloneNode(!0);i.autoplay=!1,this.elements.original=i;const n=this.media.tagName.toLowerCase();let s=null,r=null;switch(n){case"div":if(s=this.media.querySelector("iframe"),Ys(s)){if(r=ta(s.getAttribute("src")),this.provider=function(e){return/^(https?:\/\/)?(www\.)?(youtube\.com|youtube-nocookie\.com|youtu\.?be)\/.+$/.test(e)?oa.youtube:/^https?:\/\/player.vimeo.com\/video\/\d{0,9}(?=\b|\/)/.test(e)?oa.vimeo:null}(r.toString()),this.elements.container=this.media,this.media=s,this.elements.container.className="",r.search.length){const e=["1","true"];e.includes(r.searchParams.get("autoplay"))&&(this.config.autoplay=!0),e.includes(r.searchParams.get("loop"))&&(this.config.loop.active=!0),this.isYouTube?(this.config.playsinline=e.includes(r.searchParams.get("playsinline")),this.config.youtube.hl=r.searchParams.get("hl")):this.config.playsinline=!0}}else this.provider=this.media.getAttribute(this.config.attributes.embed.provider),this.media.removeAttribute(this.config.attributes.embed.provider);if(er(this.provider)||!Object.values(oa).includes(this.provider))return void this.debug.error("Setup failed: Invalid provider");this.type=ca;break;case"video":case"audio":this.type=n,this.provider=oa.html5,this.media.hasAttribute("crossorigin")&&(this.config.crossorigin=!0),this.media.hasAttribute("autoplay")&&(this.config.autoplay=!0),(this.media.hasAttribute("playsinline")||this.media.hasAttribute("webkit-playsinline"))&&(this.config.playsinline=!0),this.media.hasAttribute("muted")&&(this.config.muted=!0),this.media.hasAttribute("loop")&&(this.config.loop.active=!0);break;default:return void this.debug.error("Setup failed: unsupported type")}this.supported=Tr.check(this.type,this.provider,this.config.playsinline),this.supported.api?(this.eventListeners=[],this.listeners=new fa(this),this.storage=new Gr(this),this.media.plyr=this,Ys(this.elements.container)||(this.elements.container=lr("div",{tabindex:0}),ar(this.media,this.elements.container)),ma.migrateStyles.call(this),ma.addStyleHook.call(this),Sa.setup.call(this),this.config.debug&&Cr.call(this,this.elements.container,this.config.events.join(" "),(e=>{this.debug.log(`event: ${e.type}`)})),this.fullscreen=new da(this),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&ma.build.call(this),this.listeners.container(),this.listeners.global(),this.config.ads.enabled&&(this.ads=new Aa(this)),this.isHTML5&&this.config.autoplay&&this.once("canplay",(()=>Or(this.play()))),this.lastSeekTime=0,this.config.previewThumbnails.enabled&&(this.previewThumbnails=new Pa(this))):this.debug.error("Setup failed: no support")}get isHTML5(){return this.provider===oa.html5}get isEmbed(){return this.isYouTube||this.isVimeo}get isYouTube(){return this.provider===oa.youtube}get isVimeo(){return this.provider===oa.vimeo}get isVideo(){return this.type===ca}get isAudio(){return this.type===la}get playing(){return Boolean(this.ready&&!this.paused&&!this.ended)}get paused(){return Boolean(this.media.paused)}get stopped(){return Boolean(this.paused&&0===this.currentTime)}get ended(){return Boolean(this.media.ended)}set currentTime(e){if(!this.duration)return;const t=Hs(e)&&e>0;this.media.currentTime=t?Math.min(e,this.duration):0,this.debug.log(`Seeking to ${this.currentTime} seconds`)}get currentTime(){return Number(this.media.currentTime)}get buffered(){const{buffered:e}=this.media;return Hs(e)?e:e&&e.length&&this.duration>0?e.end(0)/this.duration:0}get seeking(){return Boolean(this.media.seeking)}get duration(){const e=parseFloat(this.config.duration),t=(this.media||{}).duration,i=Hs(t)&&t!==1/0?t:0;return e||i}set volume(e){let t=e;Bs(t)&&(t=Number(t)),Hs(t)||(t=this.storage.get("volume")),Hs(t)||({volume:t}=this.config),t>1&&(t=1),t<0&&(t=0),this.config.volume=t,this.media.volume=t,!er(e)&&this.muted&&t>0&&(this.muted=!1)}get volume(){return Number(this.media.volume)}set muted(e){let t=e;Vs(t)||(t=this.storage.get("muted")),Vs(t)||(t=this.config.muted),this.config.muted=t,this.media.muted=t}get muted(){return Boolean(this.media.muted)}get hasAudio(){return!this.isHTML5||(!!this.isAudio||(Boolean(this.media.mozHasAudio)||Boolean(this.media.webkitAudioDecodedByteCount)||Boolean(this.media.audioTracks&&this.media.audioTracks.length)))}set speed(e){let t=null;Hs(e)&&(t=e),Hs(t)||(t=this.storage.get("speed")),Hs(t)||(t=this.config.speed.selected);const{minimumSpeed:i,maximumSpeed:n}=this;t=function(e=0,t=0,i=255){return Math.min(Math.max(e,t),i)}(t,i,n),this.config.speed.selected=t,setTimeout((()=>{this.media&&(this.media.playbackRate=t)}),0)}get speed(){return Number(this.media.playbackRate)}get minimumSpeed(){return this.isYouTube?Math.min(...this.options.speed):this.isVimeo?.5:.0625}get maximumSpeed(){return this.isYouTube?Math.max(...this.options.speed):this.isVimeo?2:16}set quality(e){const t=this.config.quality,i=this.options.quality;if(!i.length)return;let n=[!er(e)&&Number(e),this.storage.get("quality"),t.selected,t.default].find(Hs),s=!0;if(!i.includes(n)){const e=Mr(i,n);this.debug.warn(`Unsupported quality option: ${n}, using ${e} instead`),n=e,s=!1}t.selected=n,this.media.quality=n,s&&this.storage.set({quality:n})}get quality(){return this.media.quality}set loop(e){const t=Vs(e)?e:this.config.loop.active;this.config.loop.active=t,this.media.loop=t}get loop(){return Boolean(this.media.loop)}set source(e){La.change.call(this,e)}get source(){return this.media.currentSrc}get download(){const{download:e}=this.config.urls;return Zs(e)?e:this.source}set download(e){Zs(e)&&(this.config.urls.download=e,ea.setDownloadUrl.call(this))}set poster(e){this.isVideo?ma.setPoster.call(this,e,!1).catch((()=>{})):this.debug.warn("Poster can only be set for video")}get poster(){return this.isVideo?this.media.getAttribute("poster")||this.media.getAttribute("data-poster"):null}get ratio(){if(!this.isVideo)return null;const e=$r(qr.call(this));return zs(e)?e.join(":"):e}set ratio(e){this.isVideo?Bs(e)&&Rr(e)?(this.config.ratio=$r(e),Ur.call(this)):this.debug.error(`Invalid aspect ratio specified (${e})`):this.debug.warn("Aspect ratio can only be set for video")}set autoplay(e){const t=Vs(e)?e:this.config.autoplay;this.config.autoplay=t}get autoplay(){return Boolean(this.config.autoplay)}toggleCaptions(e){na.toggle.call(this,e,!1)}set currentTrack(e){na.set.call(this,e,!1),na.setup()}get currentTrack(){const{toggled:e,currentTrack:t}=this.captions;return e?t:-1}set language(e){na.setLanguage.call(this,e,!1)}get language(){return(na.getCurrentTrack.call(this)||{}).language}set pip(e){if(!Tr.pip)return;const t=Vs(e)?e:!this.pip;Ws(this.media.webkitSetPresentationMode)&&this.media.webkitSetPresentationMode(t?ra:aa),Ws(this.media.requestPictureInPicture)&&(!this.pip&&t?this.media.requestPictureInPicture():this.pip&&!t&&document.exitPictureInPicture())}get pip(){return Tr.pip?er(this.media.webkitPresentationMode)?this.media===document.pictureInPictureElement:this.media.webkitPresentationMode===ra:null}setPreviewThumbnails(e){this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),Object.assign(this.config.previewThumbnails,e),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new Pa(this))}static supported(e,t,i){return Tr.check(e,t,i)}static loadSprite(e,t){return Qr(e,t)}static setup(e,t={}){let i=null;return Bs(e)?i=Array.from(document.querySelectorAll(e)):Ks(e)?i=Array.from(e):zs(e)&&(i=e.filter(Ys)),er(i)?null:i.map((e=>new xa(e,t)))}}var Ia;return xa.defaults=(Ia=sa,JSON.parse(JSON.stringify(Ia))),xa}));
//# sourceMappingURL=plyr.polyfilled.min.js.map
