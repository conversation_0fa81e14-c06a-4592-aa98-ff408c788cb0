@extends('front-end.layout')
@section('title')
    {{ get_label('home', 'Home') }}
@endsection
@section('content')
                                                                                    <link rel="stylesheet" href='assets/lightbox/lightbox.min.css'>
                                                                                    </link>

                                                            <header class="custom-hero-header" id="home">
                                                                <div class="page-header py-5">
                                                                    <div class="container text-center">
                                                                        <div class="row justify-content-center">
                                                                            <div class="col-lg-6">
                                                                                <h1 class="mb-3">
                                                                                    {{ $frontend_general_settings['company_title'] }}
                                                                                </h1>
                                                                                <p class="asap-sans-serif font-size-22">
                                                                                    {{ $frontend_general_settings['company_description'] }}
                                                                                </p>
                                                                                <div class="d-flex justify-content-center gap-3 mt-4 flex-wrap">
                                                                                    <a href="{{ route('login') }}" class="btn btn-gradient-dark btn-lg px-4 rounded-pill">
                                                                                        {{ get_label('get_started', 'Get Started') }}
                                                                                        <i class="fas fa-angle-right ms-2"></i>
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <section class="hero-images-section py-5">
                                                                    <div class="container">
                                                                        <div class="row justify-content-center">
                                                                            <div class="col-12 col-lg-10">
                                                                                <div class="position-relative d-flex justify-content-center align-items-center">
                                                                                    <!-- Main image, centered -->
                                                                                    <div class="main-image-container mx-auto">
                                                                                        <img src="{{ asset('assets/front-end/img/gallery/home_hero.png') }}?v={{ time() }}"
                                                                                             alt="Project Management Dashboard"
                                                                                             class="img-fluid rounded-4">
                                                                                    </div>
                                                                                    <!-- Top right floating card -->
                                                                                    <div class="floating-card top-right d-none d-md-block">
                                                                                        <img src="{{ asset('assets/front-end/img/gallery/home_hero2.png') }}"
                                                                                             alt="Analytics Dashboard"
                                                                                             class="img-fluid rounded-4 floating-shadow">
                                                                                    </div>
                                                                                    <!-- Bottom left floating card -->
                                                                                    <div class="floating-card bottom-left d-none d-md-block">
                                                                                        <img src="{{ asset('assets/front-end/img/gallery/home_hero1.png') }}"
                                                                                             alt="Task Management"
                                                                                             class="img-fluid rounded-4 floating-shadow">
                                                                                    </div>
                                                                                </div>

                                                                                <!-- Mobile stacked images -->
                                                                                <div class="d-md-none mt-4">
                                                                                    <div class="row g-3">
                                                                                        <div class="col-6">
                                                                                            <img src="{{ asset('assets/front-end/img/gallery/home_hero2.png') }}"
                                                                                                 alt="Analytics Dashboard"
                                                                                                 class="img-fluid rounded-3 shadow-sm">
                                                                                        </div>
                                                                                        <div class="col-6">
                                                                                            <img src="{{ asset('assets/front-end/img/gallery/home_hero1.png') }}"
                                                                                                 alt="Task Management"
                                                                                                 class="img-fluid rounded-3 shadow-sm">
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </section>
                                                            </header>


                    <section class="carousel-section">
                            <div class="container">
                                <!-- Main Title -->

                                <h2 class="main-title">
                                  Your All-in-one solution for smarter<br>
                                  <span class="highlight">{!! str_replace('project management', '<span style="color: #9747FF">project management</span>', 'project management') !!}</span>
                                </h2>

                                <!-- Carousel -->
                                <div class="carousel-container position-relative">
                                    <div id="featuresCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="5000">
                                        <div class="carousel-inner">
                                            <!-- Slide 1 -->
                                            <div class="carousel-item active">
                                                <div class="carousel-content">
                                                    <div class="carousel-image">
                                                        <img src="{{asset('assets/front-end/img/gallery/StreamlineProject.png')}}" alt="Streamline Projects" class="img-fluid">
                                                    </div>
                                                    <div class="carousel-text">
                                                        <h5>
                                                            {{ !empty($frontend_general_settings['feature1_title']) ? $frontend_general_settings['feature1_title'] :  get_label('streamline_projects', 'Streamline Your Projects with') . ' ' . $general_settings['company_title'] }}
                                                        </h5>
                                                        <p class="asap-sans-serif">
                                                            {{ !empty($frontend_general_settings['feature1_description']) ? $frontend_general_settings['feature1_description'] : get_label(
            'streamlineProjectDesc',
            'Take control of your projects and boost team productivity with ' .
            $general_settings['company_title'] .
            ', the all-in-one project management and task management solution. Our cloud-based platform empowers you to effortlessly organize projects, collaborate with your team, and track progress – all in one place.'
        )  }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Slide 2 -->
                                            <div class="carousel-item">
                                                <div class="carousel-content">
                                                    <div class="carousel-image">
                                                        <img src="{{asset('assets/front-end/img/gallery/EffortlessOrganization.png')}}" alt="Effortless Organization" class="img-fluid">
                                                    </div>
                                                    <div class="carousel-text">
                                                        <h5>
                                                            {{ !empty($frontend_general_settings['feature2_title']) ? $frontend_general_settings['feature2_title'] : get_label('effortless_organization', 'Effortless Organization') }}
                                                        </h5>
                                                        <p class="asap-sans-serif">
                                                            {{ !empty($frontend_general_settings['feature2_description']) ? $frontend_general_settings['feature2_description'] : $general_settings['company_title'] . get_label(
            'effortlessOrganizationDesc',
            'provides a centralized hub to create, manage, and track all your projects. Say goodbye to scattered tasks and missed deadlines – our intuitive interface keeps everything organized and accessible.'
        )  }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Slide 3 -->
                                            <div class="carousel-item">
                                                <div class="carousel-content">
                                                    <div class="carousel-image">
                                                        <img src="{{asset('assets/front-end/img/gallery/SeamLessOraganization.png')}}" alt="Seamless Collaboration" class="img-fluid">
                                                    </div>
                                                    <div class="carousel-text">
                                                        <h5>
                                                            {!! !empty($frontend_general_settings['feature3_title']) ? str_replace('Collaboration', '<span style="color: #9747FF">Collaboration</span>', $frontend_general_settings['feature3_title']) : str_replace('Collaboration', '<span style="color: #9747FF">Collaboration</span>', get_label('seamless_collaboration', 'Seamless Collaboration')) !!}
                                                        </h5>
                                                        <p class="asap-sans-serif">
                                                            {{ !empty($frontend_general_settings['feature3_description']) ? $frontend_general_settings['feature3_description'] : get_label(
            'seamlessCollaborationDesc',
            'Foster a collaborative work environment with ' .
            $general_settings['company_title'] .
            '. Assign tasks, share files, and communicate effectively with your team in real-time. Ensure everyone is on the same page and working towards a common goal.'
        )  }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Slide 4 -->
                                            <div class="carousel-item">
                                                <div class="carousel-content">
                                                    <div class="carousel-image">
                                                        <img src="{{asset('assets/front-end/img/gallery/VisualizeProjectHealth.png')}}" alt="Visualize Project Health" class="img-fluid">
                                                    </div>
                                                    <div class="carousel-text">
                                                        <h5>
                                                            {{ !empty($frontend_general_settings['feature4_title']) ? $frontend_general_settings['feature4_title'] : get_label('visualize_project_health', 'Visualize Project Health') }}
                                                        </h5>
                                                        <p class="asap-sans-serif">
                                                            {{ !empty($frontend_general_settings['feature4_description']) ? $frontend_general_settings['feature4_description'] : get_label(
            'visualizeProjectDesc',
            'Get insightful dashboards and reports to monitor project performance and identify areas for improvement.'
        )  }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Desktop Progress Indicator -->
                                        <div class="carousel-progress-indicator d-none d-md-flex justify-content-center align-items-center">
                                            <div class="progress-bar-container">
                                                <div class="progress-bar-active" id="carouselProgressBar"></div>
                                            </div>
                                        </div>

                                        <!-- Mobile Navigation Indicators -->
                                        <div class="carousel-indicators d-md-none">
                                            <button type="button" data-bs-target="#featuresCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                                            <button type="button" data-bs-target="#featuresCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                                            <button type="button" data-bs-target="#featuresCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                                            <button type="button" data-bs-target="#featuresCarousel" data-bs-slide-to="3" aria-label="Slide 4"></button>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </section>


                    <section class="features-section" id="features-section">
                        <div class="container">
                        <div class="features-header">
                            <div class="asap-sans-serif">Features</div>
                            <h2 class="features-title">
                                Every <span class="highlight">{!! str_replace('Feature', '<span style="color: #9747FF">Feature</span>', 'Feature') !!}</span> Your Team Needs To<br>
                                Complete Work Faster
                            </h2>
                        </div>

                        <div class="feature-cards-container">
                            <div class="feature-cards-wrapper">
                                @if (!empty($frontend_general_settings['features']))
                                    <!-- Row 1 - Moving Left -->
                                    <div class="feature-row">
                                        <!-- Original set -->
                                        @foreach ($frontend_general_settings['features'] as $index => $feature)
                                            @if ($index >= 0 && $index < 5)
                                                <div class="feature-card">
                                                    <div class="icon">
                                                        @if (!empty($feature['icon']))
                                                            <img src="{{ asset($feature['icon']) }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @else
                                                            <img src="{{ asset('assets/front-end/img/icons/task.svg') }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @endif
                                                    </div>
                                                    <div class="feature-title">{{ $feature['title'] }}</div>
                                                    <div class="feature-description">
                                                        {{ strlen($feature['description']) > 70 ? substr($feature['description'], 0, 70) . '...' : $feature['description'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                        <!-- Duplicate set for seamless loop -->
                                        @foreach ($frontend_general_settings['features'] as $index => $feature)
                                            @if ($index >= 0 && $index < 5)
                                                <div class="feature-card">
                                                    <div class="icon">
                                                        @if (!empty($feature['icon']))
                                                            <img src="{{ asset($feature['icon']) }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @else
                                                            <img src="{{ asset('assets/front-end/img/icons/task.svg') }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @endif
                                                    </div>
                                                    <div class="feature-title">{{ $feature['title'] }}</div>
                                                    <div class="feature-description">
                                                        {{ strlen($feature['description']) > 70 ? substr($feature['description'], 0, 70) . '...' : $feature['description'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>

                                    <!-- Row 2 - Moving Right -->
                                    <div class="feature-row">
                                        <!-- Original set -->
                                        @foreach ($frontend_general_settings['features'] as $index => $feature)
                                            @if ($index >= 5 && $index < 10)
                                                <div class="feature-card">
                                                    <div class="icon">
                                                        @if (!empty($feature['icon']))
                                                            <img src="{{ asset($feature['icon']) }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @else
                                                            <img src="{{ asset('assets/front-end/img/icons/default-feature.png') }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @endif
                                                    </div>
                                                    <div class="feature-title">{{ $feature['title'] }}</div>
                                                    <div class="feature-description">
                                                        {{ strlen($feature['description']) > 70 ? substr($feature['description'], 0, 70) . '...' : $feature['description'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                        <!-- Duplicate set for seamless loop -->
                                        @foreach ($frontend_general_settings['features'] as $index => $feature)
                                            @if ($index >= 5 && $index < 10)
                                                <div class="feature-card">
                                                    <div class="icon">
                                                        @if (!empty($feature['icon']))
                                                            <img src="{{ asset($feature['icon']) }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @else
                                                            <img src="{{ asset('assets/front-end/img/icons/default-feature.png') }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @endif
                                                    </div>
                                                    <div class="feature-title">{{ $feature['title'] }}</div>
                                                    <div class="feature-description">
                                                        {{ strlen($feature['description']) > 70 ? substr($feature['description'], 0, 70) . '...' : $feature['description'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>

                                    <!-- Row 3 - Moving Left -->
                                    <div class="feature-row">
                                        <!-- Original set -->
                                        @foreach ($frontend_general_settings['features'] as $index => $feature)
                                            @if ($index >= 10 && $index < 15)
                                                <div class="feature-card">
                                                    <div class="icon">
                                                        @if (!empty($feature['icon']))
                                                            <img src="{{ asset($feature['icon']) }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @else
                                                            <img src="{{ asset('assets/front-end/img/icons/default-feature.png') }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @endif
                                                    </div>
                                                    <div class="feature-title">{{ $feature['title'] }}</div>
                                                    <div class="feature-description">
                                                        {{ strlen($feature['description']) > 70 ? substr($feature['description'], 0, 70) . '...' : $feature['description'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                        <!-- Duplicate set for seamless loop -->
                                        @foreach ($frontend_general_settings['features'] as $index => $feature)
                                            @if ($index >= 10 && $index < 15)
                                                <div class="feature-card">
                                                    <div class="icon">
                                                        @if (!empty($feature['icon']))
                                                            <img src="{{ asset($feature['icon']) }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @else
                                                            <img src="{{ asset('assets/front-end/img/icons/default-feature.png') }}"
                                                                 alt="{{ $feature['title'] }}" />
                                                        @endif
                                                    </div>
                                                    <div class="feature-title">{{ $feature['title'] }}</div>
                                                    <div class="feature-description">
                                                        {{ strlen($feature['description']) > 70 ? substr($feature['description'], 0, 70) . '...' : $feature['description'] }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center">No features available.</div>
                                @endif
                                <div class="text-center asap-sans-serif mt-3">
                                    Many more features...
                                </div>
                            </div>
                        </div>
                        </div>
                    </section>



                                            @if (count($plans) > 0)
                                                                                        <section class="section-py" id="pricing-plans-section">
                                                                                            <div class="position-relative border-radius-xl w-100">
                                                                                                <img src="/assets/front-end/img/gallery/waves-white.svg" alt="pattern-lines"
                                                                                                    class="position-absolute top-md-0 w-100 opacity-6 start-0">
                                                                                                <div class="position-relative z-index-2 container pb-8 pt-0">
                                                                                                    <div class="row z-index-frontend mb-5">
                                                                                                        <div class="col-md-8 mx-auto text-center">
                                                                                                            <span class="asap-sans-serif mb-2">{{ get_label('pricing_plans', 'Pricing Plans') }}</span>
                                                                                                            {{-- <p class="">
                                                                                                                {{ get_label(
                                                    'seePricingDesc',
                                                    'You have Free Unlimited Updates and Premium Support on each package.',
                                                ) }}
                                                                                                                <a href="{{ route('frontend.pricing') }}"
                                                                                                                    class="small icon-move-right">{{ get_label('explore_more_plans', 'Explore More Plans') }}
                                                                                                                    <i class="fa fa-arrow-right"></i> </a>
                                                                                                            </p> --}}
                                                                                                            <h2 class="pricing-highlight">
                                                                                                                You Have Free
                                                                                                                <span class="pricing-highlight-blue">
                                                                                                                  {!! str_replace(['Unlimited Updates And', 'Premium Support'], ['<span style="color: #9747FF">Unlimited Updates And</span>', '<span style="color: #9747FF">Premium Support</span>'], 'Unlimited Updates And<br>Premium Support') !!}
                                                                                                                </span>
                                                                                                                On Each Package.
                                                                                                            </h2>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="mt-n9 pb-6">
                                                                                                @if ($plans !== null)
                                                                                                    <div class="container rounded-pill">
                                                                                                        @php
                                                                                                            // Check if there are plans with prices for each frequency or free plans
                                                                                                            $hasMonthlyPlans = $plans->where('monthly_price', '>', 0)->count() > 0 || $plans->where('plan_type', 'free')->count() > 0;
                                                                                                            $hasYearlyPlans = $plans->where('yearly_price', '>', 0)->count() > 0 || $plans->where('plan_type', 'free')->count() > 0;
                                                                                                            $hasLifetimePlans = $plans->where('lifetime_price', '>', 0)->count() > 0 || $plans->where('plan_type', 'free')->count() > 0;

                                                                                                            // Count how many tabs we have
                                                                                                            $tabCount = ($hasMonthlyPlans ? 1 : 0) + ($hasYearlyPlans ? 1 : 0) + ($hasLifetimePlans ? 1 : 0);

                                                                                                            // Set default active tab
                                                                                                            $activeTab = $hasMonthlyPlans ? 'monthly' : ($hasYearlyPlans ? 'yearly' : 'lifetime');
                                                                                                        @endphp

                                                                                                        @if ($tabCount > 1)
                                                                                                            <div class="row">
                                                                                                                <div class="col-md-6 col-12 mx-auto text-center">
                                                                                                                    <div class="nav-wrapper">
                                                                                                                        <ul class="nav nav-pills nav-fill pricing-tabs position-relative flex-row p-2" id="tabs-pricing-4"
                                                                                                                            role="tablist">
                                                                                                                            @if ($hasMonthlyPlans)
                                                                                                                                <li class="nav-item">
                                                                                                                                    <a class="nav-link {{ $activeTab == 'monthly' ? 'active' : '' }} mb-0" id="tabs-iconpricing-tab-1"
                                                                                                                                        data-bs-toggle="tab" href="#monthly" role="tab"
                                                                                                                                        aria-controls="monthly" aria-selected="{{ $activeTab == 'monthly' ? 'true' : 'false' }}">
                                                                                                                                        {{ get_label('monthly', 'Monthly') }}
                                                                                                                                    </a>
                                                                                                                                </li>
                                                                                                                            @endif
                                                                                                                            @if ($hasYearlyPlans)
                                                                                                                                <li class="nav-item">
                                                                                                                                    <a class="nav-link {{ $activeTab == 'yearly' ? 'active' : '' }} mb-0" id="tabs-iconpricing-tab-2" data-bs-toggle="tab"
                                                                                                                                        href="#yearly" role="tab" aria-controls="yearly"
                                                                                                                                        aria-selected="{{ $activeTab == 'yearly' ? 'true' : 'false' }}">
                                                                                                                                        {{ get_label('annual', 'Annual') }}
                                                                                                                                    </a>
                                                                                                                                </li>
                                                                                                                            @endif
                                                                                                                            @if ($hasLifetimePlans)
                                                                                                                                <li class="nav-item">
                                                                                                                                    <a class="nav-link {{ $activeTab == 'lifetime' ? 'active' : '' }} mb-0" id="tabs-iconpricing-tab-3" data-bs-toggle="tab"
                                                                                                                                        href="#lifetime" role="tab" aria-controls="lifetime"
                                                                                                                                        aria-selected="{{ $activeTab == 'lifetime' ? 'true' : 'false' }}">
                                                                                                                                        {{ get_label('lifetime', 'Lifetime') }}
                                                                                                                                    </a>
                                                                                                                                </li>
                                                                                                                            @endif
                                                                                                                        </ul>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        @endif

                                                                                                        <div class="tab-content tab-space">
                                                                                                    @if ($hasMonthlyPlans)
                                                                                                        <div class="tab-pane {{ $activeTab == 'monthly' ? 'active' : '' }}" id="monthly">
                                                                                                            <div class="row mt-5">
                                                                                                                @foreach ($plans->where('monthly_price', '>', 0)->merge($plans->where('plan_type', 'free'))->sortBy(function ($plan) {
                                                                                                                                                                                                                return $plan->plan_type === 'free' ? 0 : 1;
                                                                                                                                                                                                            })->unique('id') as $index => $plan)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $gradientIndex = $index % 3;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @endphp

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <div class="col-md-4 col-lg-4 col-sm-6 mb-lg-0 mb-4 gap-2 pb-3">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="card  pricing-card monthly-gradient-{{ $gradientIndex }}">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="card-header text-sm-start px-4 pb-3 pt-4 text-center pricing-card-header">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <h5 class="mb-2 fw-bold plan-title-monthly-{{ $gradientIndex }}">{{ $plan->name }}</h5>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <h3 class="font-weight-bolder mt-3">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <small class="text-dark font-weight-bolder price-text-monthly-{{ $gradientIndex }}">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @if ($plan->monthly_discounted_price > 0 && $plan->monthly_discounted_price < $plan->monthly_price)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <strike>{{ format_currency($plan->monthly_price) }}</strike>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ format_currency($plan->monthly_discounted_price) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <small class="text-lighter text-black-50 font-weight-bold text-sm">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        / {{ get_label('monthly_price', 'Monthly Price') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @else
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ format_currency($plan->monthly_price) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <small class="text-lighter text-black-50 font-weight-bold text-sm">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        / {{ get_label('monthly_price', 'Monthly Price') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </h3>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <p class="small">{{ $plan->description }}</p>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            $gradientIndex = $index % 3;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @endphp

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @if ($plan->monthly_price == 0)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <a href="{{ route('login') }}" class="btn btn-outline-plan-{{ $gradientIndex }} btn-sm w-100 border-radius-md mb-2 mt-4 text-center">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                {{ get_label('buy_now', 'Buy now') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @else
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <a href="{{ route('login') }}" class="btn btn-sm btn-plan-{{ $gradientIndex }} w-100 border-radius-md mb-2 mt-4 text-center">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                {{ get_label('buy_now', 'Buy now') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <hr class="horizontal dark my-0">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="card-body pt-0 pricing-card-body">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <div class="justify-content-start d-flex px-2 py-1">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <ul class="list-unstyled mb-4">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-monthly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_projects', 'Max Projects') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_projects == -1
                                                                                                                    ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                    : '<span class="fw-semibold">' . $plan->max_projects . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-monthly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_clients', 'Max Clients') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_clients == -1
                                                                                                                    ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                    : '<span class="fw-semibold">' . $plan->max_clients . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-monthly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_team_members', 'Max Team Members') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_team_members == -1
                                                                                                                    ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                    : '<span class="fw-semibold">' . $plan->max_team_members . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-monthly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_workspaces', 'Max Workspaces') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_worksapces == -1
                                                                                                                    ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                    : '<span class="fw-semibold">' . $plan->max_worksapces . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @if ($plan->modules)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <i class="fas fa-check me-2 check-icon-monthly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <span class="fw-semibold">{{ get_label('modules', 'Modules') }}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <ul class="list-unstyled text-smallcaps m-3 my-2 ps-0">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $modules = json_decode($plan->modules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $checkedModules = [];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $uncheckedModules = [];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                foreach (config('taskify.modules') as $moduleName => $moduleData) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $included = in_array($moduleName, $modules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if ($included) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $checkedModules[] = [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'name' => $moduleName,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'icon' => $moduleData['icon'],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'included' => true,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    } else {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $uncheckedModules[] = [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'name' => $moduleName,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'icon' => $moduleData['icon'],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'included' => false,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $sortedModules = array_merge($checkedModules, $uncheckedModules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @endphp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @foreach ($sortedModules as $module)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $iconClass = $module['included'] ? 'fa-check' : 'fa-times text-danger';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $iconColorClass = $module['included']
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ? "module-icon-included-monthly-{$gradientIndex}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        : 'module-icon-excluded';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endphp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="text-dark mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas {{ $iconClass }} me-2 {{ $iconColorClass }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="{{ $module['icon'] }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ ucfirst($module['name']) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @endforeach
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </div>
                                                                                                                @endforeach
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    @endif

                                                                                                           @if ($hasYearlyPlans)
                                                                                                            <div class="tab-pane {{ $activeTab == 'yearly' ? 'active' : '' }}" id="yearly">
                                                                                                                <div class="row mt-5">
                                                                                                                    @foreach ($plans->where('yearly_price', '>', 0)->merge($plans->where('plan_type', 'free'))->sortBy(function ($plan) {
                                                                                                                                                                                                                        return $plan->plan_type === 'free' ? 0 : 1;
                                                                                                                                                                                                                    })->unique('id') as $index => $plan)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $gradientIndex = $index % 3;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    @endphp

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="col-md-4 col-lg-4 col-sm-6 mb-lg-0 mb-4 gap-2 pb-3">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <div class="card  pricing-card yearly-gradient-{{ $gradientIndex }}">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <div class="card-header text-sm-start px-4 pb-3 pt-4 text-center pricing-card-header">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <h5 class="mb-2 fw-bold plan-title-yearly-{{ $gradientIndex }}">{{ $plan->name }}</h5>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <h3 class="font-weight-bolder mt-3">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <small class="text-dark font-weight-bolder price-text-yearly-{{ $gradientIndex }}">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @if ($plan->yearly_discounted_price > 0 && $plan->yearly_discounted_price < $plan->yearly_price)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <strike>{{ format_currency($plan->yearly_price) }}</strike>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {{ format_currency($plan->yearly_discounted_price) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <small class="text-lighter text-black-50 font-weight-bold text-sm">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                / {{ get_label('yearly_price', 'Yearly Price') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @else
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {{ format_currency($plan->yearly_price) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <small class="text-lighter text-black-50 font-weight-bold text-sm">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                / {{ get_label('yearly_price', 'Yearly Price') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </h3>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <p class="text-lighter small text-black-50">{{ $plan->description }}</p>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @if ($plan->yearly_price == 0)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <a href="{{ route('login') }}" class="btn btn-outline-plan-{{ $gradientIndex }} btn-sm w-100 border-radius-md mb-2 mt-4 text-center">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        {{ get_label('buy_now', 'Buy now') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @else
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <a href="{{ route('login') }}" class="btn btn-sm bg-gradient-dark w-100 border-radius-md mb-2 mt-4 text-center">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        {{ get_label('buy_now', 'Buy now') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <hr class="horizontal dark my-0">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <div class="card-body pt-0 pricing-card-body">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="justify-content-start d-flex px-2 py-1">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <ul class="list-unstyled mb-4">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <i class="fas fa-check me-2 check-icon-yearly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <span class="fw-semibold">{{ get_label('max_projects', 'Max Projects') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {!! $plan->max_projects == -1
                                                                                                                        ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                        : '<span class="fw-semibold">' . $plan->max_projects . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <i class="fas fa-check me-2 check-icon-yearly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <span class="fw-semibold">{{ get_label('max_clients', 'Max Clients') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {!! $plan->max_clients == -1
                                                                                                                        ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                        : '<span class="fw-semibold">' . $plan->max_clients . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <i class="fas fa-check me-2 check-icon-yearly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <span class="fw-semibold">{{ get_label('max_team_members', 'Max Team Members') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {!! $plan->max_team_members == -1
                                                                                                                        ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                        : '<span class="fw-semibold">' . $plan->max_team_members . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <i class="fas fa-check me-2 check-icon-yearly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <span class="fw-semibold">{{ get_label('max_workspaces', 'Max Workspaces') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {!! $plan->max_worksapces == -1
                                                                                                                        ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                        : '<span class="fw-semibold">' . $plan->max_worksapces . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @if ($plan->modules)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <i class="fas fa-check me-2 check-icon-yearly-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <span class="fw-semibold">{{ get_label('modules', 'Modules') }}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <ul class="list-unstyled text-smallcaps m-3 my-2 ps-0">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $modules = json_decode($plan->modules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $checkedModules = [];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $uncheckedModules = [];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        foreach (config('taskify.modules') as $moduleName => $moduleData) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            $included = in_array($moduleName, $modules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if ($included) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $checkedModules[] = [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    'name' => $moduleName,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    'icon' => $moduleData['icon'],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    'included' => true,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            } else {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $uncheckedModules[] = [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    'name' => $moduleName,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    'icon' => $moduleData['icon'],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    'included' => false,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $sortedModules = array_merge($checkedModules, $uncheckedModules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    @endphp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    @foreach ($sortedModules as $module)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            $iconClass = $module['included'] ? 'fa-check' : 'fa-times text-danger';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            $iconColorClass = $module['included']
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ? "module-icon-included-yearly-{$gradientIndex}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                : 'module-icon-excluded';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @endphp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <li class="text-dark mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <i class="fas {{ $iconClass }} me-2 {{ $iconColorClass }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <i class="{{ $module['icon'] }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {{ ucfirst($module['name']) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    @endforeach
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>
                                                                                                                    @endforeach
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        @endif

                                                                                                            @if ($hasLifetimePlans)
                                                                                                                <div class="tab-pane {{ $activeTab == 'lifetime' ? 'active' : '' }}" id="lifetime">
                                                                                                                    <div class="row mt-5">
                                                                                                                        @foreach ($plans->where('lifetime_price', '>', 0)->merge($plans->where('plan_type', 'free'))->sortBy(function ($plan) {
                                                                                                                                                                                                                                return $plan->plan_type === 'free' ? 0 : 1;
                                                                                                                                                                                                                            })->unique('id') as $index => $plan)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $gradientIndex = $index % 3;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @endphp

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <div class="col-md-4 col-lg-4 col-sm-6 mb-lg-0 mb-4 gap-2 pb-3">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <div class="card  pricing-card lifetime-gradient-{{ $gradientIndex }}">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="card-header text-sm-start px-4 pb-3 pt-4 text-center pricing-card-header">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <h5 class="mb-2 fw-bold plan-title-lifetime-{{ $gradientIndex }}">{{ $plan->name }}</h5>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <h3 class="font-weight-bolder mt-3">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <small class="text-dark font-weight-bolder price-text-lifetime-{{ $gradientIndex }}">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @if ($plan->lifetime_discounted_price > 0 && $plan->lifetime_discounted_price < $plan->lifetime_price)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <strike>{{ format_currency($plan->lifetime_price) }}</strike>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ format_currency($plan->lifetime_discounted_price) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <small class="text-lighter text-black-50 font-weight-bold text-sm">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        / {{ get_label('lifetime_price', 'Lifetime Price') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @else
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ format_currency($plan->lifetime_price) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <small class="text-lighter text-black-50 font-weight-bold text-sm">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        / {{ get_label('lifetime_price', 'Lifetime Price') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </small>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </h3>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <p class="text-lighter small text-black-50">{{ $plan->description }}</p>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @if ($plan->lifetime_price == 0)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <a href="{{ route('login') }}" class="btn btn-outline-plan-{{ $gradientIndex }} btn-sm w-100 border-radius-md mb-2 mt-4 text-center">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                {{ get_label('buy_now', 'Buy now') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @else
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <a href="{{ route('login') }}" class="btn btn-sm bg-gradient-dark w-100 border-radius-md mb-2 mt-4 text-center">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                {{ get_label('buy_now', 'Buy now') }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <hr class="horizontal dark my-0">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <div class="card-body pt-0 pricing-card-body">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <div class="justify-content-start d-flex px-2 py-1">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <ul class="list-unstyled mb-4">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-lifetime-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_projects', 'Max Projects') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_projects == -1
                                                                                                                            ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                            : '<span class="fw-semibold">' . $plan->max_projects . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-lifetime-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_clients', 'Max Clients') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_clients == -1
                                                                                                                            ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                            : '<span class="fw-semibold">' . $plan->max_clients . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-lifetime-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_team_members', 'Max Team Members') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_team_members == -1
                                                                                                                            ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                            : '<span class="fw-semibold">' . $plan->max_team_members . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas fa-check me-2 check-icon-lifetime-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <span class="fw-semibold">{{ get_label('max_workspaces', 'Max Workspaces') }}:</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {!! $plan->max_worksapces == -1
                                                                                                                            ? '<span class="fw-semibold">Unlimited</span>'
                                                                                                                            : '<span class="fw-semibold">' . $plan->max_worksapces . '</span>' !!}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @if ($plan->modules)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <li class="mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <i class="fas fa-check me-2 check-icon-lifetime-{{ $gradientIndex }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <span class="fw-semibold">{{ get_label('modules', 'Modules') }}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <ul class="list-unstyled text-smallcaps m-3 my-2 ps-0">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $modules = json_decode($plan->modules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $checkedModules = [];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $uncheckedModules = [];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                foreach (config('taskify.modules') as $moduleName => $moduleData) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $included = in_array($moduleName, $modules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if ($included) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $checkedModules[] = [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'name' => $moduleName,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'icon' => $moduleData['icon'],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'included' => true,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    } else {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $uncheckedModules[] = [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'name' => $moduleName,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'icon' => $moduleData['icon'],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            'included' => false,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ];
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                $sortedModules = array_merge($checkedModules, $uncheckedModules);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @endphp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @foreach ($sortedModules as $module)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @php
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $iconClass = $module['included'] ? 'fa-check' : 'fa-times text-danger';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    $iconColorClass = $module['included']
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ? "module-icon-included-lifetime-{$gradientIndex}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        : 'module-icon-excluded';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endphp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <li class="text-dark mb-2">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="fas {{ $iconClass }} me-2 {{ $iconColorClass }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    <i class="{{ $module['icon'] }}"></i>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ ucfirst($module['name']) }}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            @endforeach
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </li>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                @endif
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </div>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </div>
                                                                                                                        @endforeach
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            @endif

                                                                                                            <!-- If no pricing plans have price > 0 or free plans, show this message -->
                                                                                                            @if (!$hasMonthlyPlans && !$hasYearlyPlans && !$hasLifetimePlans)
                                                                                                                <div class="container mb-8 mt-8 py-3">
                                                                                                                    <div class="alert bg-gradient-warning h4 text-center">
                                                                                                                        <i class="fas fa-exclamation-circle"></i> <span class="text-black">
                                                                                                                            {{ get_label('no_plans_available', 'No Plans Available') }}</span>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            @endif
                                                                                                        </div>
                                                                                                    </div>
                                                                                                @else
                                                                                                    <div class="container mb-8 mt-8 py-3">
                                                                                                        <div class="alert bg-gradient-warning h4 text-center">
                                                                                                            <i class="fas fa-exclamation-circle"></i> <span class="text-black">
                                                                                                                {{ get_label('no_plans_available', 'No Plans Available') }}</span>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                @endif
                                                                                            </div>
                                                                                        </section>
                                            @endif

                <section class="about-section" id="about-section">
    <div class="container">
        <p class="asap-sans-serif">About us</p>
        <h1 class="main-heading">
            Simplifying <span class="highlight">{!! str_replace(['Project &', 'Task'], ['<span style="color: #9747FF">Project &</span>', '<span style="color: #9747FF">Task</span>'], 'Project &<br>Task') !!}</span> Management
        </h1>

        <div class="features-grid">
            @php
                // Define your features as an array. You can load these from config or the DB as needed.
                $features = [
                    [
                        'icon' => '<svg class="icon" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>',
                        'image' => '/assets/front-end/img/icons/manage_project.svg', // Replace with your actual image filename
                        'title' => !empty($frontend_general_settings['info_about_us_card1_title']) ? $frontend_general_settings['info_about_us_card1_title'] : get_label('manage_projects_efficiently', 'Manage Projects Efficiently'),
                        'description' => !empty($frontend_general_settings['info_about_us_card1_description']) ? $frontend_general_settings['info_about_us_card1_description'] : get_label('simplify_workflow', 'Simplify your workflow with powerful tools to organize tasks, collaborate in real-time, and track progress — all in one place.')
                    ],
                    [
                        'icon' => '<svg class="icon" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
                        'image' => '/assets/front-end/img/icons/assign.svg', // Replace with your actual image filename
                        'title' => !empty($frontend_general_settings['info_about_us_card2_title']) ? $frontend_general_settings['info_about_us_card2_title'] : get_label('assign_and_monitor_tasks', 'Assign and Monitor Tasks'),
                        'description' => !empty($frontend_general_settings['info_about_us_card2_description']) ? $frontend_general_settings['info_about_us_card2_description'] : get_label('assign_track_and_meet_deadlines', 'Easily assign tasks to team members, set priorities, and track progress in real-time to ensure nothing falls through the cracks.')
                    ],
                    [
                        'icon' => '<svg class="icon" viewBox="0 0 24 24"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-1c0-1.33 2.67-2 4-2s4 .67 4 2v1H4zM8 10c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm8 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"/></svg>',
                        'image' => '/assets/front-end/img/icons/collaboration.svg', // Replace with your actual image filename
                        'title' => !empty($frontend_general_settings['info_about_us_card3_title']) ? str_replace('Collaboration', '<span style="color: #9747FF">Collaboration</span>', $frontend_general_settings['info_about_us_card3_title']) : str_replace('Collaboration', '<span style="color: #9747ff">Collaboration</span>', get_label('enhance_collaboration','Enhance Collaboration')),
                        'description' => !empty($frontend_general_settings['info_about_us_card3_description']) ? $frontend_general_settings['info_about_us_card3_description'] : get_label('seamless_collaboration_for_success','Connect your team with real-time communication, shared tasks, and seamless file sharing to boost teamwork and productivity.')
                    ]
                ];
            @endphp

            @foreach($features as $feature)
                <div class="service-item">
                    <div class="icon-container">
                        {!! $feature['icon'] !!}
                        {{-- <img src="{{ $feature['image'] }}" alt="{{ $feature['title'] }}" class="static-icon"> --}}
                    </div>
                    <h3 class="feature-title">{!! $feature['title'] !!}</h3>
                    <p class="asap-sans-serif">{!! $feature['description'] !!}</p>
                </div>
            @endforeach
        </div>
    </div>
</section>
            <section class="feature-main-carousel">
              <div class="container">
                <div id="featureMainCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="7000">
                  <div class="carousel-inner">

                    <!-- Project Management Slide -->
                    <div class="carousel-item active">
                      <div class="feature-slide-row">
                        <div class="feature-slide-content">
                          <h5 class="sub-title">
                            {{ !empty($frontend_general_settings['project_management_subtitle']) ? $frontend_general_settings['project_management_subtitle'] : get_label('project_management', 'Project Management') }}
                          </h5>
                          <h2 class="feature-slide-title">
                            {!! !empty($frontend_general_settings['project_management_title']) ? str_replace('Intuitive Project', '<span style="color: #9747FF">Intuitive Project</span>', $frontend_general_settings['project_management_title']) : str_replace('Intuitive Project', '<span style="color: #9747FF">Intuitive Project</span>', get_label('simple_and_intuitive_project_management', 'Simple and Intuitive Project Management')) !!}
                          </h2>
                          <p class="asap-sans-serif max-width-95">
                            <?= !empty($frontend_general_settings['project_management_description']) ? $frontend_general_settings['project_management_description'] : get_label('simpleIntuitiveDesc', 'No more learning curves: Our user-friendly interface makes it easy for anyone to get started, regardless of technical expertise. Visualize your projects with intuitive dashboards and customizable views.') ?>
                          </p>
                        </div>
                        <div class="feature-slide-image">
                          <img class="feature-img feature1-main-img"
                            src="{{ asset('assets/front-end/img/gallery/feature1_main.png') }}?v={{ time() }}"
                            alt="Project Management">

                            <div class="feature-img-overlay">
                                <img src="{{ asset('assets/front-end/img/gallery/feature1_sub.png') }}"
                                alt="Project Card Overlay" />
                            </div>
                        </div>
                      </div>
                    </div>

                    <!-- Task Management Slide -->
                    <div class="carousel-item">
                      <div class="feature-slide-row">
                        <div class="feature-slide-content">
                          <h5 class="sub-title">
                            {{ !empty($frontend_general_settings['task_management_subtitle']) ? $frontend_general_settings['task_management_subtitle'] : get_label('task_management', 'Task Management') }}
                          </h5>
                          <h2 class="feature-slide-title">
                            {!! !empty($frontend_general_settings['task_management_title']) ? str_replace('Workspaces and Statuses', '<span style="color: #9747FF">Workspaces and Statuses</span>', $frontend_general_settings['task_management_title']) : str_replace('Workspaces and Statuses', '<span style="color: #9747FF">Workspaces and Statuses</span>', get_label('effective_task_organization_with_workspaces_and_statuses', 'Effective Task Organization with Workspaces and Statuses')) !!}
                          </h2>
                          <p class="asap-sans-serif max-width-95">
                            <?= !empty($frontend_general_settings['task_management_description']) ? $frontend_general_settings['task_management_description'] : get_label('effectiveTaskDesc', 'Organize chaos: Break down complex projects into manageable tasks and subtasks using our flexible workspace system. Keep track of progress with customizable task statuses (e.g., "To Do," "In Progress," "Completed") and prioritize effectively by highlighting critical tasks.') ?>
                          </p>
                        </div>
                        <div class="feature-slide-image">
                          <img class="feature-img feature1-main-img"
                            src="{{ !empty($frontend_general_settings['task_management_image']) ? asset($frontend_general_settings['task_management_image']) : asset('assets/front-end/img/gallery/Visual data-pana.png') }}"
                            alt="Task Management">
                        </div>

                      </div>
                    </div>

                    <!-- Team Collaboration Slide -->
                    <div class="carousel-item">
                      <div class="feature-slide-row">
                        <div class="feature-slide-content">
                          <h5 class="sub-title">
                            {!! !empty($frontend_general_settings['team_collaboration_subtitle']) ? str_replace('Team Collaboration', '<span style="color: #9747FF">Team Collaboration</span>', $frontend_general_settings['team_collaboration_subtitle']) : str_replace('Team Collaboration', '<span style="color: #9747FF">Team Collaboration</span>', get_label('team_collaboration', 'Team Collaboration')) !!}
                          </h5>
                          <h2 class="feature-slide-title">
                            {!! !empty($frontend_general_settings['team_collaboration_title']) ? str_replace(['Collaboration', 'Communication'], ['<span style="color: #9747FF">Collaboration</span>', '<span style="color: #9747FF">Communication</span>'], $frontend_general_settings['team_collaboration_title']) : str_replace(['Collaboration', 'Communication'], ['<span style="color: #9747FF">Collaboration</span>', '<span style="color: #9747FF">Communication</span>'], get_label('improved_team_collaboration_and_communication', 'Improved Team Collaboration and Communication')) !!}
                          </h2>
                          <p class="asap-sans-serif max-width-95">
                            {!! !empty($frontend_general_settings['team_collaboration_description']) ? str_replace(['collaboration', 'communication'], ['<span style="color: #9747FF">collaboration</span>', '<span style="color: #9747FF">communication</span>'], $frontend_general_settings['team_collaboration_description']) : str_replace(['collaboration', 'communication'], ['<span style="color: #9747FF">collaboration</span>', '<span style="color: #9747FF">communication</span>'], get_label('improvedTeamDesc', 'Break down silos: Foster seamless collaboration with built-in communication tools like comments, mentions, and discussions. Stay on the same page with real-time task updates and activity feeds, ensuring everyone is informed. Centralize all project-related information, documents, and files in one easily accessible location.')) !!}
                          </p>
                        </div>
                        <div class="feature-slide-image ">
                          <img class="feature-img feature1-main-img"
                            src="{{ !empty($frontend_general_settings['team_collaboration_image']) ? asset($frontend_general_settings['team_collaboration_image']) : asset('assets/front-end/img/gallery/Connected world-bro.png') }}"
                            alt="Team Collaboration">
                        </div>
                      </div>
                    </div>

                    <!-- Increased Productivity Slide -->
                    <div class="carousel-item">
                      <div class="feature-slide-row">
                        <div class="feature-slide-content">
                          <h5 class="sub-title">
                            {!! !empty($frontend_general_settings['increased_productivity_subtitle']) ? str_replace('Increased', '<span style="color: #9747FF">Increased</span>', $frontend_general_settings['increased_productivity_subtitle']) : str_replace('Increased', '<span style="color: #9747FF">Increased</span>', get_label('increased_productivity', 'Increased Productivity')) !!}
                          </h5>
                          <h2 class="feature-slide-title">
                            <?= !empty($frontend_general_settings['increased_productivity_title']) ? $frontend_general_settings['increased_productivity_title'] : get_label('increased_productivity_and_efficiency', 'Increased Productivity and Efficiency') ?>
                          </h2>
                          <p class="asap-sans-serif max-width-95">
                            <?= !empty($frontend_general_settings['increased_productivity_description']) ? $frontend_general_settings['increased_productivity_description'] : get_label('increasedProductivityDesc', 'Automate repetitive tasks to free up valuable time. Minimize distractions and streamline your workflow with centralized task management. Meet deadlines with confidence with built-in time tracking, milestone management, and progress reporting.') ?>
                          </p>
                        </div>
                        <div class="feature-slide-image feature1-main-img">
                          <img class="feature-img"
                            src="{{ !empty($frontend_general_settings['increased_productivity_image']) ? asset($frontend_general_settings['increased_productivity_image']) : asset('assets/front-end/img/gallery/Design stats-rafiki.png') }}"
                            alt="Increased Productivity">
                        </div>
                      </div>
                    </div>

                  </div>

                  <!-- Place progress indicator just after carousel-inner -->
              <div class="carousel-dash-progress-indicator d-flex justify-content-center align-items-center">
                <div class="progress-dash-container" id="carouselDashProgress"></div>
              </div>

                </div>
              </div>
            </section>
                                                                <section class="py-4">
                                                                    <div class="container">
                                                                        <div class="row my-5">
                                                                            <div class="text-center">
                                                                                <h2>{!! str_replace('Asked Questions', '<span style="color: #9747FF">Asked Questions</span>', get_label('frequently_asked_questions', 'Frequently Asked Questions')) !!}</h2>
                                                                                </h2>
                                                                                <p class="asap-sans-serif">{{ get_label(
        'faqsDesc',
        "A lot of people don't appreciate the moment until it’s passed. I'm not trying my hardest, and I'm
                                                                                        not trying to do",
    ) }}
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="">
                                                                                <div class="accordion" id="accordionRental">
                                                                                    <div class="accordion-item mb-3">
                                                                                        <h5 class="accordion-header" id="headingOne">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false"
                                                                                                aria-controls="collapseOne">
                                                                                                <?= get_label('what_is_a_project_management_system', 'What is a project management system?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('what_is_a_project_management_system_answer', 'A project management system is a software tool designed to help teams plan, execute, and manage projects from initiation to completion. It facilitates collaboration, task allocation, scheduling, and tracking of project progress.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <h5 class="accordion-header" id="headingTwo">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false"
                                                                                                aria-controls="collapseTwo">
                                                                                                <?= get_label('key_features_of_project_management_system', 'What are the key features of a project management system?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('key_features_of_project_management_system_answer', 'Key features typically include task management, team collaboration, project planning and scheduling, time tracking, file sharing, reporting and analytics, and integration with other tools.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <h5 class="accordion-header" id="headingThree">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false"
                                                                                                aria-controls="collapseThree">
                                                                                                <?= get_label('benefits_of_project_management_system', 'How does a project management system benefit businesses?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('benefits_of_project_management_system_answer', 'Project management systems improve productivity and efficiency by streamlining workflows, enabling better communication and collaboration among team members, providing transparency into project progress, and facilitating effective resource allocation.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <h5 class="accordion-header" id="headingFour">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false"
                                                                                                aria-controls="collapseFour">
                                                                                                <?= get_label('task_management_in_project_management_system', 'What is task management in the context of project management systems?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('task_management_in_project_management_system_answer', 'Task management involves creating, assigning, tracking, and organizing individual tasks within a project. It helps ensure that team members are aware of their responsibilities and deadlines, and allows for better coordination and prioritization of work.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <h5 class="accordion-header" id="headingFifth">
                                                                                            <button class="accordion-button font-weight-bold" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseFifth" aria-expanded="true"
                                                                                                aria-controls="collapseFifth">
                                                                                                <?= get_label('task_management_contribution_to_project_success', 'How does task management contribute to project success?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseFifth" class="accordion-collapse show collapse"
                                                                                            aria-labelledby="headingFifth" data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('task_management_contribution_to_project_success_answer', 'Effective task management ensures that project activities are completed on time and within budget, minimizes delays and bottlenecks, identifies potential issues early on, and enables efficient resource utilization.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <!-- FAQ 6 -->
                                                                                        <h5 class="accordion-header" id="headingSix">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseSix" aria-expanded="false"
                                                                                                aria-controls="collapseSix">
                                                                                                <?= get_label('multiple_projects_handling', 'Can a project management system handle multiple projects simultaneously?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseSix" class="accordion-collapse collapse" aria-labelledby="headingSix"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('multiple_projects_handling_answer', 'Yes, most project management systems are designed to support the management of multiple projects concurrently. They typically provide features for organizing projects into separate workspaces or folders, allowing teams to easily switch between projects.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <!-- FAQ 6 -->
                                                                                        <h5 class="accordion-header" id="headingSeven">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseSeven" aria-expanded="false"
                                                                                                aria-controls="collapseSeven">
                                                                                                <?= get_label('customization_of_project_management_system', 'Is it possible to customize project management systems to fit specific project requirements?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseSeven" class="accordion-collapse collapse" aria-labelledby="headingSeven"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('customization_of_project_management_system_answer', 'Many project management systems offer customization options such as creating custom task types, defining project-specific workflows, adding custom fields, and integrating with other tools to adapt to the unique needs of different projects or industries.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <!-- FAQ 6 -->
                                                                                        <h5 class="accordion-header" id="headingEight">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseEight" aria-expanded="false"
                                                                                                aria-controls="collapseEight">
                                                                                                <?= get_label('security_of_project_management_system', 'How secure are project management systems for storing sensitive project data?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseEight" class="accordion-collapse collapse" aria-labelledby="headingEight"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('security_of_project_management_system_answer', 'Project management systems prioritize data security and typically employ measures such as encryption, user authentication, access control, and regular data backups to safeguard sensitive project information from unauthorized access, loss, or theft.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <!-- FAQ 6 -->
                                                                                        <h5 class="accordion-header" id="headingNine">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseNine" aria-expanded="false"
                                                                                                aria-controls="collapseSeven">
                                                                                                <?= get_label('integration_with_other_tools', 'Can project management systems integrate with other tools and applications?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseNine" class="accordion-collapse collapse" aria-labelledby="headingNine"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('integration_with_other_tools_answer', 'Yes, project management systems often offer integrations with popular productivity tools, communication platforms, file storage services, and software development tools to streamline workflows and enhance collaboration across teams.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="accordion-item mb-3">
                                                                                        <!-- FAQ 6 -->
                                                                                        <h5 class="accordion-header" id="headingTen">
                                                                                            <button class="accordion-button font-weight-bold collapsed" type="button"
                                                                                                data-bs-toggle="collapse" data-bs-target="#collapseTen" aria-expanded="false"
                                                                                                aria-controls="collapseTen">
                                                                                                <?= get_label('choosing_right_project_management_system', 'How do I choose the right project management system for my team?') ?>
                                                                                                <i class="collapse-close fa fa-chevron-down position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                                <i class="collapse-open fa fa-chevron-up position-absolute end-0 me-3 pt-1 text-xs"
                                                                                                    aria-hidden="true"></i>
                                                                                            </button>
                                                                                        </h5>
                                                                                        <div id="collapseTen" class="accordion-collapse collapse" aria-labelledby="headingTen"
                                                                                            data-bs-parent="#accordionRental">
                                                                                            <div class="accordion-body text-dark opacity-8 text-sm asap-sans-serif">
                                                                                                <?= get_label('choosing_right_project_management_system_answer', 'When selecting a project management system, consider factors such as your team\'s size and requirements, the complexity of your projects, ease of use, scalability, customization options, pricing, customer support, and compatibility with existing tools and workflows. It\'s also helpful to try out different systems through free trials or demos to evaluate their suitability for your needs.') ?>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </section>
    <section class="py-4" id="contact-us-section">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="contact-card-custom px-0 py-0">
              <div class="contact-header-custom text-center mt-4">
                <h2 class="display-5 fw-semi-bold mb-1">
                  {!! str_replace('Contact Us', '<span style="color: #9747FF">Contact Us</span>', get_label('contact_us', 'Contact Us')) !!}
                </h2>
                <p class="contact-subtitle-custom mb-4">
                  {{ get_label('contact_us_subheading', 'Have questions or need support? Reach out to us!') }}
                </p>
              </div>
              <div class="row justify-content-center align-items-center contact-body-custom gx-lg-5 gy-4 px-4 px-sm-5 pb-4">
                <div class="col-lg-6 d-flex justify-content-center align-items-center mb-4 mb-lg-0 contact-illustration-custom">
                  <img src="assets/front-end/img/gallery/new_contact_us.png" alt="Contact Us" />
                </div>
                <div class="col-lg-6 d-flex justify-content-center">
                  <form action="{{ route('frontend.send_mail') }}" id="contact_us_form" method="POST" class="contact-form-custom w-100">
                    @csrf
                    <div class="form-group-custom">
                      <label for="name" class="form-label">
                        {{ get_label('your_name', 'Your Name') }}
                        <span class="asterisk">*</span>
                      </label>
                      <input name="name" type="text" class="form-control-custom" id="name" placeholder="{{ get_label('enter_your_name', 'Enter your name') }}">
                    </div>
                    <div class="form-group-custom">
                      <label for="email" class="form-label">
                        {{ get_label('your_email', 'Your Email') }}
                        <span class="asterisk">*</span>
                      </label>
                      <input type="email" name="email" class="form-control-custom" id="email" placeholder="{{ get_label('enter_your_email', 'Enter your email') }}">
                    </div>
                    <div class="form-group-custom">
                      <label for="message" class="form-label">
                        {{ get_label('your_message', 'Your Message') }}
                        <span class="asterisk">*</span>
                      </label>
                      <textarea class="form-control-custom" name="message" id="message" rows="4" placeholder="{{ get_label('enter_your_message', 'Enter your message') }}"></textarea>
                    </div>
                    <button type="button" id="contactUsSubmit"
                            class="btn btn-primary">{{ get_label('submit', 'Submit') }}</button>
                    <div id="loading-overlay"></div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Back to Top Button -->
<button onclick="scrollToTop()" id="backToTopBtn" title="Go to top" style="z-index: 9999; position: fixed;">
    <span class="vertical-label">TO TOP</span>
    <i class="fa fa-chevron-up fa-xs arrow-up"></i>
</button>

    <script>
        window.onscroll = function () {
            const btn = document.getElementById("backToTopBtn");
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                btn.classList.add("show");
            } else {
                btn.classList.remove("show");
            }
        };

        // Scroll to top function
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>

                                                                <script src="assets/lightbox/lightbox.min.js"></script>

    <!-- Start cookieyes banner -->
    <script id="cookieyes" type="text/javascript" src="https://cdn-cookieyes.com/client_data/a3da699c82e7f1a36d08cafa/script.js"></script>
    <!-- End cookieyes banner -->
<!-- ===== NESTKO CHATBOT WIDGET FOR LARAVEL ===== -->
<div id="nestko-chatbot-widget" class="widget-container nestko-chat-widget">
    <!-- Minimized trigger button -->
    <div id="widget-trigger" class="fade-in">
        <button onclick="toggleWidget()" class="laravel-btn laravel-btn-primary w-16 h-16 rounded-full shadow-lg hover:shadow-xl transition-all duration-200" style="width: 4rem; height: 4rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative;">
            <div class="chatbot-face" style="display: flex; flex-direction: column; align-items: center; gap: 2px;">
                <div class="chatbot-eyes" style="display: flex; gap: 6px;">
                    <div class="chatbot-eye" style="width: 6px; height: 6px; background: white; border-radius: 50%; transition: all 0.3s;"></div>
                    <div class="chatbot-eye" style="width: 6px; height: 6px; background: white; border-radius: 50%; transition: all 0.3s;"></div>
                </div>
                <div class="chatbot-mouth" style="width: 12px; height: 6px; border: 2px solid white; border-top: none; border-radius: 0 0 12px 12px;"></div>
            </div>
        </button>
    </div>

    <!-- Main chat widget -->
    <div id="widget-main" class="widget-main slide-up" style="display: none;">

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="h-full flex flex-col" style="height: 100%; display: flex; flex-direction: column;">
            <!-- Welcome Header -->
            <div class="widget-header">
                <div class="absolute top-4 right-4 flex space-x-2" style="position: absolute; top: 1rem; right: 1rem; display: flex; gap: 0.5rem;">
                    <button onclick="minimizeWidget()" class="chat-button text-white/80 hover:text-white hover:bg-white/10" style="color: rgba(255, 255, 255, 0.8);">
                        <div class="icon-minimize"></div>
                    </button>
                    <button onclick="closeWidget()" class="chat-button text-white/80 hover:text-white hover:bg-white/10" style="color: rgba(255, 255, 255, 0.8);">
                        <div class="icon-close"></div>
                    </button>
                </div>
                <div class="text-center" style="text-align: center;">
                    <h2 class="text-2xl font-bold mb-2" style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">How can we help you today?</h2>
                    <div class="text-2xl mb-4" style="font-size: 1.5rem; margin-bottom: 1rem;">👋</div>
                </div>
            </div>

            <!-- Welcome Content -->
            <div class="laravel-card-body" style="padding: 1.5rem 2rem;">
                <div class="flex items-center space-x-4 mb-8" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 2rem;">
                    <div class="avatar-container">
                        <div class="avatar-content">
                            <span class="avatar-text">NK</span>
                        </div>
                    </div>
                    <div>
                        <div class="font-semibold text-base" style="font-weight: 600; font-size: 1rem; color: #2c3e50;">NestKo Assistant</div>
                        <div class="text-sm text-gray-500" style="font-size: 0.875rem; color: #6b7280;">Let me know if you have any questions!</div>
                    </div>
                </div>

                <button onclick="startChat()" class="laravel-btn laravel-btn-primary w-full mb-6 py-3" style="width: 100%; margin-bottom: 1.5rem; padding: 0.75rem;">
                    Chat with us
                </button>

                <div class="space-y-3" style="display: flex; flex-direction: column; gap: 0.75rem;">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="showSignupForm()" style="display: flex; align-items: center; justify-content: space-between; padding: 0.75rem; background-color: #f9fafb; border-radius: 0.5rem; cursor: pointer; transition: background-color 0.2s;">
                        <div class="flex items-center space-x-2" style="display: flex; align-items: center; gap: 0.5rem;">
                            <span class="text-sm" style="font-size: 0.875rem;">⚡</span>
                            <span class="text-sm" style="font-size: 0.875rem;">Start free trial</span>
                        </div>
                        <span class="text-gray-400" style="color: #9ca3af;">→</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors" onclick="showContactForm()" style="display: flex; align-items: center; justify-content: space-between; padding: 0.75rem; background-color: #f9fafb; border-radius: 0.5rem; cursor: pointer; transition: background-color 0.2s;">
                        <div class="flex items-center space-x-2" style="display: flex; align-items: center; gap: 0.5rem;">
                            <span class="text-sm" style="font-size: 0.875rem;">📋</span>
                            <span class="text-sm" style="font-size: 0.875rem;">Contact Support</span>
                        </div>
                        <span class="text-gray-400" style="color: #9ca3af;">→</span>
                    </div>
                </div>

                <div class="text-center mt-6" style="text-align: center; margin-top: 1.5rem;">
                    <div class="text-xs text-gray-500" style="font-size: 0.75rem; color: #6b7280;">
                        Powered by <span class="text-blue-600 font-medium" style="color: #2563eb; font-weight: 500;">NestKo AI</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Screen -->
        <div id="chat-screen" class="h-full flex flex-col" style="display: none; height: 100%; flex-direction: column;">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="flex items-center space-x-4" style="display: flex; align-items: center; gap: 1rem;">
                    <div class="avatar-container">
                        <div class="avatar-content">
                            <span class="avatar-text">NK</span>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 text-base" style="font-weight: 600; color: #2c3e50; font-size: 1rem; margin: 0;">NestKo Assistant</h3>
                        <div class="text-sm text-green-500" style="font-size: 0.875rem; color: #10b981;">● Online</div>
                    </div>
                </div>
                    <div class="flex space-x-2" style="display: flex; gap: 0.5rem;">
                        <button onclick="newChat()" class="chat-button text-gray-400 hover:text-gray-600" style="color: #9ca3af;">
                            <div class="icon-new-chat"></div>
                        </button>
                        <button onclick="minimizeWidget()" class="chat-button text-gray-400 hover:text-gray-600" style="color: #9ca3af;">
                            <div class="icon-minimize"></div>
                        </button>
                        <button onclick="closeWidget()" class="chat-button text-gray-400 hover:text-gray-600" style="color: #9ca3af;">
                            <div class="icon-close"></div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Messages area -->
            <div id="messages-container" class="chat-messages" style="flex: 1; overflow-y: auto; display: flex; flex-direction: column; gap: 1.5rem;">
                <!-- Messages will be inserted here -->
            </div>

            <!-- Input area -->
            <div class="chat-input-area">
                <div class="flex items-center space-x-2" style="display: flex; align-items: center; gap: 0.5rem;">
                    <button onclick="rephraseMessage()" id="rephrase-btn" class="chat-button text-gray-400 hover:text-red-600" title="Rephrase with AI" style="color: #9ca3af;">
                        <div class="icon-rephrase"></div>
                    </button>
                    <div class="flex-1 relative" style="flex: 1; position: relative;">
                        <input
                            type="text"
                            id="message-input"
                            placeholder="Type your message..."
                            class="chat-input"
                            onkeydown="handleKeyPress(event)"
                            style="width: 100%; padding-right: 3rem;"
                        />
                        <button onclick="sendMessage()" id="send-btn" class="chat-button absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-600" style="position: absolute; right: 0.5rem; top: 50%; transform: translateY(-50%); color: #9ca3af;">
                            <div class="icon-send"></div>
                        </button>
                    </div>
                    <button onclick="toggleEmojiPicker()" class="chat-button text-gray-400 hover:text-gray-600" style="color: #9ca3af;">
                        <div class="icon-emoji"></div>
                    </button>
                </div>

                <!-- Emoji Picker -->
                <div id="emoji-picker" class="absolute bottom-20 right-4 laravel-card p-4 w-80 max-h-64 overflow-y-auto z-50" style="display: none; position: absolute; bottom: 5rem; right: 1rem; width: 20rem; max-height: 16rem; overflow-y: auto; z-index: 50; padding: 1rem;">
                    <div class="grid grid-cols-8 gap-2" id="emoji-grid" style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 0.5rem;">
                        <!-- Emojis will be inserted here -->
                    </div>
                </div>

                <div class="mt-2 text-center" style="margin-top: 0.5rem; text-align: center;">
                    <p class="text-xs text-gray-500" style="font-size: 0.75rem; color: #6b7280; margin: 0;">
                        Powered by <span class="text-blue-600 font-medium" style="color: #2563eb; font-weight: 500;">NestKo AI</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>




















@endsection