<?php return [


'redirect_url' => 'https://dev-taskify-saas.infinitietech.com/superadmin/settings/languages',
'langcode' => 'en',
'dashboard' => 'Dashboard',
'total_projects' => 'Total projects',
'total_tasks' => 'Total tasks',
'total_users' => 'Total users',
'total_clients' => 'Total clients',
'projects' => 'Projects',
'tasks' => 'Tasks',
'session_expired' => 'Session expired',
'log_in' => 'Log in',
'search_results' => 'Search results',
'no_results_found' => 'No Results Found!',
'create_project' => 'Create project',
'create' => 'Create',
'title' => 'Title',
'status' => 'Status',
'create_status' => 'Create status',
'budget' => 'Budget',
'starts_at' => 'Starts at',
'ends_at' => 'Ends at',
'description' => 'Description',
'select_users' => 'Select users',
'select_clients' => 'Select clients',
'you_will_be_project_participant_automatically' => 'You will be project participant automatically.',
'grid_view' => 'Grid view',
'update' => 'Update',
'delete' => 'Delete',
'warning' => 'Warning',
'delete_project_alert' => 'Are you sure you want to delete this project?',
'close' => 'Close',
'yes' => 'Yes',
'users' => 'Users',
'view' => 'View',
'create_task' => 'Create task',
'time' => 'Time',
'clients' => 'Clients',
'list_view' => 'List view',
'draggable' => 'Draggable',
'task' => 'Task',
'project' => 'Project',
'actions' => 'Actions',
'delete_task_alert' => 'Are you sure you want to delete this task?',
'update_project' => 'Update project',
'cancel' => 'Cancel',
'update_task' => 'Update task',
'messages' => 'MESSAGES',
'contacts' => 'Contacts',
'favorites' => 'Favorites',
'all_messages' => 'All Messages',
'search' => 'Search',
'type_to_search' => 'Type to search',
'connected' => 'Connected',
'connecting' => 'Connecting',
'no_internet_access' => 'No internet access',
'please_select_a_chat_to_start_messaging' => 'Please select a chat to start messaging',
'user_details' => 'User Details',
'delete_conversation' => 'Delete Conversation',
'shared_photos' => 'Shared Photos',
'you' => 'You',
'save_messages_secretly' => 'Save messages secretly',
'attachment' => 'Attachment',
'are_you_sure_you_want_to_delete_this' => 'Are you sure you want to delete this?',
'you_can_not_undo_this_action' => 'You can not undo this action',
'upload_new' => 'Upload New',
'dark_mode' => 'Dark Mode',
'save_changes' => 'Save Changes',
'type_a_message' => 'Type a message',
'create_meeting' => 'Create meeting',
'meetings' => 'Meetings',
'you_will_be_meeting_participant_automatically' => 'You will be meeting participant automatically.',
'update_meeting' => 'Update meeting',
'create_workspace' => 'Create workspace',
'workspaces' => 'Workspaces',
'you_will_be_workspace_participant_automatically' => 'You will be workspace participant automatically.',
'update_workspace' => 'Update workspace',
'create_todo' => 'Create todo',
'todo_list' => 'Todo list',
'priority' => 'Priority',
'low' => 'Low',
'medium' => 'Medium',
'high' => 'High',
'todo' => 'Todo',
'delete_todo_warning' => 'Are you sure you want to delete this todo?',
'account' => 'Account',
'account_settings' => 'Account settings',
'profile_details' => 'Profile details',
'update_profile_photo' => 'Update profile photo',
'allowed_jpg_png' => 'Allowed JPG or PNG.',
'first_name' => 'First name',
'last_name' => 'Last name',
'phone_number' => 'Phone number',
'email' => 'E-mail',
'role' => 'Role',
'address' => 'Address',
'city' => 'City',
'state' => 'State',
'country' => 'Country',
'zip_code' => 'Zip code',
'delete_account' => 'Delete account',
'delete_account_alert' => 'Are you sure you want to delete your account?',
'delete_account_alert_sub_text' => 'Once you delete your account, there is no going back. Please be certain.',
'create_user' => 'Create user',
'password' => 'Password',
'confirm_password' => 'Confirm password',
'profile_picture' => 'Profile picture',
'profile' => 'Profile',
'assigned' => 'Assigned',
'delete_user_alert' => 'Are you sure you want to delete this user?',
'client_projects' => 'Client projects',
'create_client' => 'Create client',
'client' => 'Client',
'company' => 'Company',
'delete_client_alert' => 'Are you sure you want to delete this client?',
'settings' => 'Settings',
'smtp_host' => 'SMTP host',
'smtp_port' => 'SMTP port',
'email_content_type' => 'Email content type',
'smtp_encryption' => 'SMTP Encryption',
'general' => 'General',
'company_title' => 'Company title',
'full_logo' => 'Full logo',
'half_logo' => 'Half logo',
'favicon' => 'Favicon',
'system_time_zone' => 'System time zone',
'select_time_zone' => 'Select time zone',
'currency_full_form' => 'Currency full form',
'currency_symbol' => 'Currency symbol',
'currency_code' => 'Currency Code',
'permission_settings' => 'Permission settings',
'create_role' => 'Create role',
'permissions' => 'Permissions',
'no_permissions_assigned' => 'No Permissions Assigned!',
'delete_role_alert' => 'Are you sure you want to delete this role?',
'pusher' => 'Pusher',
'important_settings_for_chat_feature_to_be_work' => 'Important settings for chat feature to be work',
'click_here_to_find_these_settings_on_your_pusher_account' => 'Click here to find these settings on your pusher account',
'pusher_app_id' => 'Pusher app id',
'pusher_app_key' => 'Pusher app key',
'pusher_app_secret' => 'Pusher app secret',
'pusher_app_cluster' => 'Pusher app cluster',
'no_meetings_found' => 'No meetings found!',
'delete_meeting_alert' => 'Are you sure you want to delete this meeting?',
'manage_workspaces' => 'Manage workspaces',
'edit_workspace' => 'Edit workspace',
'remove_me_from_workspace' => 'Remove me from workspace',
'chat' => 'Chat',
'todos' => 'Todos',
'languages' => 'Languages',
'no_projects_found' => 'No projects Found!',
'no_tasks_found' => 'No tasks Found!',
'no_workspace_found' => 'No workspaces found!',
'delete_workspace_alert' => 'Are you sure you want to delete this workspace?',
'preview' => 'Preview',
'primary' => 'Primary',
'secondary' => 'Secondary',
'success' => 'Success',
'danger' => 'Danger',
'info' => 'Info',
'dark' => 'Dark',
'labels' => 'Labels',
'jump_to' => 'Jump to',
'save_language' => 'Save language',
'current_language_is_your_primary_language' => 'Current language is your primary language',
'set_as_primary' => 'Set as primary',
'set_current_language_as_your_primary_language' => 'Set current language as your primary language',
'set_primary_lang_alert' => 'Are you want to set as your primary language?',
'home' => 'Home',
'project_details' => 'Project details',
'list' => 'List',
'drag_drop_update_task_status' => 'Drag and drop to update task status',
'update_role' => 'Update role',
'date_format' => 'Date format',
'this_date_format_will_be_used_in_the_system_everywhere' => 'This date format will be used in the system everywhere',
'select_date_format' => 'Select date format',
'select_status' => 'Select status',
'sort_by' => 'Sort by',
'newest' => 'Newest',
'oldest' => 'Oldest',
'most_recently_updated' => 'Most recently updated',
'least_recently_updated' => 'Least recently updated',
'important_settings_for_email_feature_to_be_work' => 'Important settings for email feature to be work',
'click_here_to_test_your_email_settings' => 'Click here to test your email settings',
'data_not_found' => 'Data Not Found',
'oops!' => 'Oops!',
'data_does_not_exists' => 'Data does not exists',
'create_now' => 'Create now',
'select_project' => 'Select project',
'select' => 'Select',
'not_assigned' => 'Not assigned',
'confirm_leave_workspace' => 'Are you sure you want leave this workspace?',
'not_workspace_found' => 'No workspace(s) found',
'must_workspace_participant' => 'You must be participant in atleast one workspace',
'pending_email_verification' => 'Pending email verification. Please check verification mail sent to you!',
'resend_verification_link' => 'Resend verification link',
'id' => 'ID',
'projects_grid_view' => 'Projects grid view',
'tasks_list' => 'Tasks list',
'task_details' => 'Task details',
'update_todo' => 'Update todo',
'user_profile' => 'User profile',
'update_user_profile' => 'Update user profile',
'update_profile' => 'Update profile',
'client_profile' => 'Client profile',
'update_client_profile' => 'Update client profile',
'todos_not_found' => 'Todos not found!',
'view_more' => 'View more',
'project_statistics' => 'Project statistics',
'task_statistics' => 'Task statistics',
'status_wise_projects' => 'Status wise projects',
'status_wise_tasks' => 'Status wise tasks',
'manage_status' => 'Manage status',
'ongoing' => 'Ongoing',
'ended' => 'Ended',
'footer_text' => 'Footer text',
'view_current_full_logo' => 'View current full logo',
'current_full_logo' => 'Current full logo',
'view_current_half_logo' => 'View current half logo',
'current_half_logo' => 'Current half logo',
'view_current_favicon' => 'View current favicon',
'current_favicon' => 'Current favicon',
'manage_statuses' => 'Manage statuses',
'statuses' => 'Statuses',
'update_status' => 'Update status',
'delete_status_warning' => 'Are you sure you want to delete this status?',
'select_user' => 'Select User',
'select_client' => 'Select client',
'tags' => 'Tags',
'create_tag' => 'Create tag',
'manage_tags' => 'Manage tags',
'update_tag' => 'Update tag',
'delete_tag_warning' => 'Are you sure you want to delete this tag?',
'filter_by_tags' => 'Filter by tags',
'filter' => 'Filter',
'select_tags' => 'Select tags',
'start_date_between' => 'Start date between',
'end_date_between' => 'End date between',
'reload_page_to_change_chart_colors' => 'Reload the page to change chart colors!',
'todos_overview' => 'Todos overview',
'done' => 'Done',
'pending' => 'Pending',
'total' => 'Total',
'not_authorized' => 'You are not authorized to perform this action.',
'un_authorized_action' => 'Un authorized action!',
'not_authorized_notice' => 'Sorry for the inconvenience but you are not authorized to perform this action',
'not_specified' => 'Not specified',
'manage_projects' => 'Manage projects',
'total_todos' => 'Total todos',
'total_meetings' => 'Total meetings',
'add_favorite' => 'Click to mark as favorite',
'remove_favorite' => 'Click to remove from favorite',
'favorite_projects' => 'Favorite projects',
'favorite' => 'Favorite',
'duplicate' => 'Duplicate',
'duplicate_warning' => 'Are you sure you want to duplicate?',
'leave_requests' => 'Leave requests',
'leave_request' => 'Leave request',
'create_leave_requet' => 'Create leave request',
'leave_from_date' => 'Leave from date',
'leave_reason' => 'Leave reason',
'days' => 'Days',
'to' => 'To',
'name' => 'Name',
'duration' => 'Duration',
'reason' => 'Reason',
'action_by' => 'Action by',
'approved' => 'Approved',
'rejected' => 'Rejected',
'update_leave_requet' => 'Update leave request',
'select_leave_editors' => 'Select leave editors',
'leave_editor_info' => 'You are leave editor',
'from_date_between' => 'From date between',
'to_date_between' => 'To date between',
'contracts' => 'Contracts',
'create_contract' => 'Create contract',
'contract_types' => 'Contract types',
'create_contract_type' => 'Create contract type',
'type' => 'Type',
'update_contract_type' => 'Update contract type',
'created_at' => 'Created at',
'signed' => 'Signed',
'partially_signed' => 'Partially signed',
'not_signed' => 'Not signed',
'value' => 'Value',
'select_contract_type' => 'Select contract type',
'update_contract' => 'Update contract',
'promisor_sign_status' => 'Promisor sign status',
'promisee_sign_status' => 'Promisee sign status',
'manage_contract_types' => 'Manage contract types',
'contract' => 'Contract',
'contract_id_prefix' => 'CTR -',
'promiser_sign' => 'Promisor sign',
'promisee_sign' => 'Promisee sign',
'created_by' => 'Created by',
'updated_at' => 'Updated at',
'last_updated_at' => 'Last updated at',
'create_signature' => 'Create signature',
'reset' => 'Reset',
'delete_signature' => 'Delete signature',
'payslips' => 'Payslips',
'print_contract' => 'Print contract',
'create_payslip' => 'Create payslip',
'payslip_month' => 'Payslip month',
'working_days' => 'Working days',
'lop_days' => 'Loss of pay days',
'paid_days' => 'Paid days',
'please_select' => 'Please select',
'basic_salary' => 'Basic salary',
'leave_deduction' => 'Leave deduction',
'over_time_hours' => 'Over time hours',
'over_time_rate' => 'Over time rate',
'over_time_payment' => 'Over time payment',
'bonus' => 'Bonus',
'incentives' => 'Incentives',
'payment_method' => 'Payment Method',
'payment_date' => 'Payment date',
'paid' => 'Paid',
'unpaid' => 'Unpaid',
'payment_status' => 'Payment status',
'create_payment_method' => 'Create payment method',
'manage_payment_methods' => 'Manage payment methods',
'payment_methods' => 'Payment Methods',
'allowances' => 'Allowances',
'update_payment_method' => 'Update payment method',
'manage_payslips' => 'Manage payslips',
'manage_contracts' => 'Manage contracts',
'allowance' => 'Allowance',
'deduction' => 'Deduction',
'amount' => 'Amount',
'manage_allowances' => 'Manage allowances',
'update_allowance' => 'Update allowance',
'create_allowance' => 'Create allowance',
'manage_deductions' => 'Manage deductions',
'create_deduction' => 'Create deduction',
'percentage' => 'Percentage',
'deductions' => 'Deductions',
'update_deduction' => 'Update deduction',
'add' => 'Add',
'remove' => 'Remove',
'total_allowances' => 'Total allowances',
'total_deductions' => 'Total deductions',
'total_earning' => 'Total earning',
'net_payable' => 'Net payable',
'payslip_id_prefix' => 'PSL (payslip ID prefix)',
'team_member' => 'Team member',
'update_payslip' => 'Update payslip',
'payslip' => 'Payslip',
'payslip_for' => 'Payslip for',
'print_payslip' => 'Print payslip',
'total_allowances_and_deductions' => 'Total allowances and deductions',
'no_deductions_found_payslip' => 'No deductions found for this payslip.',
'no_allowances_found_payslip' => 'No allowances found for this payslip.',
'total_earnings' => 'Total earnings',
'select_team_member' => 'Select team member',
'select_payment_status' => 'Select payment status',
'select_created_by' => 'Select created by',
'notes' => 'Notes',
'create_note' => 'Create note',
'upcoming_birthdays' => 'Upcoming birthdays',
'upcoming_work_anniversaries' => 'Upcoming work anniversaries',
'birthday_count' => 'Birthday count',
'days_left' => 'Days left',
'till_upcoming_days_def_30' => 'Till upcoming days : default 30',
'work_anniversary_date' => 'Work anniversary date',
'birth_day_date' => 'Birth day date',
'select_member' => 'Select member',
'update_note' => 'Update note',
'today' => 'Today',
'tomorow' => 'Tomorrow',
'day_after_tomorow' => 'Day after tomorrow',
'on_leave' => 'On leave',
'on_leave_tomorrow' => 'On leave from tomorrow',
'on_leave_day_after_tomorow' => 'On leave from day after tomorrow',
'dob_not_set_alert' => 'You DOB is not set',
'click_here_to_set_it_now' => 'Click here to set it now',
'system_updater' => 'System updater',
'update_the_system' => 'Update the system',
'hi' => 'Hi',
'active' => 'Active',
'deactive' => 'Deactive',
'status_not_active' => 'Your account is currently inactive. Please contact admin for assistance.',
'demo_restriction' => 'This operation is not allowed in demo mode.',
'please_enter_title' => 'Please enter title',
'please_enter_description' => 'Please enter description',
'time_tracker' => 'Time tracker',
'start' => 'Start',
'stop' => 'Stop',
'pause' => 'Pause',
'hours' => 'Hours',
'minutes' => 'Minutes',
'second' => 'Second',
'message' => 'Message',
'view_timesheet' => 'View timesheet',
'timesheet' => 'Timesheet',
'stop_timer_alert' => 'Are you sure you want to stop the timer?',
'user' => 'User',
'started_at' => 'Started at',
'ended_at' => 'Ended at',
'yet_to_start' => 'Yet to start',
'select_all' => 'Select All',
'users_associated_with_project' => 'Users associated with project',
'admin_has_all_permissions' => 'Admin has all the permissions',
'current_version' => 'Current version',
'delete_selected' => 'Deleted selected',
'delete_selected_alert' => 'Are you sure you want to delete selected record(s)?',
'please_wait' => 'Please wait...',
'please_select_records_to_delete' => 'Please select records to delete.',
'something_went_wrong' => 'Something went wrong.',
'please_correct_errors' => 'Please correct errors.',
'project_removed_from_favorite_successfully' => 'Project removed from favorite successfully.',
'project_marked_as_favorite_successfully' => 'Project marked as favorite successfully.',
'data_access' => 'Data Access',
'all_data_access' => 'All Data Access',
'allocated_data_access' => 'Allocated Data Access',
'date_between' => 'Date between',
'actor_id' => 'Actor ID',
'actor_name' => 'Actor name',
'actor_type' => 'Actor type',
'type_id' => 'Type ID',
'activity' => 'Activity',
'type_title' => 'Type title',
'select_activity' => 'Select activity',
'created' => 'Created',
'updated' => 'Updated',
'duplicated' => 'Duplicated',
'deleted' => 'Deleted',
'updated_status' => 'Updated status',
'unsigned' => 'Unsigned',
'select_type' => 'Select type',
'upload' => 'Upload',
'file_name' => 'File name',
'file_size' => 'File size',
'download' => 'Download',
'uploaded' => 'Uploaded',
'project_media' => 'Project media',
'task_media' => 'Task media',
'media_storage' => 'Media storage',
'select_storage_type' => 'Select storage type',
'local_storage' => 'Local storage',
'media_storage_settings' => 'Media storage settings',
'create_customers' => 'Create Customers',
'customers' => 'Customers',
'register_customer' => 'Register Customer',
'monthly_revenue' => 'Total Revenue (Monthly)',
'percentageChange' => 'Change from last month',
'monthly_customer' => 'Total Customer (Monthly)',
'monthly_subscription' => 'Active Subscriptions (Monthly)',
'totalPlans' => 'Total Plans',
'total_customers' => 'Total Customers',
'customer_counts' => 'Total Count of Customers',
'total_revenue' => 'Total Revenue',
'subscription_rate' => 'Subscription Rate',
'plan_sales' => 'Plan Sales',
'get_active_subscription_per_plan' => 'Active Subscriptions Per Plans',
'recent_transactions' => 'Recent Transactions',
'recently_added_transactions' => 'Recently Added Transactions',
'top_customers' => 'Top Customers',
'topCustomers' => 'Top 5 Customers by Maximum Purchase',
'create_plans' => 'Create Plans',
'plans' => 'Plans',
'max_projects' => 'Maximum Projects',
'max_clients' => 'Maximum Clients',
'max_team_members' => 'Maximum Team Members',
'max_workspaces' => 'Maximum Workspaces',
'plan_tenure' => 'Plan Tenure',
'price' => 'Price',
'discounted_price' => 'Discounted Price',
'monthly' => 'Mothly',
'yearly' => 'Yearly',
'lifetime' => 'Lifetime',
'module_selection' => 'Module Selection',
'create_plan_button' => 'Create Plan',
'plan_type' => 'Plan Type',
'modules' => 'Modules',
'monthly_price' => 'Monthly Price',
'monthly_discounted_price' => 'Monthly Discounted Price',
'yearly_price' => 'Yearly Price',
'yearly_discounted_price' => 'Yearly Discounted Price',
'lifetime_price' => 'Lifetime Price',
'lifetime_discounted_price' => 'Lifetime Discounted Price',
'edit_plan' => 'Edit Plan',
'update_plan_button' => 'Update Plan',
'create_subscriptions' => 'Create Subscriptions',
'subscriptions' => 'Subscriptions',
'select_plan' => 'Select Plan',
'select_tenure' => 'Select Tenure',
'charging_price' => 'Charging Price',
'offline' => 'Offline',
'bank_transfer' => 'Bank Transfer',
'payment_gateway' => 'Payment Gateway',
'plan_features' => 'Plan Features',
'user_name' => 'User Name',
'plan_name' => 'Plan Name',
'tenure' => 'Tenure',
'features' => 'Features',
'charging_currency' => 'Charging Currency',
'upgrade_subscriptions' => 'Upgrade Subscriptions',
'transactions' => 'Transactions',
'subscription_id' => 'Subscription Id',
'user_id' => 'User ID',
'transaction_id' => 'Transaction ID',
'created_date' => 'Created Date',
'buy_plan' => 'Buy Plan',
'subscription_plan' => 'Subscription plan',
'pricing_plans' => 'Pricing Plans',
'buy_plan_description1' => 'All plans include advanced tools and features to boost your productivityChoose the best plan to fit your needs',
'proceed' => 'Proceed',
'checkout' => 'Checkout',
'checkoutDescription1' => 'All plans include advanced tools and features to boost your product.',
'plan_details' => 'Plan Details',
'paypal' => 'Paypal',
'phonepe' => 'PhonePe',
'stripe' => 'Stripe',
'paystack' => 'Paystack',
'order_summary' => 'Order Summary',
'change_plan' => 'Change Plan',
'order_accept' => 'By continuing, you accept to our Terms of Services and Privacy Policy. Please note that payments are non-refundable',
'proceed_with_payment' => 'Proceed with Payment',
'remaining_days' => 'Remaining Days',
'current_plan' => 'Current Plan',
'my_subscription' => 'My Subscription',
'mySubscriptionDesc1' => 'Here is a detail of your current subscriptions',
'started_on' => 'Started On',
'end_date' => 'End Date',
'my_transactions' => 'My Transactions',
'myTrnxDesc1' => 'Here is a detail of your all transactions',
'subscription_support_closing' => 'Were here to ensure a smooth onboarding experience for you.',
'payment_successfull' => 'Payment Successfull !!!',
'subscription_support' => 'If you haven',
'subscription_added' => 'Your subscription request has been received successfully. We',
'subscription_closing' => 'Were thrilled to have you as a new subscriber and look forward to providing you with an exceptional [product/service name] experience. Thank you for choosing us!',
'payment_failed' => 'Payment Failed !!!',
'subscription_failed' => 'Your Subcription Is Not Successfully Added , Some Error Occured',
'orderSummaryDecs' => 'It can help you manage and service orders before, during and after fulfilment',
'create_estimate_invoice' => 'Create estimate/invoice',
'finance' => 'Finance',
'taxes' => 'Taxes',
'create_tax' => 'Create tax',
'update_tax' => 'Update tax',
'units' => 'Units',
'create_unit' => 'Create unit',
'update_unit' => 'Update unit',
'items' => 'Items',
'create_item' => 'Create item',
'please_enter_price' => 'Please enter price',
'unit' => 'Unit',
'unit_id' => 'Unit ID',
'update_item' => 'Update item',
'etimates_invoices' => 'Estimates/Invoices',
'sent' => 'Sent',
'accepted' => 'Accepted',
'draft' => 'Draft',
'declined' => 'Declined',
'expired' => 'Expired',
'estimate' => 'Estimate',
'invoice' => 'Invoice',
'billing_details' => 'Billing details',
'update_billing_details' => 'Update billing details',
'please_enter_name' => 'Please enter name',
'contact' => 'Contact',
'please_enter_contact' => 'Please enter contact',
'apply' => 'Apply',
'billing_details_updated_successfully' => 'Billing details updated successfully.',
'note' => 'Note',
'from_date' => 'From date',
'to_date' => 'To date',
'personal_note' => 'Personal note',
'please_enter_personal_note_if_any' => 'Please enter personal note if any',
'item' => 'Item',
'manage_items' => 'Manage items',
'product_service' => 'Product/Service',
'quantity' => 'Quantity',
'rate' => 'Rate',
'tax' => 'Tax',
'sub_total' => 'Sub total',
'final_total' => 'Final total',
'etimate_invoice' => 'Estimate/Invoice',
'estimate_id_prefix' => 'EST-',
'invoice_id_prefix' => 'INV-',
'update_estimate' => 'Update estimate',
'estimate_details' => 'Estimate details',
'invoice_details' => 'Invoice details',
'estimate_summary' => 'Estimate summary',
'invoice_summary' => 'Invoice summary',
'select_unit' => 'Select unit',
'estimate_no' => 'Estimate No.',
'invoice_no' => 'Invoice No.',
'storage_type_set_as_aws_s3' => 'Storage type is set as AWS S3 storage',
'storage_type_set_as_local' => 'Storage type is set as local storage',
'click_here_to_change' => 'Click here to change',
'expenses' => 'Expenses',
'expenses_types' => 'Expense types',
'create_expense' => 'Create expense',
'update_expense_type' => 'Update expense type',
'expense_type' => 'Expense type',
'expense_date' => 'Expense date',
'update_expense' => 'Update expense',
'payments' => 'Payments',
'create_payment' => 'Create payment',
'payment_id' => 'Payment ID',
'invoice_id' => 'Invoice ID',
'payment_method_id' => 'Payment method ID',
'payment_date_between' => 'Payment date between',
'update_payment' => 'Update payment',
'select_invoice' => 'Select invoice',
'select_payment_method' => 'Select payment method',
'fully_paid' => 'Fully paid',
'partially_paid' => 'Partially paid',
'estimates' => 'Estimates',
'invoices' => 'Invoices',
'amount_left' => 'Amount left',
'no_payments_found_invoice' => 'No payments found for this invoice.',
'no_items_found' => 'No items found',
'update_invoice' => 'Update invoice',
'view_estimate' => 'View estimate',
'view_invoice' => 'View invoice',
'currency_symbol_position' => 'Currency symbol position',
'before' => 'Before',
'after' => 'After',
'currency_formate' => 'Currency formate',
'comma_separated' => 'Comma separated',
'dot_separated' => 'Dot separated',
'decimal_points_in_currency' => 'Decimal points in currency',
'project_milestones' => 'Project milestones',
'create_milestone' => 'Create milestone',
'incomplete' => 'Incomplete',
'complete' => 'Complete',
'cost' => 'Cost',
'please_enter_cost' => 'Please enter cost',
'progress' => 'Progress',
'update_milestone' => 'Update milestone',
'learn_more' => 'Learn more',
'annual' => 'Annual',
'privacy_policy' => 'Privacy Policy',
'save' => 'Save',
'refund_policy' => 'Refund Policy',
'terms_and_conditions' => 'Terms and Conditons',
'paypal_client_id' => 'PayPal Client Id',
'paypal_secret_key' => 'PayPal Secret Key',
'payment_mode' => 'Payment Mode [ SANDBOX / PRODUCTION ]',
'sandbox' => 'Sandbox (Testing)',
'production' => 'Production (Live)',
'paypal_business_email' => 'PayPal Business Email ID',
'notification_url' => 'Notification Url (Set this as IPN notification URL in you PayPal account)',
'merchant_id' => 'Merchant Id',
'app_id' => 'App Id',
'salt_index' => 'Salt Index',
'salt_key' => 'Salt Key',
'phonepe_mode' => 'PhonePe Mode [ SANDBOX / UAT / PRODUCTION ]',
'UAT' => 'UAT',
'payment_endpoint_url' => 'Payment Endpoint Url',
'stripe_publishable_key' => 'Stripe Publishable Key',
'stripe_secret_key' => 'Stripe Secret Key',
'stripe_webhook_secret_key' => 'Stripe Webhook Secret Key',
'paystack_key_id' => 'Paystack Key Id',
'paystack_secret_key' => 'Paystack Secret Key',
'create_customer' => 'Create Customer',
'homeDesc1' => 'Unleash peak productivity with this system, your one-stop cloud-based project management platform. Streamline workflows, boost team collaboration, and stay ahead of deadlines. This system empowers you to effortlessly create tasks, assign them to team members, and track progress in real-time.',
'get_started' => 'Get Started',
'contact_us' => 'Contact Us',
'streamline_projects' => 'Streamline Your Projects with',
'streamlineProjectDesc' => 'Take control of your projects and boost team productivity with Taskify SaaS-Dev ,the all-in-one project management and task management solution. Our cloud-based platform empowers you to effortlessly organize projects, collaborate with your team, and track progress – all in one place.',
'effortlessOrganizationDesc' => 'provides a centralized hub to create, manage, and track all your projects. Say goodbye to scattered tasks and missed deadlines – our intuitive interface keeps everything organized and accessible.',
'effortless_organization' => 'Effortless Organization',
'seamless_collaboration' => 'Seamless Collaboration',
'seamlessCollaborationDesc' => 'Foster a collaborative work environment with Taskify SaaS-Dev. Assign tasks,share files, and communicate effectively with your team in real-time. Ensure everyone is on the same page and working towards a common goal.',
'visualize_project_health' => 'Visualize Project Health',
'visualizeProjectDesc' => 'Get insightful dashboards and reports to monitor project performance and identify areas for improvement.',
'pricingDesc' => 'Choose the Plan That Fits Your Needs',
'one_time_payment' => 'one time payment',
'sign_in' => 'Sign In',
'about_us' => 'About Us',
'faqs' => 'FAQs',
'quick_links' => 'Quick Links',
'contact_info_here' => 'Contact Information Here',
'about_us_desc1' => 'We are passionate about empowering teams to achieve peak productivity.',
'about_us_desc2' => 'was born from the frustration of juggling complex projects and scattered tasks. We envisioned a better way – a cloud-based platform that streamlines workflows, fosters seamless collaboration, and empowers teams to accomplish more.',
'about_us_desc3' => 'is a powerful project management and task management solution trusted by businesses of all sizes. We are dedicated to continuous innovation, ensuring our platform remains at the forefront of project management technology.',
'our_mission' => 'Our Mission',
'ourMissionDesc' => 'To simplify project management and empower teams to achieve remarkable results.',
'our_values' => 'Our Values',
'our_values_Desc1' => 'Innovation: We are constantly seeking new ways to improve',
'our_values_Desc2' => 'and push the boundaries of project management software.',
'our_values_Desc3' => 'Collaboration: We believe in the power of teamwork and strive to create a platform that fosters seamless communication and collaboration.',
'our_values_Desc4' => 'Customer Focus: We are dedicated to providing exceptional customer support and ensuring our users have the tools they need to succeed.',
'why_choose' => 'Why Choose',
'whyChooseDesc' => 'In today fast-paced world, managing projects and teams effectively can be a challenge. Our system is here to help you streamline your workflow, boost productivity, and achieve your goals. Here why our system stands out from the crowd',
'simple_and_intuitive_project_management' => 'Simple and Intuitive Project Management',
'simpleIntuitiveDesc' => 'No more learning curves: Our user-friendly interface makes it easy for anyone to get started, regardless of technical expertise. Visualize your projects with intuitive dashboards and customizable views.',
'effective_task_organization_with_workspaces_and_statuses' => 'Effective Task Organization with Workspaces and Statuses',
'effectiveTaskDesc' => 'Organize chaos: Break down complex projects into manageable tasks and subtasks using our flexible workspace system. Keep track of progress with customizable task statuses (e.g., \\\\\\\\\\\\\\\\\\\\\\\"To Do,\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\"In Progress,\\\\\\\\\\\\\\\\\\\\\\\" \\\\\\\\\\\\\\\\\\\\\\\"Completed\\\\\\\\\\\\\\\\\\\\\\\") and prioritize effectively by highlighting critical tasks.',
'improved_team_collaboration_and_communication' => 'Improved Team Collaboration and Communication',
'improvedTeamDesc' => 'Break down silos: Foster seamless collaboration with built-in communication tools like comments, mentions, and discussions. Stay on the same page with real-time task updates and activity feeds, ensuring everyone is informed. Centralize all project-related information, documents, and files in one easily accessible location.',
'increased_productivity_and_efficiency' => 'Increased Productivity and Efficiency',
'increasedProductivityDesc' => 'Automate repetitive tasks to free up valuable time. Minimize distractions and streamline your workflow with centralized task management. Meet deadlines with confidence with built-in time tracking, milestone management, and progress reporting.',
'aboutUsCTA' => 'Ready to experience the Taskify SaaS-Devdifference? Sign up and see how we can help your team achieve more!',
'taskify_features_heading' => 'Taskify SaaS-Dev Powerful Features for Efficient Project Management',
'taskify_features_subheading' => 'Streamline your team',
'project_management' => 'Project Management',
'project_management_desc' => 'Create and manage multiple projects with ease, ensuring seamless collaboration and organization.',
'task_tracking' => 'Task Tracking',
'task_tracking_desc' => 'Assign, prioritize, and track tasks efficiently, keeping your team on top of their workload.',
'user_management' => 'User Management',
'user_management_desc' => 'Manage user roles, permissions, and access levels, ensuring secure collaboration and data privacy.',
'client_management' => 'Client Management',
'client_management_desc' => 'Streamline communication and manage client relationships with dedicated client portals.',
'contract_management' => 'Contract Management',
'contract_management_desc' => 'Create, store, and manage contracts seamlessly, ensuring compliance and transparency.',
'reporting' => 'Reporting and Analytics',
'reporting_desc' => 'Gain insights into project performance with comprehensive reporting and analytics features.',
'collaboration' => 'Collaboration',
'collaboration_desc' => 'Foster seamless communication and collaboration with built-in chat, file sharing, and documentation features.',
'time_tracking' => 'Time Tracking',
'time_tracking_desc' => 'Monitor time spent on tasks and projects, enabling accurate billing and productivity analysis.',
'integrations' => 'Integrations',
'integrations_desc' => 'Connect Taskify with your favorite tools and services for a seamless workflow experience.',
'payment_gateways' => 'Payment Gateways',
'payment_gateways_desc' => 'Accept payments securely through integrated gateways like Stripe, PayPal, Paystack, and PhonePe.',
'security' => 'Security and Compliance',
'security_desc' => 'Enjoy peace of mind with robust security measures and compliance with industry standards.',
'customization' => 'Customization',
'customization_desc' => 'Tailor Taskify to your specific needs with our flexible customization options and integrations.',
'frequently_asked_questions' => 'Frequently Asked Questions',
'faqSubheading' => 'Find answers to common questions about our Project & Task Management System.',
'what_is_a_project_management_system' => 'What is a project management system?',
'what_is_a_project_management_system_answer' => 'A project management system is a software tool designed to help teams plan, execute, and manage projects from initiation to completion. It facilitates collaboration, task allocation, scheduling, and tracking of project progress.',
'key_features_of_project_management_system' => 'What are the key features of a project management system?',
'key_features_of_project_management_system_answer' => 'Key features typically include task management, team collaboration, project planning and scheduling, time tracking, file sharing, reporting and analytics, and integration with other tools.',
'benefits_of_project_management_system' => 'How does a project management system benefit businesses?',
'benefits_of_project_management_system_answer' => 'Project management systems improve productivity and efficiency by streamlining workflows, enabling better communication and collaboration among team members, providing transparency into project progress, and facilitating effective resource allocation.',
'task_management_in_project_management_system' => 'What is task management in the context of project management systems?',
'task_management_in_project_management_system_answer' => 'Task management involves creating, assigning, tracking, and organizing individual tasks within a project. It helps ensure that team members are aware of their responsibilities and deadlines, and allows for better coordination and prioritization of work.',
'task_management_contribution_to_project_success' => 'How does task management contribute to project success?',
'task_management_contribution_to_project_success_answer' => 'Effective task management ensures that project activities are completed on time and within budget, minimizes delays and bottlenecks, identifies potential issues early on, and enables efficient resource utilization.',
'multiple_projects_handling' => 'Can a project management system handle multiple projects simultaneously?',
'multiple_projects_handling_answer' => 'Yes, most project management systems are designed to support the management of multiple projects concurrently. They typically provide features for organizing projects into separate workspaces or folders, allowing teams to easily switch between projects.',
'customization_of_project_management_system' => 'Is it possible to customize project management systems to fit specific project requirements?',
'customization_of_project_management_system_answer' => 'Many project management systems offer customization options such as creating custom task types, defining project-specific workflows, adding custom fields, and integrating with other tools to adapt to the unique needs of different projects or industries.',
'security_of_project_management_system' => 'How secure are project management systems for storing sensitive project data?',
'security_of_project_management_system_answer' => 'Project management systems prioritize data security and typically employ measures such as encryption, user authentication, access control, and regular data backups to safeguard sensitive project information from unauthorized access, loss, or theft.',
'integration_with_other_tools' => 'Can project management systems integrate with other tools and applications?',
'integration_with_other_tools_answer' => 'Yes, project management systems often offer integrations with popular productivity tools, communication platforms, file storage services, and software development tools to streamline workflows and enhance collaboration across teams.',
'choosing_right_project_management_system' => 'How do I choose the right project management system for my team?',
'choosing_right_project_management_system_answer' => 'When selecting a project management system, consider factors such as your team \\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\"s size and requirements, the complexity of your projects, ease of use, scalability, customization options, pricing, customer support, and compatibility with existing tools and workflows. It',
'contact_us_subheading' => 'Have questions or need support? Reach out to us!',
'your_name' => 'Your Name',
'your_email' => 'Your Email',
'enter_your_name' => 'Enter your name',
'enter_your_email' => 'Enter your email',
'your_message' => 'Your Message',
'enter_your_message' => 'Enter your message',
'submit' => 'Submit',
'login' => 'Login',
'register' => 'Register',
'forgot_password' => 'Forgot Password?',
'login_register_heading' => 'Login or Register',
'login_register_subheading' => 'Access your account or create a new one to start managing your projects.',
'enter_your_password' => 'Please enter your password',
'enter_your_phone_number' => 'Please Enter YourPhone Number',
'activity_log' => 'Activity Log',
'active_subscription' => 'Active Subscription',
'renew_manage_plan' => 'Renew or Manage Plan',
'subscription_history' => 'Subscription History',
'explore_more_features' => 'Explore More Features',
'taskify_features_heading2' => 'Empower your workflow with features designed to streamline your day.',
'create_new_plan' => 'Create New Plan',
'plan_image' => 'Plan Image',
'inactive' => 'Inactive',
'read_more_about_us' => 'Read More About Us',
'maximizing_efficiency' => 'Maximizing Efficiency',
'pricing' => 'Pricing',
'see_our_pricing' => 'See our pricing',
'seePricingDesc' => 'You have Free Unlimited Updates and Premium Support on each package.',
'maxEffiencyDesc' => 'Efficiency is paramount in project management. We streamline processes, foster teamwork, and minimize inefficiencies, ensuring smooth project execution and success.',
'enter_your_emailDesc' => 'Enter your email and we will send you password reset link',
'back_to_login' => 'Back to login',
'team_collaboration' => 'Team Collaboration',
'teamCollabDesc' => 'Enhance team productivity and communication with our intuitive collaboration platform,facilitating seamless coordination and information sharing.',
'login_as_superadmin' => 'Login As SuperAdmin',
'login_as_admin' => 'Login As Admin',
'login_as_team_member' => 'Login As Team Member',
'login_as_client' => 'Login As Client',
'empowering_teamsDesc' => 'Empowering Teams: Your Path to Productivity and Success',
'unlimited' => 'Unilimited',
'faqsDesc' => 'A lot of people dont appreciate the moment until its passed. I am not trying my hardest, and I am not trying to do',
'chat_messages' => 'Chat Messages',
'chat_messagesDesc' => 'Enable real-time communication among team members with built-in chat messaging.',
'virtual_meetings' => 'Virtual Meetings',
'virtual_meetingsDesc' => 'Organize virtual meetings and video conferences to facilitate remote collaboration.',
'payslipsDesc' => 'Generate and distribute payslips to employees securely,ensuring transparency in payroll management.',
'finance_management' => 'Finance Management',
'finance_managementDesc' => 'Track expenses, manage budgets, and calculate taxes to maintain financial stability and compliance.',
'team_engagement' => 'Team Engagement',
'team_engagementDesc' => 'Celebrate upcoming birthdays and work anniversaries, and stay updated on team members leave status to foster a positive work environment.',
'elegant_dashboard' => 'Elegant Dashboard',
'elegant_dashboardDesc' => 'Access a visually appealing and comprehensive dashboard that provides key insights and metrics about your projects and tasks.',
'multi_language_support' => 'Multi-Language Support',
'multi_language_supportDesc' => 'Enable users to switch between multiple languages to accommodate diverse teams and clients.',
'workspace_management' => 'Workspace Management',
'workspace_managementDesc' => 'Organize projects, tasks, and team members into separate workspaces for better organization and efficiency.',
'explore_more_plans' => 'Explore More Plans',
'project_management_and_task_management_system' => 'Project Management and Task Management System',
'enhance_team_collaboration_and_productivity' => 'Enhance Team Collaboration and Productivity',
'streamlined_collaboration_for_productivity' => 'Streamlined collaboration for productivity',
'manage_projects_efficiently' => 'Manage Projects Efficiently',
'simplify_project_organization_for_focus' => 'Simplify project organization for focus.',
'assign_and_monitor_tasks' => 'Assign and Monitor Tasks',
'assign_track_and_meet_deadlines' => 'Assign, track, and meet deadlines.',
'enhance_collaboration' => 'Enhance Collaboration',
'seamless_collaboration_for_success' => 'Seamless collaboration for success.',
'user_friendly_no_learning_curve' => 'User-friendly, no learning curve.',
'non_tech_users_start_easily' => 'Non-tech users start easily.',
'intuitive_project_dashboards' => 'Intuitive project dashboards.',
'customizable_project_views' => 'Customizable project views.',
'task_management' => 'Task Management',
'subdivide_tasks_for_organization' => 'Subdivide tasks for organization.',
'customizable_progress_tracking' => 'Customizable progress tracking.',
'flexible_project_workspace' => 'Flexible project workspace.',
'highlight_critical_tasks' => 'Highlight critical tasks.',
'seamless_collaboration_tools' => 'Seamless collaboration tools.',
'real_time_task_updates' => 'Real-time task updates.',
'centralized_project_data' => 'Centralize project data',
'foster_teamwork_break_silos' => 'Foster teamwork, break silos',
'centralized_focused_workflow' => 'Centralized, focused workflow',
'confident_deadline_tracking' => 'Confident deadline tracking',
'efficient_streamlined_processes' => 'Efficient streamlined processes',
'increased_productivity' => 'Increased Productivity',
'progress_ensures_timely_completion' => 'Progress ensures timely completion.',
'system_overview' => 'System Overview',
'discover_our_system' => 'Discover Our System',
'buy_now' => 'Buy now',
'subcription_rate_of_plans' => 'Subscription Rate of Plans',
'total_count_of_customers' => 'Total Count of Customers',
'total_revrenue' => 'Total Revenue',
'total_revenue_obtained' => 'Total Revenue obtained',
'my_profile' => 'My Profile',
'logout' => 'Logout',
'manage_tasks_and_assignments_efficiently' => 'Manage tasks and assignments efficiently.',
'take_and_organize_notes_for_better_productivity' => 'Take and organize notes for better productivity',
'schedule_and_organize_meetings_with_team_members' => 'Schedule and organize meetings with team members',
'communicate_with_team_members_in_real_time' => 'Communicate with team members in real time',
'create_and_manage_to_do_lists_for_tasks_and_projects' => 'Create and manage to do lists for tasks and projects',
'manage_contracts_and_agreements_with_clients' => 'Manage contracts and agreements with clients',
'view_and_manage_payslips_for_employees' => 'View and manage payslips for employees',
'create_and_manange_expenses_payments_and_invoice_estimates' => 'Create and Manange Expenses Payments and Invoice Estimates',
'if_you_want_to_make_this_plan_free_turn_this_off' => 'If you want to make this plan free turn this off',
'upgrade' => 'Upgrade',
'footer_logo' => 'Footer Logo',
'general_settings' => 'General settings',
'email_settings' => 'E-mail settings',
'payment_method_settings' => 'Payment method settings',
'support_email' => 'Support Email',
'superadmin_has_all_permissions' => 'Super Admin has all the permissions',
'value_must_be_greater_then_0' => 'Value must be greater then 0',
'not_greater_then_100' => 'Not greater Then 100',
'members_on_leave' => 'Members on leave',
'filter_by_status' => 'Filter by status',
'color' => 'COLOR',
'enter_title' => 'Enter Title',
'not_found' => 'Not Found',
'like_admin_selected_users_will_be_able_to_update_and_create_leaves_for_other_members' => 'Like admin, selected users will be able to update and create leavesfor other members',
'member' => 'Member',
'start_date' => 'Start date',
'file' => 'File',
'notification_templates' => 'Notification Templates',
'sms' => 'SMS',
'whatsapp' => 'WhatsApp',
'system' => 'System',
'default_email_template_info' => 'A Default Subject and Message Will Be Used if a Specific Email Notification Template Is Not Set',
'default_sms_template_info' => 'A Default Message Will Be Used if a Specific SMS Notification Template Is Not Set.',
'default_whatsapp_template_info' => 'A Default Message Will Be Used if a Specific WhatsApp Notification Template Is Not Set.',
'default_system_template_info' => 'A Default Title and Message Will Be Used if a Specific System Notification Template Is Not Set.',
'workspace_assignment' => 'Workspace assignment',
'meeting_assignment' => 'Meeting assignment',
'assignment' => 'Assignment',
'status_updation' => 'Status Updation',
'account_creation_email_info' => 'This template will be used for the email notification sent to notify users/clients about the successful creation of their account.',
'verify_user_client_email_info' => 'This template will be used for the email sent for verifying new user/client creation.',
'forgot_password_email_info' => 'This template will be used for the email notification sent to users/clients to reset their password if they have forgotten it.',
'project_assignment_email_info' => 'This template will be used for the email notification sent to users/clients when they are assigned a project.',
'task_assignment_email_info' => 'This template will be used for the email notification sent to users/clients when they are assigned a task.',
'workspace_assignment_email_info' => 'This template will be used for the email notification sent to users/clients when they are added to a workspace.',
'meeting_assignment_email_info' => 'This template will be used for the email notification sent to users/clients when they are added to a meeting.',
'leave_request_creation_email_info' => 'This Template Will Be Used for the Email notification sent to the Admin and Leave Editors Upon the Creation of a Leave Request.',
'creation' => 'Creation',
'team_member_on_leave_alert' => 'Team Member on Leave Alert',
'project_assignment_sms_info' => 'This template will be used for the SMS notification sent to users/clients when they are assigned a project.',
'possible_placeholders' => 'Possible placeholders',
'project_assignment_sms_will_not_sent' => 'If Deactive, project assignment SMS  won',
'account_creation_email_will_not_sent' => 'If Deactive, account creation email  won',
'leave_request_creation_whatsapp_will_not_sent' => 'If Deactive, Leave Request Creation Whatsapp Notification  won',
'project_assignment_email_will_not_sent' => 'If Deactive, project assignment email  won',
'task_assignment_email_will_not_sent' => 'If Deactive, task assignment email  won',
'workspace_assignment_email_will_not_sent' => 'If Deactive, workspace assignment email won',
'meeting_assignment_email_will_not_sent' => 'If Deactive, meeting assignment email  won',
'leave_request_creation_email_will_not_sent' => 'If Deactive, Leave Request Creation Email Won',
'task_assignment_sms_will_not_sent' => 'If Deactive, task assignment SMS  won',
'project_assignment_whatsapp_will_not_sent' => 'If Deactive, project assignment whatsapp notification  won',
'task_assignment_whatsapp_will_not_sent' => 'If Deactive, task assignment whatsapp notification  won',
'workspace_assignment_whatsapp_will_not_sent' => 'If Deactive, workspace assignment whatsapp notification won',
'meeting_assignment_whatsapp_will_not_sent' => 'If Deactive, meeting assignment whatsapp notification won',
'project_status_updation_whatsapp_will_not_sent' => 'If Deactive, Project Status Updation Whatsapp Notification  won',
'team_member_on_leave_alert_email_will_not_sent' => 'If Deactive, Team Member on Leave Alert Email  won',
'task_status_updation_sms_info' => 'This Template Will Be Used for the SMS notification sent to the Users/Clients Upon the Status Updation of a Task.',
'all_available_placeholders' => 'All available placeholders',
'available_placeholders' => 'Available placeholders',
'account_creation' => 'Account creation',
'email_verification' => 'Email verification',
'subject' => 'Subject',
'task_assignment_sms_info' => 'This template will be used for the SMS notification sent to users/clients when they are assigned a task.',
'workspace_assignment_sms_info' => 'This template will be used for the SMS notification sent to users/clients when they are added to a workspace.',
'meeting_assignment_sms_info' => 'This template will be used for the SMS notification sent to users/clients when they are added to a meeting.',
'leave_request_creation_sms_info' => 'This Template Will Be Used for the SMS notification sent to the Admin and Leave Editors Upon the Creation of a Leave Request.',
'leave_request_status_updation_sms_info' => 'This Template Will Be Used for the SMS notification sent to the Admin/Leave Editors/Requestee Upon the Status Updation of a Leave Request.',
'team_member_on_leave_alert_sms_info' => 'This template will be used for the SMS notification sent to team members upon approval of a leave request, informing them about the absence of the requestee.',
'project_assignment_whatsapp_info' => 'This template will be used for the whatsApp notification sent to users/clients when they are assigned a project.',
'task_assignment_whatsapp_info' => 'This template will be used for the whatsapp notification sent to users/clients when they are assigned a task.',
'workspace_assignment_whatsapp_info' => 'This template will be used for the whatsapp notification sent to users/clients when they are added to a workspace.',
'meeting_assignment_whatsapp_info' => 'This template will be used for the whatsapp notification sent to users/clients when they are added to a meeting.',
'leave_request_creation_whatsapp_info' => 'This Template Will Be Used for the Whatsapp notification sent to the Admin and Leave Editors Upon the Creation of a Leave Request.',
'leave_request_status_updation_whatsapp_info' => 'This Template Will Be Used for the Whatsapp notification sent to the Admin/Leave Editors/Requestee Upon the Status Updation of a Leave Request.',
'team_member_on_leave_alert_whatsapp_info' => 'This template will be used for the WhatsApp notification sent to team members upon approval of a leave request, informing them about the absence of the requestee.',
'project_assignment_system_info' => 'This template will be used for the system notification sent to users/clients when they are assigned a project.',
'task_assignment_system_info' => 'This template will be used for the system notification sent to users/clients when they are assigned a task.',
'task_status_updation_system_info' => 'This Template Will Be Used for the System notification sent to the Users/Clients Upon the Status Updation of a Task.',
'workspace_assignment_system_info' => 'This template will be used for the system notification sent to users/clients when they are added to a workspace.',
'meeting_assignment_system_info' => 'This template will be used for the system notification sent to users/clients when they are added to a meeting.',
'leave_request_creation_system_info' => 'This Template Will Be Used for the System notification sent to the Admin and Leave Editors Upon the Creation of a Leave Request.',
'leave_request_status_updation_system_info' => 'This Template Will Be Used for the System notification sent to the Admin/Leave Editors/Requestee Upon the Status Updation of a Leave Request.',
'team_member_on_leave_alert_system_info' => 'This template will be used for the system notification sent to team members upon approval of a leave request, informing them about the absence of the requestee.',
'leave_request_status_updation_email_info' => 'This Template Will Be Used for the Email notification sent to the Admin/Leave Editors/Requestee Upon the Status Updation of a Leave Request.',
'team_member_on_leave_alert_email_info' => 'This template will be used for the email notification sent to team members upon approval of a leave request, informing them about the absence of the requestee.',
'project_status_updation_email_info' => 'This Template Will Be Used for the Email notification sent to the Users/Clients Upon the Status Updation of a Project.',
'task_status_updation_email_info' => 'This Template Will Be Used for the Email notification sent to the Users/Clients Upon the Status Updation of a Task.',
'project_status_updation_sms_info' => 'This Template Will Be Used for the SMS notification sent to the Users/Clients Upon the Status Updation of a Project.',
'project_status_updation_whatsapp_info' => 'This Template Will Be Used for the Whatsapp notification sent to the Users/Clients Upon the Status Updation of a Project.',
'task_status_updation_whatsapp_info' => 'This Template Will Be Used for the Whatsapp notification sent to the Users/Clients Upon the Status Updation of a Task.',
'project_status_updation_system_info' => 'This Template Will Be Used for the System notification sent to the Users/Clients Upon the Status Updation of a Project.',
'reset_to_default' => 'Reset to default',
'sms_gateway_wa' => 'SMS gateway/WhatsApp',
'sms_gateway_wa_settings' => 'SMS gateway/WhatsApp settings',
'preferences' => 'Preferences',
'notification_preferences' => 'Notification Preferences',
'project_assignment' => 'Project assignment',
'task_assignment' => 'Task assignment',
'project_status_updation' => 'Project Status Updation',
'task_status_updation' => 'Task Status Updation',
'leave_request_creation' => 'Leave Request Creation',
'leave_request_status_updation' => 'Leave Request Status Updation',
'mark_all_notifications_as_read_alert' => 'Are you sure you want to mark all notifications as read?',
'confirm' => 'Confirm',
'no_unread_notifications' => 'No unread notifications',
'notifications' => 'Notifications',
'update_notifications_status_alert' => 'Are you sure you want to update notification status?',
'view_all' => 'View all',
'calendar_view' => 'Calendar View',
'set_as_default_view' => 'Set as default view',
'kanban_view' => 'Kanban View',
'set_default_view_alert' => 'Are You Want to Set as Default View?',
'default_view' => 'Default View',
'tasks_insights' => 'Tasks Insights',
'reports' => 'Reports',
'projects_report' => 'Projects Report',
'total_team_members' => 'Total Team Members',
'average_overdue_days_per_project' => 'Avg. Overdue Days/Project',
'overdue_projects_percentage' => 'Overdue Projects (%)',
'total_overdue_days' => 'Total Overdue Days',
'export' => 'Export',
'total_days' => 'Total Days',
'days_elapsed' => 'Days Elapsed',
'days_remaining' => 'Days Remaining',
'due_tasks' => 'Due Tasks',
'overdue_tasks' => 'Overdue Tasks',
'overdue_days' => 'Overdue Days',
'team_members' => 'Team Members',
'team' => 'Team',
'dates' => 'Dates',
'select_date_range' => 'Select Date Range',
'tasks_report' => 'Tasks Report',
'average_task_completion_time' => 'Avg. Task Completion Time',
'urgent_tasks' => 'Urgent Tasks',
'date_info' => 'Date Info',
'due_date' => 'Due Date',
'invoices_report' => 'Invoices Report',
'total_invoices' => 'Total Invoices',
'total_amount' => 'Total Amount',
'total_tax' => 'Total Tax',
'total_final' => 'Total Final',
'average_invoice_value' => 'Avg. Invoice Value',
'date_range' => 'Date Range',
'timestamps' => 'Timestamps',
'leaves_report' => 'Leaves Report',
'total_leaves' => 'Total Leaves',
'approved_leaves' => 'Approved Leaves',
'pending_leaves' => 'Pending Leaves',
'rejected_leaves' => 'Rejected Leaves',
'income_vs_expense_report' => 'Income vs Expense Report',
'total_income' => 'Total Income',
'total_expenses' => 'Total Expenses',
'profit_or_loss' => 'Profit or Loss',
'date' => 'Date',
'calendar' => 'Calendar',
'tasks_count' => 'Tasks Count',
'save_column_visibility' => 'Save Column Visibility',
'days_overdue' => 'Days Overdue',
'select_projects' => 'Select Projects',
'select_statuses' => 'Select Statuses',
'select_priorities' => 'Select Priorities',
'clear_filters' => 'Clear Filters',
'expense_type_id' => 'Expense type ID',
'priorities' => 'Priorities',
'primary_workspace' => 'Primary Workspace',
'dob' => 'Date of birth',
'doj' => 'Date of joining',
'no' => 'No',
'task_summary' => 'Task Summary',
'discussions' => 'Discussions',
'media' => 'Media',
'milestones' => 'Milestones',
'mind_map_view' => 'Mind Map View',
'mind_map' => 'Mind Map',
'export_mindmap' => 'Export Mind Map',
'gantt_chart_view' => 'Gantt Chart View',
'prev' => 'Previous',
'next' => 'Next',
'day' => 'Day',
'week' => 'Week',
'month' => 'Month',
'admin_settings' => 'Admin Settings',
'income_vs_expense' => 'Income vs Expense',
'clear_cache' => 'Clear Cache',
'confirm_update_dates' => 'Confirm Update Dates',
'manager_alert' => 'As a Manager, user can access and manage Plans, Subscriptions, Transactions, and Customers And Support',
'notifications_settings' => 'Notifications Settings',
'slack_webhook_url' => 'Slack webhook URL',
'slack' => 'Slack',
'managers' => 'Managers',
'create_manager' => 'Create Manager',
'register_manager' => 'Register Manager',
'support' => 'Support',
'create_new_ticket' => 'Create New Ticket',
'create_ticket' => 'Create Ticket',
'submit_ticket' => 'Submit Ticket',
'country_code_and_phone_number' => 'Country code and phone number',
'slack_bot_token' => 'Slack Bot Token',
'whatsapp_access_token' => 'WhatsApp access token',
'whatsapp_phone_number_id' => 'WhatsApp phone number ID',
'base_url' => 'Base URL',
'method' => 'Method',
'create_authorization_token' => 'Create authorization token',
'account_sid' => 'Account SID',
'auth_token' => 'Auth token',
'header' => 'Header',
'body' => 'Body',
'params' => 'Params',
'add_header_data' => 'Add header data',
'key' => 'Key',
'action' => 'Action',
'sms_gateway' => 'SMS Gateway',
'security_settings' => 'Security Settings',
'max_login_attempts' => 'Max Login Attempts',
'max_login_attempts_info' => 'Leave it blank if you do not want to lock the account',
'time_decay' => 'Time Decay',
'time_decay_info' => 'This will not apply if login attempts are not locked',
'bank_name' => 'Bank Name',
'bank_code' => 'Bank Code',
'account_name' => 'Account Name',
'account_number' => 'Account Number',
'swift_code' => 'Swift Code',
'extra_notes' => 'Extra Notes',
'projects_and_tasks_management' => 'Projects And Tasks Management',
'utilities' => 'Utilities',
'customize_menu_order' => 'Customize Menu Order',
'time_sheet_report' => 'Time Sheet Report',
'holiday_calendar' => 'Holiday calendar',
'calendars' => 'Calendars',
'event_type' => 'Event Type',
'google_calendar' => 'Google calendar',
"module" => "Module",
"field_label" =>  "Field Label",
"field_type" => "Field Type",
"required" => "Required",
"show_in_table"  => "Show In Table",
"text" => "Text",
"number" => "Number",
"textarea" => "Textarea",
"radio" => "Radio",
"checkbox"  => "Check Box",
"confirm_deletion"  => "Confirm Deletion",
"confirm_message"  => "Are you sure you want to delete this field?",
"no_custom_fields" => "No custom fields for this project",
"custom_fields"  => "Custom fields",
"is_required" => "Is Required",
"optional" => "Optional",
"add_field" => "Add Fields",
"documentation_for_integration_with_google_calendar" => "Documentation for integration with Google Calendar",
"click_for_help" => "Click Here for Help",
"api_key" => "API Key",
"please_enter_your_google_api_key" => "Please Enter Your Google API Key",
"google_calendar_id" => "Google Calendar ID",
"please_enter_your_google_calendar_id" => "Please Enter Your Google Calendar ID",
"custom_holiday_name" => "Custom Holiday Name",
"please_enter_custom_holiday_name" => "Please Enter Custom Holiday Name",
"google_calendar_integration" => "Google Calendar Integration",
"google_calendar_not_configured" => "Google Calendar is Not Configured Properly.",
"click_here_to_configure" => "Click Here to Configure",
"leave_accepted" => "Leave Accepted",
"leave_pending" => "Leave Pending",
"leave_rejected" => "Leave Rejected"
];
