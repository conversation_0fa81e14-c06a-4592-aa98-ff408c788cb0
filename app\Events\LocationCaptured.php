<?php

namespace App\Events;

use App\Models\TaskLocation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LocationCaptured implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $taskLocation;
    public $workspaceId;

    /**
     * Create a new event instance.
     */
    public function __construct(TaskLocation $taskLocation, $workspaceId)
    {
        $this->taskLocation = $taskLocation;
        $this->workspaceId = $workspaceId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('workspace.' . $this->workspaceId . '.locations'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'location.captured';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->taskLocation->id,
            'user_id' => $this->taskLocation->user_id,
            'user_name' => $this->taskLocation->user->first_name . ' ' . $this->taskLocation->user->last_name,
            'task_id' => $this->taskLocation->task_id,
            'task_title' => $this->taskLocation->task->title,
            'latitude' => (float) $this->taskLocation->latitude,
            'longitude' => (float) $this->taskLocation->longitude,
            'coordinates' => $this->taskLocation->coordinates,
            'action' => $this->taskLocation->action,
            'action_label' => $this->taskLocation->action_label,
            'timestamp' => $this->taskLocation->created_at->toISOString(),
            'date' => $this->taskLocation->created_at->format('M d, Y'),
            'time' => $this->taskLocation->created_at->format('h:i A'),
        ];
    }
}
