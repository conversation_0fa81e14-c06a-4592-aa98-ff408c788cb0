@extends('layout')

@section('title')
    {{ get_label('task_location_timeline', 'Task Location Timeline') }}
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="fw-bold py-3 mb-4">
                <i class="bx bx-map-alt me-2"></i>
                {{ get_label('task_location_timeline', 'Task Location Timeline') }}
            </h4>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('tasks.index') }}">{{ get_label('tasks', 'Tasks') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('tasks.info', $task->id) }}">{{ $task->title }}</a></li>
                    <li class="breadcrumb-item active">{{ get_label('location_timeline', 'Location Timeline') }}</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('tasks.info', $task->id) }}" class="btn btn-outline-secondary">
                <i class="bx bx-arrow-back me-1"></i>
                {{ get_label('back_to_task', 'Back to Task') }}
            </a>
            <button type="button" class="btn btn-primary" id="refreshTimelineBtn">
                <i class="bx bx-refresh me-1"></i>
                {{ get_label('refresh', 'Refresh') }}
            </button>
        </div>
    </div>

    <!-- Task Information -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bx bx-task me-2"></i>
                {{ get_label('task_information', 'Task Information') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>{{ get_label('title', 'Title') }}:</strong> {{ $task->title }}</p>
                    <p><strong>{{ get_label('assigned_to', 'Assigned To') }}:</strong> {{ $task->user->first_name }} {{ $task->user->last_name }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>{{ get_label('status', 'Status') }}:</strong> 
                        <span class="badge bg-{{ $task->status == 'completed' ? 'success' : ($task->status == 'in_progress' ? 'warning' : 'secondary') }}">
                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                        </span>
                    </p>
                    <p><strong>{{ get_label('description', 'Description') }}:</strong> {{ $task->description ?? 'N/A' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-primary">
                                <i class="bx bx-map-pin"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('total_locations', 'Total Locations') }}</span>
                            <h3 class="card-title mb-2" id="totalLocationsCount">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-success">
                                <i class="bx bx-log-in"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('check_ins', 'Check-ins') }}</span>
                            <h3 class="card-title mb-2" id="checkinCount">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-warning">
                                <i class="bx bx-log-out"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('check_outs', 'Check-outs') }}</span>
                            <h3 class="card-title mb-2" id="checkoutCount">0</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avatar flex-shrink-0 me-3">
                            <span class="avatar-initial rounded bg-label-info">
                                <i class="bx bx-time"></i>
                            </span>
                        </div>
                        <div>
                            <span class="fw-semibold d-block mb-1">{{ get_label('duration', 'Duration') }}</span>
                            <h6 class="card-title mb-2" id="timelineDuration">--</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Timeline Map -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bx bx-map me-2"></i>
                        {{ get_label('location_progression_map', 'Location Progression Map') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div id="timelineMap" style="height: 500px; width: 100%;"></div>
                </div>
            </div>
        </div>

        <!-- Timeline Activity -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bx bx-list-ul me-2"></i>
                        {{ get_label('activity_timeline', 'Activity Timeline') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div id="timelineActivity" style="max-height: 500px; overflow-y: auto;">
                        <!-- Dynamic timeline content -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let timelineMap;
let timelineData = [];
let markers = [];
let polyline = null;

$(document).ready(function() {
    // Initialize map
    initializeTimelineMap();
    
    // Load timeline data
    loadTimelineData();
    
    // Event handlers
    $('#refreshTimelineBtn').on('click', function() {
        loadTimelineData();
    });
});

function initializeTimelineMap() {
    timelineMap = L.map('timelineMap').setView([0, 0], 2);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(timelineMap);
}

function loadTimelineData() {
    $.ajax({
        url: '{{ route('tasks.location.timeline.data', $task->id) }}',
        method: 'GET',
        success: function(response) {
            if (response.error === false) {
                timelineData = response.data;
                updateTimelineMap(response.data);
                updateTimelineActivity(response.data);
                updateStatistics(response.stats);
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            toastr.error('Failed to load timeline data');
        }
    });
}

function updateTimelineMap(data) {
    // Clear existing markers and polyline
    markers.forEach(marker => timelineMap.removeLayer(marker));
    if (polyline) {
        timelineMap.removeLayer(polyline);
    }
    markers = [];
    
    if (data.length === 0) {
        return;
    }
    
    // Create markers for each location
    data.forEach((location, index) => {
        const color = getMarkerColor(location.action_raw);
        const marker = L.circleMarker([location.lat, location.lng], {
            radius: 8,
            fillColor: color,
            color: '#fff',
            weight: 2,
            opacity: 1,
            fillOpacity: 0.8
        });
        
        // Add number label to marker
        const numberIcon = L.divIcon({
            className: 'timeline-marker-number',
            html: `<div style="background: ${color}; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; border: 2px solid white;">${index + 1}</div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10]
        });
        
        const numberedMarker = L.marker([location.lat, location.lng], { icon: numberIcon });
        
        const popupContent = `
            <div class="p-2">
                <h6 class="mb-2">#${index + 1} - ${location.action}</h6>
                <p class="mb-1"><strong>User:</strong> ${location.user}</p>
                <p class="mb-1"><strong>Time:</strong> ${location.time}</p>
                <p class="mb-1"><strong>Date:</strong> ${location.date}</p>
                <p class="mb-0"><strong>Location:</strong> ${location.coordinates}</p>
            </div>
        `;
        
        numberedMarker.bindPopup(popupContent);
        numberedMarker.addTo(timelineMap);
        markers.push(numberedMarker);
    });
    
    // Create polyline to show progression
    if (data.length > 1) {
        const latlngs = data.map(location => [location.lat, location.lng]);
        polyline = L.polyline(latlngs, {
            color: '#007bff',
            weight: 3,
            opacity: 0.7,
            dashArray: '5, 10'
        }).addTo(timelineMap);
    }
    
    // Fit map to show all markers
    if (markers.length > 0) {
        const group = new L.featureGroup(markers);
        timelineMap.fitBounds(group.getBounds().pad(0.1));
    }
}

function updateTimelineActivity(data) {
    const container = $('#timelineActivity');
    container.empty();
    
    if (data.length === 0) {
        container.append(`
            <div class="text-center text-muted py-4">
                <i class="bx bx-info-circle me-1"></i>
                No location data found for this task
            </div>
        `);
        return;
    }
    
    data.forEach((location, index) => {
        const isLast = index === data.length - 1;
        const badgeColor = getActionBootstrapColor(location.action_raw);
        
        container.append(`
            <div class="timeline-item ${isLast ? 'timeline-end' : ''}">
                <div class="timeline-point">
                    <span class="badge bg-${badgeColor} rounded-pill">${index + 1}</span>
                </div>
                <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start mb-1">
                        <h6 class="mb-0">${location.action}</h6>
                        <small class="text-muted">${location.time}</small>
                    </div>
                    <p class="text-muted mb-1">${location.date}</p>
                    <p class="mb-1"><strong>User:</strong> ${location.user}</p>
                    <p class="mb-2"><strong>Location:</strong> ${location.coordinates}</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="focusOnLocation(${location.lat}, ${location.lng}, ${index})">
                        <i class="bx bx-map-pin"></i> View on Map
                    </button>
                </div>
            </div>
        `);
    });
}

function updateStatistics(stats) {
    $('#totalLocationsCount').text(stats.total_locations);
    $('#checkinCount').text(stats.checkin_count);
    $('#checkoutCount').text(stats.checkout_count);
    $('#timelineDuration').text(stats.duration || '--');
}

function getMarkerColor(action) {
    switch(action) {
        case 'checkin': return '#28a745';
        case 'checkout': return '#ffc107';
        case 'task_created': return '#17a2b8';
        default: return '#6c757d';
    }
}

function getActionBootstrapColor(action) {
    switch(action) {
        case 'checkin': return 'success';
        case 'checkout': return 'warning';
        case 'task_created': return 'info';
        default: return 'secondary';
    }
}

function focusOnLocation(lat, lng, index) {
    timelineMap.setView([lat, lng], 15);
    
    // Open the corresponding marker popup
    if (markers[index]) {
        markers[index].openPopup();
    }
}
</script>

<style>
.timeline-item {
    position: relative;
    padding-left: 40px;
    padding-bottom: 20px;
}

.timeline-item:not(.timeline-end)::before {
    content: '';
    position: absolute;
    left: 18px;
    top: 30px;
    bottom: -20px;
    width: 2px;
    background: #dee2e6;
}

.timeline-point {
    position: absolute;
    left: 0;
    top: 0;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 3px solid #007bff;
}
</style>
@endsection
