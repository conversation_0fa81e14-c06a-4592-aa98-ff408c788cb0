@include('Chatify::layouts.headLinks')
<div class="messenger">
    <input type="hidden" id="chat_type" value="{{$type??null}}">
    <input type="hidden" id="chat_type_id" value="{{$paramId??null}}">
    {{-- ----------------------Users/Groups lists side---------------------- --}}
    <div class="messenger-listView {{ !!$id ? 'conversation-active' : '' }}">
        {{-- Header and search bar --}}
        @php
        $messageLabel = get_label('messages', 'MESSAGES');
        if (isset($type)) {
        if ($type == 'project') {
        $messageLabel = get_label('project','Project').':'.$project->title;
        } elseif ($type == 'task') {
        $messageLabel = get_label('task','Task').':'.$task->title;
        }
        }
        @endphp
        <div class="m-header">
            <nav>
                <a href="#"><i class="fas fa-inbox"></i> <span class="messenger-headTitle">{{$messageLabel}}</span> </a>
                {{-- header buttons --}}
                <nav class="m-header-right">
                    <a href="#"><i class="fas fa-cog settings-btn"></i></a>
                    <a href="#" class="listView-x"><i class="fas fa-times"></i></a>
                </nav>
            </nav>
            {{-- Search input --}}
            <input type="text" class="messenger-search" placeholder="<?= get_label('search', 'Search') ?>" />
            {{-- Tabs --}}
            {{-- <div class="messenger-listView-tabs">
                <a href="#" class="active-tab" data-view="users">
                    <span class="far fa-user"></span> <?= get_label('contacts', 'Contacts') ?></a>
            </div> --}}
        </div>
        {{-- tabs and lists --}}
        <div class="m-body contacts-container">
            {{-- Lists [Users/Group] --}}
            {{-- ---------------- [ User Tab ] ---------------- --}}
            <div class="show messenger-tab users-tab app-scroll" data-view="users">
                {{-- Favorites --}}
                <div class="favorites-section">
                    <p class="messenger-title"><span><?= get_label('favorites', 'Favorites') ?></span></p>
                    <div class="messenger-favorites app-scroll-hidden"></div>
                </div>
                {{-- Saved Messages --}}
                <p class="messenger-title"><span></span></p>
                {!! view('Chatify::layouts.listItem', ['get' => 'saved']) !!}
                {{-- Contact --}}
                <p class="messenger-title"><span><?= get_label('all_messages', 'All Messages') ?></span></p>
                <div class="listOfContacts" style="width: 100%;height: calc(100% - 272px);position: relative;"></div>
            </div>
            {{-- ---------------- [ Search Tab ] ---------------- --}}
            <div class="messenger-tab search-tab app-scroll" data-view="search">
                {{-- items --}}
                <p class="messenger-title"><span><?= get_label('search', 'Search') ?></span></p>
                <div class="search-records">
                    <p class="message-hint center-el"><span><?= get_label('type_to_search', 'Type to search') ?>..</span></p>
                </div>
            </div>
        </div>
    </div>
    {{-- ----------------------Messaging side---------------------- --}}
    <div class="messenger-messagingView">
        {{-- header title [conversation name] amd buttons --}}
        <div class="m-header m-header-messaging">
            <nav class="chatify-d-flex chatify-justify-content-between chatify-align-items-center">
                {{-- header back button, avatar and user name --}}
                <div class="chatify-d-flex chatify-justify-content-between chatify-align-items-center">
                    <a href="#" class="show-listView"><i class="fas fa-arrow-left"></i></a>
                    <div class="avatar av-s header-avatar" style="margin: 0px 10px; margin-top: -5px; margin-bottom: -5px;">
                    </div>
                    <a href="#" class="user-name">{{ Auth::user()->first_name.' '.Auth::user()->last_name }}</a>
                </div>
                {{-- header buttons --}}
          <nav class="m-header-right">
    <a href="#" class="add-to-favorite"><i class="fas fa-star"></i></a>
    <a href="{{ route('home.index') }}" id="home-icon"><i class="fas fa-home"></i></a>
    <a href="#" class="show-infoSide"><i class="fas fa-info-circle"></i></a>
</nav>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (window.self !== window.top) {
            // The page is in an iframe
            document.getElementById('home-icon').style.display = 'none';
        }
    });
</script>
            </nav>
            {{-- Internet connection --}}
            <div class="internet-connection">
                <span class="ic-connected"><?= get_label('connected', 'Connected') ?></span>
                <span class="ic-connecting"><?= get_label('connecting', 'Connecting') ?>...</span>
                <span class="ic-noInternet"><?= get_label('no_internet_access', 'No internet access') ?></span>
            </div>
            <?php
            $pusher_settings = get_settings('pusher_settings');
            $is_settings = isset($pusher_settings['pusher_app_id']) &&
                isset($pusher_settings['pusher_app_key']) &&
                isset($pusher_settings['pusher_app_secret']) &&
                isset($pusher_settings['pusher_app_cluster']) &&
                !empty($pusher_settings['pusher_app_id']) &&
                !empty($pusher_settings['pusher_app_key']) &&
                !empty($pusher_settings['pusher_app_secret']) &&
                !empty($pusher_settings['pusher_app_cluster']) ? 1 : 0;
            ?>
            @role('admin')
            @if($is_settings == 0)
            <div class="settings-alert">
                Important settings for the chat feature are not set. <a href="/settings/pusher" target="_blank">Click here to configure them now.</a>
            </div>
            @endif
            @endrole
        </div>
        {{-- Messaging area --}}
        <div class="m-body messages-container app-scroll">
            <div class="messages">
                <p class="message-hint center-el"><span><?= get_label('please_select_a_chat_to_start_messaging', 'Please select a chat to start messaging') ?></span></p>
            </div>
            {{-- Typing indicator --}}
            <div class="typing-indicator">
                <div class="message-card typing">
                    <div class="message">
                        <span class="typing-dots">
                            <span class="dot dot-1"></span>
                            <span class="dot dot-2"></span>
                            <span class="dot dot-3"></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        {{-- Send Message Form --}}
        @include('Chatify::layouts.sendForm')
    </div>
    {{-- ---------------------- Info side ---------------------- --}}
    <div class="messenger-infoView app-scroll">
        {{-- nav actions --}}
        <nav>
            <p><?= get_label('user_details', 'User Details') ?></p>
            <a href="#"><i class="fas fa-times"></i></a>
        </nav>
        {!! view('Chatify::layouts.info')->render() !!}
    </div>
</div>
@include('Chatify::layouts.modals')
@include('Chatify::layouts.footerLinks')
