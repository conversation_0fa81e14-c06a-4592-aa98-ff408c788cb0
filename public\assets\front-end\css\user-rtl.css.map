{"version": 3, "sources": ["theme/_variables.scss"], "names": [], "mappings": "AAAA,oBAAA", "file": "user-rtl.css", "sourcesContent": ["/* prettier-ignore */\r\n\r\n@import 'colors';\r\n$variable-prefix: 'gohub-';\r\n// Grid containers\r\n//\r\n// Define the maximum width of `.container` for different screen sizes.\r\n// scss-docs-start container-max-widths\r\n$container-max-widths: () !default;\r\n$container-max-widths: map-merge(\r\n  (\r\n    sm: 576px,\r\n    md: 768px,\r\n    lg: 992px,\r\n    xl: 1100px,\r\n    xxl: 1200px,\r\n  ),\r\n  $container-max-widths\r\n);\r\n\r\n$grid-breakpoints: () !default;\r\n$grid-breakpoints: map-merge(\r\n  (\r\n    xs: 0,\r\n    sm: 540px,\r\n    md: 720px,\r\n    lg: 960px,\r\n    xl: 1140px,\r\n    xxl: 1440px,\r\n  ),\r\n  $grid-breakpoints\r\n);\r\n\r\n//*-----------------------------------------------\r\n//|   Gutter\r\n//-----------------------------------------------*/\r\n$grid-gutter-width: 2rem !default;\r\n\r\n\r\n\r\n// Customize the light and dark text colors for use in our color contrast function.\r\n$color-contrast-dark: $gray-800 !default;\r\n\r\n// Min contrast ratio\r\n$min-contrast-ratio: 2 !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Brand colors\r\n//-----------------------------------------------*/\r\n$linkedin: #0077b5 !default;\r\n$facebook: #3c5a99 !default;\r\n$twitter: #1da1f2 !default;\r\n$google-plus: #dd4b39 !default;\r\n$github: #333333 !default;\r\n$youtube: #ff0001 !default;\r\n\r\n$brand-colors: () !default;\r\n$brand-colors: map-merge(\r\n  (\r\n    'facebook': $facebook,\r\n    'google-plus': $google-plus,\r\n    'twitter': $twitter,\r\n    'linkedin': $linkedin,\r\n    'youtube': $youtube,\r\n    'github': $github,\r\n  ),\r\n  $brand-colors\r\n);\r\n\r\n//*-----------------------------------------------\r\n//|   Border\r\n//-----------------------------------------------*/\r\n$border-color: var(--#{$variable-prefix}border-color);\r\n$border-width: 1px !default;\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n\r\n$enable-shadows: true !default;\r\n$enable-gradients: false !default;\r\n$enable-negative-margins: true !default;\r\n\r\n\r\n// $component-active-color: var(--#{$variable-prefix}white) !default;\r\n// $component-active-bg: var(--#{$variable-prefix}primary) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Shadow\r\n//-----------------------------------------------*/\r\n\r\n$box-shadow: var(--#{$variable-prefix}box-shadow) !default;\r\n$box-shadow-sm: var(--#{$variable-prefix}box-shadow-sm) !default;\r\n$box-shadow-lg: var(--#{$variable-prefix}box-shadow-lg) !default;\r\n$box-shadow-inset: var(--#{$variable-prefix}box-shadow-inset) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Spacing\r\n//----------------------------------------------\r\n\r\n$spacer: 1rem !default;\r\n$spacers: () !default;\r\n\r\n$spacers: map-merge(\r\n  (\r\n    0: 0,\r\n    1: $spacer * 0.25,\r\n    2: $spacer * 0.5,\r\n    3: $spacer,\r\n    4: $spacer * 1.8,\r\n    5: $spacer * 3,\r\n    6: $spacer * 4,\r\n    7: $spacer * 5,\r\n    8: $spacer * 7.5,\r\n    9: $spacer * 10,\r\n    10: $spacer * 12.5,\r\n    11: $spacer * 15\r\n  ),\r\n  $spacers\r\n);\r\n\r\n//*-----------------------------------------------\r\n//|   Body\r\n//-----------------------------------------------*/\r\n\r\n$body-bg: $white !default; \r\n$body-color: $gray-700 !default;\r\n$body-bg: $white;\r\n\r\n//*-----------------------------------------------\r\n//|   Link\r\n//-----------------------------------------------*/\r\n$link-decoration: none !default;\r\n$link-hover-decoration: underline !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Components\r\n//-----------------------------------------------*/\r\n\r\n$border-radius:               .5rem !default;\r\n$border-radius-sm:            .3rem !default;\r\n$border-radius-lg:            .7rem !default;\r\n$border-radius-xl:            1rem !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Fonts Families\r\n//-----------------------------------------------*/\r\n$font-family-sans-serif: 'Poppins', -apple-system, BlinkMacSystemFont,\r\n  'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji',\r\n  'Segoe UI Emoji', 'Segoe UI Symbol' !default;\r\n $font-family-cursive: 'Baloo Bhaijaan 2', Menlo, Monaco, Consolas,\r\n   'Liberation Mono', 'Courier New', monospace !default;\r\n$font-family-serif: 'PT Serif', Times, 'Times New Roman', Georgia, serif !default;\r\n$font-family-base: $font-family-sans-serif !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Fonts\r\n//-----------------------------------------------*/\r\n$type-scale: 1.333;\r\n$font-size-base: 1rem !default;\r\n$font-sizes: () !default;\r\n\r\n$font-sizes: () !default;\r\n$font-sizes: (\r\n  '-1': 0.75rem,\r\n  0: 1rem,\r\n  1: 1.333rem,\r\n  2: 1.777rem,\r\n  3: 2.369rem,\r\n  4: 3.157rem,\r\n  5: 4.199rem,\r\n  6: 5.584rem,\r\n  7: 7.427rem,\r\n  8: 9.878rem,\r\n);\r\n\r\n$font-size-lg: map_get($font-sizes, 1) !default;\r\n$font-size-sm:  map_get($font-sizes, '-1') !default;\r\n\r\n$font-weight-thin: 100 !default;\r\n$font-weight-lighter: 200 !default;\r\n$font-weight-light: 300 !default;\r\n$font-weight-normal: 400 !default;\r\n$font-weight-medium: 500 !default;\r\n$font-weight-semi-bold: 600 !default;\r\n$font-weight-bold: 700 !default;\r\n$font-weight-bolder: 800 !default;\r\n$font-weight-black: 900 !default;\r\n\r\n$line-height-base: 1.45 !default;\r\n// $input-btn-line-height: 1.5 !default;\r\n\r\n$h1-font-size: map_get($font-sizes, 4) !default;\r\n$h2-font-size: map_get($font-sizes, 3) !default;\r\n$h3-font-size: map_get($font-sizes, 2) !default;\r\n$h4-font-size: map_get($font-sizes, 1) !default;\r\n$h5-font-size: map_get($font-sizes, 0) !default;\r\n$h6-font-size: map_get($font-sizes, '-1') !default;\r\n\r\n$headings-font-family: $font-family-cursive !default;\r\n$headings-font-weight: $font-weight-bold !default;\r\n$headings-color: var(--#{$variable-prefix}headings-color) !default;\r\n\r\n// scss-docs-start display-headings\r\n$type-scale: 1.250 !default;\r\n$font-size-base: 1rem !default;\r\n$font-sizes: () !default;\r\n$font-sizes: map-merge(\r\n  (\r\n    '-2': (1 / pow($type-scale, 2)) * $font-size-base,//11.11\r\n    '-1':(1 / $type-scale) * $font-size-base,//13.33\r\n    0: $font-size-base,//16\r\n    1: pow($type-scale, 1) * $font-size-base,\r\n    2: pow($type-scale, 2) * $font-size-base,\r\n    3: pow($type-scale, 3) * $font-size-base,\r\n    4: pow($type-scale, 4) * $font-size-base,\r\n    5: pow($type-scale, 5) * $font-size-base,\r\n    6: pow($type-scale, 6) * $font-size-base,\r\n    7: pow($type-scale, 7) * $font-size-base,\r\n    8: pow($type-scale, 8) * $font-size-base,\r\n  ),\r\n  $font-sizes\r\n);\r\n\r\n\r\n\r\n$display-font-weight: $font-weight-black !default;\r\n$display-line-height: 1 !default;\r\n// scss-docs-end display-headings\r\n\r\n$lead-font-size: $font-size-lg !default;\r\n$lead-font-weight: $font-weight-normal !default;\r\n\r\n$small-font-size: 75% !default;\r\n\r\n$text-muted: $gray-500 !default; //#949494v\r\n\r\n$blockquote-font-size: $font-size-lg !default;\r\n\r\n\r\n\r\n// //*-----------------------------------------------\r\n//|   Tables\r\n//-----------------------------------------------*/\r\n$table-border-color: var(--#{$variable-prefix}table-border-color) !default;\r\n$table-head-bg: $gray-200 !default;\r\n$table-head-color: $dark !default;\r\n$table-dark-bg: $gray-1000 !default;\r\n$table-dark-border-color: lighten($gray-1000, 7.5%) !default;\r\n$table-striped-order: even !default;\r\n// $table-accent-bg: $gray-100 !default;\r\n$table-cell-padding-y: 0.75rem !default;\r\n$table-cell-padding-x: 0.75rem !default;\r\n$table-group-separator-color: inherit !default;\r\n\r\n\r\n\r\n\r\n\r\n//*-----------------------------------------------\r\n//|   Buttons\r\n//-----------------------------------------------*/\r\n$btn-sparrow-box-shadow: var(--#{$variable-prefix}btn-sparrow-box-shadow) !default;\r\n$btn-sparrow-hover-box-shadow: var(--#{$variable-prefix}btn-sparrow-hover-box-shadow) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Buttons and Forms\r\n//-----------------------------------------------*/\r\n$input-btn-padding-y: 0.8rem !default;\r\n$input-btn-padding-x: 2.5rem !default;\r\n\r\n$input-btn-padding-y-sm: 0.4rem !default;\r\n$input-btn-padding-x-sm: 1.2rem !default;\r\n\r\n$input-btn-padding-y-lg: 1.2rem !default;\r\n$input-btn-padding-x-lg: 5rem !default;\r\n\r\n$btn-reveal-hover-shadow: 0 0 0 1px rgba(43, 45, 80, 0.1),\r\n  0 2px 5px 0 rgba(43, 45, 80, 0.08), 0 1px 1.5px 0 rgba($black, 0.07),\r\n  0 1px 2px 0 rgba($black, 0.08) !default;\r\n$btn-font-weight: $font-weight-bold !default;\r\n$btn-focus-width: 0 !default;\r\n\r\n$btn-border-radius-lg: $border-radius;\r\n//$btn-line-height: 1.5 !default; //- need to review4\r\n//btn-close\r\n// $btn-close-color:            $white !default;\r\n$btn-close-width:            1.2em !default;\r\n\r\n\r\n//*-----------------------------------------------\r\n//|   Forms\r\n//-----------------------------------------------*/\r\n$input-disabled-bg: var(--#{$variable-prefix}200) !default;\r\n$input-group-addon-bg: var(--#{$variable-prefix}200) !default;\r\n$input-border-color: var(--#{$variable-prefix}input-border-color) !default;\r\n$input-placeholder-color: var(--#{$variable-prefix}600) !default;\r\n$input-color: var(--#{$variable-prefix}input-color) !default;\r\n$form-check-margin-bottom: 0.34375rem !default;\r\n$form-label-font-size: $font-size-sm !default;\r\n$form-check-input-bg: transparent !default;\r\n$form-check-input-border: 1px solid var(--#{$variable-prefix}form-check-input-border-color) !default;\r\n$form-switch-color: $gray-500 !default;\r\n$form-select-disabled-bg: var(--#{$variable-prefix}200) !default;\r\n$input-bg: var(--#{$variable-prefix}input-bg) !default;\r\n// $input-focus-border-color: var(--#{$variable-prefix}input-focus-border-color) !default;\r\n$form-file-button-bg: $gray-900 !default;\r\n$form-file-button-color: $gray-300 !default;\r\n\r\n$input-padding-x: $input-btn-padding-y !default;\r\n$input-padding-x-sm: $input-btn-padding-y-sm !default;\r\n$input-padding-x-lg: $input-btn-padding-y-lg !default;\r\n$input-box-shadow: 'null' !default;\r\n\r\n\r\n\r\n//*-----------------------------------------------\r\n//|   Z-index\r\n//-----------------------------------------------*/\r\n\r\n$zindex-sticky: 1015 !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Navigation\r\n//-----------------------------------------------*/\r\n$navbar-dark-color: var(--#{$variable-prefix}navbar-dark-color) !default;\r\n$navbar-dark-hover-color: var(--#{$variable-prefix}navbar-dark-hover-color) !default;\r\n$navbar-dark-active-color: var(--#{$variable-prefix}navbar-dark-active-color) !default;\r\n$navbar-dark-disabled-color: var(--#{$variable-prefix}navbar-dark-disabled-color) !default;\r\n$navbar-dark-toggler-border-color: var(--#{$variable-prefix}navbar-dark-toggler-border-color) !default;\r\n$navbar-light-color: var(--#{$variable-prefix}navbar-light-color) !default;\r\n$navbar-light-hover-color: var(--#{$variable-prefix}navbar-light-hover-color) !default;\r\n$navbar-light-active-color: var(--#{$variable-prefix}navbar-light-active-color) !default;\r\n$navbar-light-disabled-color: var(--#{$variable-prefix}navbar-light-disabled-color) !default;\r\n$navbar-light-toggler-border-color: var(--#{$variable-prefix}navbar-light-toggler-border-color) !default;\r\n\r\n$navbar-font-size: $font-size-base * 0.8 !default;\r\n$navbar-padding-y: map_get($spacers, 2) !default;\r\n$navbar-padding-x: $spacer !default;\r\n\r\n\r\n// $navbar-dark-color: rgba($white, .7) !default;\r\n$top-nav-height: 4.3125rem !default;\r\n$standard-nav-height: 3.5625rem !default;\r\n$navbar-light-toggler-icon-bg: str-replace(\r\n  url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#9da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E\"),\r\n  '#',\r\n  '%23'\r\n) !default;\r\n\r\n$navbar-dark-toggler-icon-bg: str-replace(\r\n  url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#9da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E\"),\r\n  '#',\r\n  '%23'\r\n) !default;\r\n\r\n// Navbar Glass\r\n\r\n$bg-navbar-glass: var(--#{$variable-prefix}bg-navbar-glass) !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Navbar Brand\r\n//-----------------------------------------------*/\r\n$navbar-brand-font-size: map_get($font-sizes, 1) !default;\r\n$navbar-brand-font-weight: $font-weight-bold !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Dropdowns | Dropdown menu container and contents.\r\n//-----------------------------------------------*/\r\n$dropdown-bg: var(--#{$variable-prefix}dropdown-bg) !default;\r\n$dropdown-color: var(--#{$variable-prefix}dropdown-color) !default;\r\n$dropdown-link-color: var(--#{$variable-prefix}dropdown-link-color) !default;\r\n$dropdown-link-hover-color: var(--#{$variable-prefix}dropdown-link-hover-color) !default;\r\n$dropdown-link-hover-bg: var(--#{$variable-prefix}dropdown-link-hover-bg) !default;\r\n$dropdown-link-disabled-color: var(--#{$variable-prefix}dropdown-link-disabled-color) !default;\r\n$dropdown-divider-bg: var(--#{$variable-prefix}dropdown-border-color) !default;\r\n$dropdown-border-color: var(--#{$variable-prefix}dropdown-border-color) !default;\r\n$dropdown-link-active-color: var(--#{$variable-prefix}dropdown-link-active-color) !default;\r\n$dropdown-link-active-bg: var(--#{$variable-prefix}dropdown-link-active-bg) !default;\r\n$dropdown-box-shadow: var(--#{$variable-prefix}dropdown-box-shadow) !default;\r\n\r\n$dropdown-font-size: $navbar-font-size !default;\r\n$dropdown-item-padding-y: map_get($spacers, 1) !default;\r\n$dropdown-item-padding-x: map_get($spacers, 3) !default;\r\n$dropdown-padding-y: map_get($spacers, 3) !default;\r\n\r\n\r\n//*-----------------------------------------------\r\n//|   Pagination\r\n//-----------------------------------------------*/\r\n$pagination-padding-y: 0.5rem !default;\r\n$pagination-padding-x: 0.75rem !default;\r\n$pagination-padding-y-sm: 0.25rem !default;\r\n$pagination-padding-x-sm: 0.5rem !default;\r\n$pagination-padding-y-lg: 0.75rem !default;\r\n$pagination-padding-x-lg: 1.5rem !default;\r\n\r\n$pagination-color: var(--#{$variable-prefix}pagination-color) !default;\r\n$pagination-bg: var(--#{$variable-prefix}pagination-bg) !default;\r\n$pagination-border-color: var(--#{$variable-prefix}pagination-border-color) !default;\r\n$pagination-focus-color: var(--#{$variable-prefix}pagination-focus-color) !default;\r\n$pagination-focus-bg: var(--#{$variable-prefix}pagination-focus-bg) !default;\r\n$pagination-focus-box-shadow: var(--#{$variable-prefix}pagination-focus-box-shadow) !default;\r\n$pagination-hover-color: var(--#{$variable-prefix}pagination-hover-color) !default;\r\n$pagination-hover-bg: var(--#{$variable-prefix}pagination-hover-bg) !default;\r\n$pagination-hover-border-color: var(--#{$variable-prefix}pagination-hover-border-color) !default;\r\n$pagination-active-color: var(--#{$variable-prefix}pagination-active-color) !default;\r\n$pagination-active-bg: var(--#{$variable-prefix}pagination-active-bg) !default;\r\n$pagination-active-border-color: var(--#{$variable-prefix}pagination-active-border-color) !default;\r\n$pagination-disabled-color: var(--#{$variable-prefix}pagination-disabled-color) !default;\r\n$pagination-disabled-bg: var(--#{$variable-prefix}pagination-disabled-bg) !default;\r\n$pagination-disabled-border-color: var(--#{$variable-prefix}pagination-disabled-border-color) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Cards\r\n//-----------------------------------------------*/\r\n\r\n$card-spacer-y: map_get($spacers, 5) !default;\r\n$card-spacer-x: map_get($spacers, 5) !default;\r\n$card-title-spacer-y: map_get($spacers, 3) !default;\r\n$card-border-width: 1px !default;\r\n$card-border-radius: $border-radius !default;\r\n$card-border-color: var(--#{$variable-prefix}card-border-color) !default;\r\n$card-cap-bg: var(--#{$variable-prefix}card-cap-bg) !default;\r\n$card-cap-padding-y: $spacer !default;\r\n$card-cap-padding-x: 3rem !default;\r\n$card-bg: var(--#{$variable-prefix}card-bg);\r\n\r\n// /*-----------------------------------------------\r\n//|   Tooltip\r\n//-----------------------------------------------*/\r\n\r\n$tooltip-padding-y: .5rem !default;\r\n$tooltip-font-size: map_get($font-sizes, '-1') !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Badge\r\n//-----------------------------------------------*/\r\n$badge-font-weight: $font-weight-black !default;\r\n$badge-padding-y: 0.25em !default;\r\n$badge-padding-x: 0.6em !default;\r\n//$badge-border-radius: 10rem; // need to review\r\n$border-radius-pill: 10rem;\r\n\r\n//*-----------------------------------------------\r\n//|   Badge pill\r\n//-----------------------------------------------*/\r\n// $badge-pill-padding-y: 0.6rem !default;\r\n// $badge-pill-padding-x: 0.6rem !default;\r\n//$badge-pill-border-radius: 10rem !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Tabs\r\n//-----------------------------------------------*/\r\n//$nav-tabs-border-color: var(--#{$variable-prefix}100) !default;\r\n\r\n\r\n// /*-----------------------------------------------\r\n//|   Modal\r\n//-----------------------------------------------*/\r\n$modal-content-border-radius: $border-radius-lg !default;\r\n$modal-content-bg: var(--#{$variable-prefix}modal-content-bg) !default;\r\n\r\n$modal-backdrop-bg: rgb(28, 28, 28) !default;\r\n$modal-backdrop-opacity: .9 !default;\r\n$modal-fade-in-transform: scale(1) !default;\r\n$modal-xs: 12.5rem !default;\r\n\r\n\r\n///*-----------------------------------------------\r\n//|   List Group\r\n//-----------------------------------------------*/\r\n$list-group-bg: var(--#{$variable-prefix}list-group-bg) !default;\r\n$list-group-border-color: var(--#{$variable-prefix}border-color) !default;\r\n$list-group-action-hover-color: var(--#{$variable-prefix}list-group-action-hover-color) !default;\r\n$list-group-hover-bg: var(--#{$variable-prefix}list-group-hover-bg) !default;\r\n$list-group-action-active-bg: var(--#{$variable-prefix}200);\r\n\r\n//*-----------------------------------------------\r\n//|   Thumbnail\r\n//-----------------------------------------------*/\r\n$thumbnail-bg: var(--#{$variable-prefix}thumbnail-bg) !default;\r\n$thumbnail-border-width: 3px !default;\r\n$thumbnail-border-color: var(--#{$variable-prefix}thumbnail-bg) !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Breadcrumbs\r\n//-----------------------------------------------*/\r\n$breadcrumb-padding-x: 0 !default;\r\n$breadcrumb-margin-bottom: 0 !default;\r\n$breadcrumb-bg: 'transparent';\r\n$breadcrumb-divider: quote('/') !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Carousel\r\n//-----------------------------------------------*/\r\n$carousel-transition-duration: 0.8s !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Spinner\r\n//-----------------------------------------------*/\r\n$spinner-width-sm: 1.35rem !default;\r\n$spinner-height-sm: $spinner-width-sm !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Spinner\r\n//-----------------------------------------------*/\r\n$hr-color: $border-color !default;\r\n$hr-opacity: 1 !default;\r\n\r\n//*=============================================\r\n//|   Falcon Specific\r\n//=============================================\r\n\r\n//*-----------------------------------------------\r\n//|   Viewport Heights & Widths\r\n//----------------------------------------------\r\n\r\n$viewport-heights: () !default;\r\n$viewport-heights: map-merge(\r\n  (\r\n    25: 25vh,\r\n    50: 50vh,\r\n    75: 75vh,\r\n    100: 100vh,\r\n  ),\r\n  $viewport-heights\r\n);\r\n\r\n$viewport-widths: () !default;\r\n$viewport-widths: map-merge(\r\n  (\r\n    25: 25vw,\r\n    50: 50vw,\r\n    75: 75vw,\r\n    100: 100vw,\r\n  ),\r\n  $viewport-widths\r\n);\r\n\r\n$sizes: () !default;\r\n$sizes: map-merge(\r\n  (\r\n    25: 25%,\r\n    50: 50%,\r\n    75: 75%,\r\n    100: 100%,\r\n    auto: auto,\r\n  ),\r\n  $sizes\r\n);\r\n\r\n\r\n\r\n//*-----------------------------------------------\r\n//|   nav\r\n//-----------------------------------------------*/\r\n// $fancynav-width: 4.375rem !default;\r\n// $fancynav-breakpoint-up: lg !default;\r\n// $fancynav-breakpoint-down: md !default;\r\n// $fancynavHeight: 3rem !default;\r\n// $fancynav-collapse-bg: $black !default;\r\n// $fancynav-link-color: $white !default;\r\n\r\n$nav-link-font-size:               $font-size-base !default;\r\n\r\n//*-----------------------------------------------\r\n//|   offcanvas\r\n//-----------------------------------------------*/\r\n$offcanvas-transition-duration:  .7s;\r\n\r\n//*-----------------------------------------------\r\n//|   Accordion\r\n//-----------------------------------------------*/\r\n\r\n$accordion-padding-y:                     1.5rem !default;\r\n$accordion-padding-x:                     2.6rem !default;\r\n$accordion-bg:                            $white !default;\r\n$accordion-border-width:                  $border-width !default;\r\n$accordion-border-color:                  rgba($white, .125) !default;\r\n$accordion-border-radius:                 0 !default;\r\n\r\n$accordion-button-active-bg:              $white !default;\r\n$accordion-button-active-color:           $info !important;\r\n\r\n\r\n$accordion-button-focus-border-color:     $white !default;\r\n$accordion-button-focus-box-shadow:       $light !default;\r\n\r\n$accordion-icon-width:                    1rem !default;\r\n$accordion-icon-active-color:             $info !default;\r\n\r\n$accordion-button-icon:     url(\"data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='chevron-down' class='svg-inline--fa fa-chevron-down fa-w-14' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='#{$gray-1000}' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'%3E%3C/path%3E%3C/svg%3E\") !default;\r\n$accordion-button-active-icon:url(\"data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='chevron-down' class='svg-inline--fa fa-chevron-down fa-w-14' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='#{$info}' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'%3E%3C/path%3E%3C/svg%3E\") !default;\r\n"]}