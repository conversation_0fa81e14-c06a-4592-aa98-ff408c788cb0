@extends('layout')

@section('title')
    <?= get_label('create_todo', 'Create todo') ?>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between mt-4">
            <div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-style1">
                        <li class="breadcrumb-item">
                            <a href="{{ route('home.index') }}"><?= get_label('home', 'Home') ?></a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('todos.index') }}"><?= get_label('todos', 'Todos') ?></a>
                        </li>
                        <li class="breadcrumb-item active">
                            <?= get_label('create', 'Create') ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>


        <div class="card">
            <div class="card-body">
                <form action="{{ route('todos.store') }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="mb-3 col-md-6">
                            <label for="title" class="form-label"><?= get_label('title', 'Title') ?> <span
                                    class="asterisk">*</span></label>
                            <input class="form-control" type="text" id="title" name="title"
                                placeholder="Enter Title" value="{{ old('title') }}">

                            @error('title')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>



                        <div class="mb-3 col-md-6">
                            <label class="form-label" for="priority"><?= get_label('priority', 'Priority') ?> <span
                                    class="asterisk">*</span></label>
                            <div class="input-group">
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>
                                        <?= get_label('low', 'Low') ?></option>
                                    <option value="medium" {{ old('priority') == 'medium' ? 'selected' : '' }}>
                                        <?= get_label('medium', 'Medium') ?></option>
                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>
                                        <?= get_label('high', 'High') ?></option>
                                </select>
                            </div>
                            @error('priority')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label"><?= get_label('description', 'Description') ?> <span
                                    class="asterisk">*</span></label>
                            <textarea class="form-control" id="description" name="description" placeholder="Enter Description">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="text-danger text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>



                    </div>

                    <div class="mt-2">
                        <button type="submit" class="btn btn-primary me-2"
                            id="showToastPlacement"><?= get_label('create', 'Create') ?></button>
                        <button type="reset"
                            class="btn btn-outline-secondary"><?= get_label('cancel', 'Cancel') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
