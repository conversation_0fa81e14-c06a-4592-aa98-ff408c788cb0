<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class LocationAnalytics extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'workspace_id',
        'analytics_date',
        'total_locations',
        'total_distance',
        'total_duration',
        'checkin_count',
        'checkout_count',
        'auto_track_count',
        'avg_accuracy',
        'max_speed',
        'avg_speed',
        'first_location_time',
        'last_location_time',
        'geofence_entries',
        'geofence_exits',
        'time_in_zones',
        'tasks_worked_on',
        'unique_locations',
        'location_efficiency_score',
        'heat_map_data',
        'frequent_locations'
    ];

    protected $casts = [
        'analytics_date' => 'date',
        'total_distance' => 'decimal:2',
        'avg_accuracy' => 'decimal:2',
        'max_speed' => 'decimal:2',
        'avg_speed' => 'decimal:2',
        'first_location_time' => 'datetime:H:i',
        'last_location_time' => 'datetime:H:i',
        'location_efficiency_score' => 'decimal:2',
        'heat_map_data' => 'array',
        'frequent_locations' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the analytics
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the workspace
     */
    public function workspace()
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('analytics_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by workspace
     */
    public function scopeByWorkspace($query, $workspaceId)
    {
        return $query->where('workspace_id', $workspaceId);
    }

    /**
     * Generate analytics for a specific user and date
     */
    public static function generateForUserAndDate($userId, $date, $workspaceId)
    {
        $startOfDay = Carbon::parse($date)->startOfDay();
        $endOfDay = Carbon::parse($date)->endOfDay();

        // Get all locations for the day
        $locations = TaskLocation::where('user_id', $userId)
            ->whereBetween('created_at', [$startOfDay, $endOfDay])
            ->orderBy('created_at')
            ->get();

        if ($locations->isEmpty()) {
            return null;
        }

        // Calculate analytics
        $analytics = [
            'user_id' => $userId,
            'workspace_id' => $workspaceId,
            'analytics_date' => $date,
            'total_locations' => $locations->count(),
            'total_distance' => self::calculateTotalDistance($locations),
            'total_duration' => self::calculateTotalDuration($locations),
            'checkin_count' => $locations->where('action', 'checkin')->count(),
            'checkout_count' => $locations->where('action', 'checkout')->count(),
            'auto_track_count' => $locations->where('action', 'auto_track')->count(),
            'avg_accuracy' => $locations->whereNotNull('accuracy')->avg('accuracy'),
            'max_speed' => $locations->whereNotNull('speed')->max('speed'),
            'avg_speed' => $locations->whereNotNull('speed')->avg('speed'),
            'first_location_time' => $locations->first()->created_at->format('H:i'),
            'last_location_time' => $locations->last()->created_at->format('H:i'),
            'geofence_entries' => $locations->where('geofence_event', 'entry')->count(),
            'geofence_exits' => $locations->where('geofence_event', 'exit')->count(),
            'time_in_zones' => self::calculateTimeInZones($locations),
            'tasks_worked_on' => $locations->pluck('task_id')->unique()->count(),
            'unique_locations' => self::calculateUniqueLocations($locations),
            'location_efficiency_score' => self::calculateEfficiencyScore($locations),
            'heat_map_data' => self::generateHeatMapData($locations),
            'frequent_locations' => self::getFrequentLocations($locations),
        ];

        return self::updateOrCreate(
            ['user_id' => $userId, 'analytics_date' => $date],
            $analytics
        );
    }

    /**
     * Calculate total distance traveled
     */
    private static function calculateTotalDistance($locations)
    {
        $totalDistance = 0;
        $previousLocation = null;

        foreach ($locations as $location) {
            if ($previousLocation) {
                $distance = $location->distanceFrom(
                    $previousLocation->latitude,
                    $previousLocation->longitude
                );
                $totalDistance += $distance;
            }
            $previousLocation = $location;
        }

        return round($totalDistance, 2);
    }

    /**
     * Calculate total tracking duration
     */
    private static function calculateTotalDuration($locations)
    {
        if ($locations->count() < 2) return 0;

        $firstLocation = $locations->first();
        $lastLocation = $locations->last();

        return $lastLocation->created_at->diffInSeconds($firstLocation->created_at);
    }

    /**
     * Calculate time spent in geofence zones
     */
    private static function calculateTimeInZones($locations)
    {
        $timeInZones = 0;
        $entryTime = null;

        foreach ($locations as $location) {
            if ($location->geofence_event === 'entry') {
                $entryTime = $location->created_at;
            } elseif ($location->geofence_event === 'exit' && $entryTime) {
                $timeInZones += $location->created_at->diffInSeconds($entryTime);
                $entryTime = null;
            }
        }

        return $timeInZones;
    }

    /**
     * Calculate number of unique locations (clustered)
     */
    private static function calculateUniqueLocations($locations)
    {
        $clusters = [];
        $clusterRadius = 50; // 50 meters

        foreach ($locations as $location) {
            $foundCluster = false;

            foreach ($clusters as &$cluster) {
                $distance = $location->distanceFrom($cluster['lat'], $cluster['lng']);
                if ($distance <= $clusterRadius) {
                    $cluster['count']++;
                    $foundCluster = true;
                    break;
                }
            }

            if (!$foundCluster) {
                $clusters[] = [
                    'lat' => $location->latitude,
                    'lng' => $location->longitude,
                    'count' => 1
                ];
            }
        }

        return count($clusters);
    }

    /**
     * Calculate location efficiency score
     */
    private static function calculateEfficiencyScore($locations)
    {
        if ($locations->count() < 2) return 100;

        $totalDistance = self::calculateTotalDistance($locations);
        $uniqueLocations = self::calculateUniqueLocations($locations);
        $totalTime = self::calculateTotalDuration($locations);

        // Simple efficiency calculation (can be enhanced)
        $distanceEfficiency = min(100, (1000 / max(1, $totalDistance)) * 100);
        $timeEfficiency = min(100, ($totalTime / 3600) * 10); // Hours worked
        $locationEfficiency = min(100, ($uniqueLocations / max(1, $locations->count())) * 100);

        return round(($distanceEfficiency + $timeEfficiency + $locationEfficiency) / 3, 2);
    }

    /**
     * Generate heat map data
     */
    private static function generateHeatMapData($locations)
    {
        $gridSize = 0.001; // Approximately 100m grid
        $heatMap = [];

        foreach ($locations as $location) {
            $gridLat = round($location->latitude / $gridSize) * $gridSize;
            $gridLng = round($location->longitude / $gridSize) * $gridSize;
            $key = $gridLat . ',' . $gridLng;

            if (!isset($heatMap[$key])) {
                $heatMap[$key] = [
                    'lat' => $gridLat,
                    'lng' => $gridLng,
                    'intensity' => 0
                ];
            }

            $heatMap[$key]['intensity']++;
        }

        return array_values($heatMap);
    }

    /**
     * Get frequent locations
     */
    private static function getFrequentLocations($locations)
    {
        $locationCounts = [];
        $clusterRadius = 100; // 100 meters

        foreach ($locations as $location) {
            $found = false;

            foreach ($locationCounts as &$cluster) {
                $distance = $location->distanceFrom($cluster['lat'], $cluster['lng']);
                if ($distance <= $clusterRadius) {
                    $cluster['count']++;
                    $cluster['addresses'][] = $location->formatted_address;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $locationCounts[] = [
                    'lat' => $location->latitude,
                    'lng' => $location->longitude,
                    'count' => 1,
                    'addresses' => [$location->formatted_address]
                ];
            }
        }

        // Sort by frequency and return top 5
        usort($locationCounts, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        return array_slice($locationCounts, 0, 5);
    }

    /**
     * Get formatted total distance
     */
    public function getFormattedDistanceAttribute()
    {
        if ($this->total_distance >= 1000) {
            return round($this->total_distance / 1000, 2) . ' km';
        }
        return round($this->total_distance, 0) . ' m';
    }

    /**
     * Get formatted total duration
     */
    public function getFormattedDurationAttribute()
    {
        $hours = floor($this->total_duration / 3600);
        $minutes = floor(($this->total_duration % 3600) / 60);
        
        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }
        return $minutes . 'm';
    }

    /**
     * Get efficiency status
     */
    public function getEfficiencyStatusAttribute()
    {
        if (!$this->location_efficiency_score) return 'unknown';
        
        return match(true) {
            $this->location_efficiency_score >= 80 => 'excellent',
            $this->location_efficiency_score >= 60 => 'good',
            $this->location_efficiency_score >= 40 => 'fair',
            default => 'poor'
        };
    }
}
