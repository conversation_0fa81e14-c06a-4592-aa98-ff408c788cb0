<?php

namespace App\Services;

use App\Models\TaskLocation;
use App\Models\GeofenceZone;
use App\Models\LocationAnalytics;
use App\Events\LocationCaptured;
use App\Events\GeofenceEvent;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdvancedLocationService
{
    /**
     * Process advanced location data with geofencing and analytics
     */
    public function processAdvancedLocation(array $locationData, $userId, $taskId, $workspaceId)
    {
        try {
            // Enhance location data with additional information
            $enhancedData = $this->enhanceLocationData($locationData, $userId);
            
            // Calculate distance and duration from previous location
            $this->calculateLocationMetrics($enhancedData, $userId);
            
            // Check geofence zones
            $geofenceEvents = $this->checkGeofenceZones($enhancedData, $workspaceId);
            
            // Determine if this is a significant location
            $enhancedData['is_significant_location'] = $this->isSignificantLocation($enhancedData, $userId);
            
            // Reverse geocode address if not provided
            if (empty($enhancedData['address'])) {
                $enhancedData = array_merge($enhancedData, $this->reverseGeocode(
                    $enhancedData['latitude'],
                    $enhancedData['longitude']
                ));
            }
            
            // Create location record
            $location = TaskLocation::create(array_merge($enhancedData, [
                'user_id' => $userId,
                'task_id' => $taskId,
            ]));
            
            // Process geofence events
            foreach ($geofenceEvents as $event) {
                $this->processGeofenceEvent($location, $event);
            }
            
            // Broadcast real-time update
            broadcast(new LocationCaptured($location, $workspaceId));
            
            // Update daily analytics
            $this->updateDailyAnalytics($userId, $workspaceId, $location->created_at->toDateString());
            
            return $location;
            
        } catch (\Exception $e) {
            Log::error('Advanced location processing failed', [
                'error' => $e->getMessage(),
                'user_id' => $userId,
                'task_id' => $taskId,
                'location_data' => $locationData
            ]);
            
            throw $e;
        }
    }

    /**
     * Enhance location data with device and context information
     */
    private function enhanceLocationData(array $locationData, $userId)
    {
        $enhanced = $locationData;
        
        // Set default tracking type if not provided
        $enhanced['tracking_type'] = $enhanced['tracking_type'] ?? 'manual';
        
        // Set default location source
        $enhanced['location_source'] = $enhanced['location_source'] ?? 'gps';
        
        // Add device information
        $enhanced['device_info'] = [
            'user_agent' => request()->userAgent(),
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
        ];
        
        // Determine device type
        $enhanced['device_type'] = $this->detectDeviceType(request()->userAgent());
        
        // Set network type based on accuracy
        if (isset($enhanced['accuracy'])) {
            $enhanced['network_type'] = $enhanced['accuracy'] <= 10 ? 'gps' : 'network';
        }
        
        return $enhanced;
    }

    /**
     * Calculate distance and duration from previous location
     */
    private function calculateLocationMetrics(array &$locationData, $userId)
    {
        $previousLocation = TaskLocation::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();
        
        if ($previousLocation) {
            // Calculate distance
            $distance = $this->calculateDistance(
                $previousLocation->latitude,
                $previousLocation->longitude,
                $locationData['latitude'],
                $locationData['longitude']
            );
            
            $locationData['distance_from_previous'] = $distance;
            
            // Calculate duration
            $duration = now()->diffInSeconds($previousLocation->created_at);
            $locationData['duration_since_previous'] = $duration;
        }
    }

    /**
     * Check if location triggers any geofence zones
     */
    private function checkGeofenceZones(array $locationData, $workspaceId)
    {
        $events = [];
        
        $activeZones = GeofenceZone::where('workspace_id', $workspaceId)
            ->where('is_active', true)
            ->get();
        
        foreach ($activeZones as $zone) {
            if (!$zone->isCurrentlyActive()) {
                continue;
            }
            
            $isInZone = $zone->containsPoint(
                $locationData['latitude'],
                $locationData['longitude']
            );
            
            if ($isInZone) {
                $events[] = [
                    'zone' => $zone,
                    'event_type' => 'entry',
                    'location_data' => $locationData
                ];
                
                // Add geofence data to location
                $locationData['geofence_zones'] = [$zone->id];
                $locationData['geofence_event'] = 'entry';
            }
        }
        
        return $events;
    }

    /**
     * Determine if this is a significant location change
     */
    private function isSignificantLocation(array $locationData, $userId)
    {
        $recentLocations = TaskLocation::where('user_id', $userId)
            ->where('created_at', '>=', now()->subMinutes(30))
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        if ($recentLocations->isEmpty()) {
            return true;
        }
        
        // Check if location is significantly different from recent locations
        foreach ($recentLocations as $recent) {
            $distance = $this->calculateDistance(
                $recent->latitude,
                $recent->longitude,
                $locationData['latitude'],
                $locationData['longitude']
            );
            
            // If within 50 meters of a recent location, not significant
            if ($distance < 50) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Reverse geocode coordinates to address
     */
    private function reverseGeocode($latitude, $longitude)
    {
        try {
            // Using OpenStreetMap Nominatim (free service)
            $response = Http::timeout(5)->get('https://nominatim.openstreetmap.org/reverse', [
                'format' => 'json',
                'lat' => $latitude,
                'lon' => $longitude,
                'zoom' => 18,
                'addressdetails' => 1,
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'address' => $data['display_name'] ?? null,
                    'city' => $data['address']['city'] ?? $data['address']['town'] ?? $data['address']['village'] ?? null,
                    'country' => $data['address']['country'] ?? null,
                    'postal_code' => $data['address']['postcode'] ?? null,
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Reverse geocoding failed', [
                'error' => $e->getMessage(),
                'latitude' => $latitude,
                'longitude' => $longitude
            ]);
        }
        
        return [
            'address' => null,
            'city' => null,
            'country' => null,
            'postal_code' => null,
        ];
    }

    /**
     * Process geofence event
     */
    private function processGeofenceEvent($location, $event)
    {
        $zone = $event['zone'];
        
        // Update zone statistics
        if ($event['event_type'] === 'entry') {
            $zone->recordEntry();
        } else {
            $zone->recordExit();
        }
        
        // Broadcast geofence event
        broadcast(new GeofenceEvent($location, $zone, $event['event_type']));
        
        // Send notifications if configured
        $this->sendGeofenceNotifications($location, $zone, $event['event_type']);
    }

    /**
     * Send geofence notifications
     */
    private function sendGeofenceNotifications($location, $zone, $eventType)
    {
        // Implementation would depend on your notification system
        // This is a placeholder for notification logic
        Log::info('Geofence event notification', [
            'user_id' => $location->user_id,
            'zone_name' => $zone->name,
            'event_type' => $eventType,
            'location' => $location->coordinates
        ]);
    }

    /**
     * Update daily analytics
     */
    private function updateDailyAnalytics($userId, $workspaceId, $date)
    {
        LocationAnalytics::generateForUserAndDate($userId, $date, $workspaceId);
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371000; // Earth's radius in meters
        
        $latFrom = deg2rad($lat1);
        $lonFrom = deg2rad($lon1);
        $latTo = deg2rad($lat2);
        $lonTo = deg2rad($lon2);
        
        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;
        
        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * Detect device type from user agent
     */
    private function detectDeviceType($userAgent)
    {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        
        return 'desktop';
    }

    /**
     * Generate heat map data for a user
     */
    public function generateHeatMapData($userId, $startDate, $endDate)
    {
        $locations = TaskLocation::where('user_id', $userId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
        
        $gridSize = 0.001; // Approximately 100m grid
        $heatMap = [];
        
        foreach ($locations as $location) {
            $gridLat = round($location->latitude / $gridSize) * $gridSize;
            $gridLng = round($location->longitude / $gridSize) * $gridSize;
            $key = $gridLat . ',' . $gridLng;
            
            if (!isset($heatMap[$key])) {
                $heatMap[$key] = [
                    'lat' => $gridLat,
                    'lng' => $gridLng,
                    'intensity' => 0
                ];
            }
            
            $heatMap[$key]['intensity']++;
        }
        
        return array_values($heatMap);
    }

    /**
     * Get location analytics summary
     */
    public function getAnalyticsSummary($userId, $workspaceId, $startDate, $endDate)
    {
        $analytics = LocationAnalytics::where('user_id', $userId)
            ->where('workspace_id', $workspaceId)
            ->whereBetween('analytics_date', [$startDate, $endDate])
            ->get();
        
        return [
            'total_distance' => $analytics->sum('total_distance'),
            'total_duration' => $analytics->sum('total_duration'),
            'total_locations' => $analytics->sum('total_locations'),
            'avg_efficiency_score' => $analytics->avg('location_efficiency_score'),
            'total_geofence_events' => $analytics->sum('geofence_entries') + $analytics->sum('geofence_exits'),
            'unique_days' => $analytics->count(),
        ];
    }
}
