@extends('layout')
@section('title')
    {{ get_label('location_logs', 'Location Logs') }}
@endsection
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-style1">
                    <li class="breadcrumb-item">
                        <a href="{{ route('home.index') }}">{{ get_label('home', 'Home') }}</a>
                    </li>
                    <li class="breadcrumb-item active">{{ get_label('location_logs', 'Location Logs') }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bx bx-map me-2"></i>
                    {{ get_label('employee_location_logs', 'Employee Location Logs') }}
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" id="exportCsvBtn">
                        <i class="bx bx-download me-1"></i>
                        {{ get_label('export_csv', 'Export CSV') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="refreshBtn">
                        <i class="bx bx-refresh me-1"></i>
                        {{ get_label('refresh', 'Refresh') }}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card-body">
            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <label for="userFilter" class="form-label">{{ get_label('user', 'User') }}</label>
                    <select class="form-select" id="userFilter">
                        <option value="">{{ get_label('all_users', 'All Users') }}</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->first_name }} {{ $user->last_name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="actionFilter" class="form-label">{{ get_label('action', 'Action') }}</label>
                    <select class="form-select" id="actionFilter">
                        <option value="">{{ get_label('all_actions', 'All Actions') }}</option>
                        @foreach($actions as $action)
                            <option value="{{ $action }}">{{ ucfirst(str_replace('_', ' ', $action)) }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="startDate" class="form-label">{{ get_label('start_date', 'Start Date') }}</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">{{ get_label('end_date', 'End Date') }}</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="taskSearch" class="form-label">{{ get_label('search_task', 'Search Task') }}</label>
                    <input type="text" class="form-control" id="taskSearch" placeholder="{{ get_label('enter_task_name', 'Enter task name...') }}">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="button" class="btn btn-primary me-2" id="applyFiltersBtn">
                        <i class="bx bx-filter me-1"></i>
                        {{ get_label('apply_filters', 'Apply Filters') }}
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clearFiltersBtn">
                        <i class="bx bx-x me-1"></i>
                        {{ get_label('clear_filters', 'Clear Filters') }}
                    </button>
                </div>
            </div>

            <!-- Location Logs Table -->
            <div class="table-responsive">
                <table id="locationLogsTable" 
                       class="table table-striped table-hover"
                       data-toggle="table"
                       data-url="{{ route('location.admin.data') }}"
                       data-side-pagination="server"
                       data-pagination="true"
                       data-search="false"
                       data-show-refresh="false"
                       data-show-toggle="false"
                       data-show-columns="true"
                       data-sort-name="created_at"
                       data-sort-order="desc"
                       data-page-size="20"
                       data-page-list="[10, 20, 50, 100]"
                       data-response-handler="responseHandler"
                       data-query-params="queryParams">
                    <thead>
                        <tr>
                            <th data-field="user" data-sortable="false">{{ get_label('user', 'User') }}</th>
                            <th data-field="task" data-sortable="false">{{ get_label('task', 'Task') }}</th>
                            <th data-field="action" data-sortable="true">{{ get_label('action', 'Action') }}</th>
                            <th data-field="coordinates" data-sortable="false">{{ get_label('coordinates', 'Coordinates') }}</th>
                            <th data-field="date" data-sortable="true" data-sort-name="created_at">{{ get_label('date', 'Date') }}</th>
                            <th data-field="time" data-sortable="true" data-sort-name="created_at">{{ get_label('time', 'Time') }}</th>
                            <th data-field="actions" data-sortable="false">{{ get_label('actions', 'Actions') }}</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://js.pusher.com/7.2.0/pusher.min.js"></script>
<script src="{{ asset('assets/js/location-realtime.js') }}"></script>
<script>
$(document).ready(function() {
    let $table = $('#locationLogsTable');
    
    // Initialize table
    $table.bootstrapTable();
    
    // Apply filters
    $('#applyFiltersBtn').on('click', function() {
        $table.bootstrapTable('refresh');
    });
    
    // Clear filters
    $('#clearFiltersBtn').on('click', function() {
        $('#userFilter').val('');
        $('#actionFilter').val('');
        $('#startDate').val('');
        $('#endDate').val('');
        $('#taskSearch').val('');
        $table.bootstrapTable('refresh');
    });
    
    // Refresh button
    $('#refreshBtn').on('click', function() {
        $table.bootstrapTable('refresh');
    });
    
    // Export CSV
    $('#exportCsvBtn').on('click', function() {
        let params = queryParams({});
        let queryString = $.param(params);
        window.open(`{{ route('location.admin.data') }}?export=csv&${queryString}`, '_blank');
    });

    // Initialize real-time location tracking
    const locationRealtime = new LocationRealtime({{ session('workspace_id') }}, {
        key: '{{ config('chatify.pusher.key') }}',
        cluster: '{{ config('chatify.pusher.options.cluster') }}',
        encrypted: {{ config('chatify.pusher.options.encrypted') ? 'true' : 'false' }},
        authEndpoint: '/broadcasting/auth'
    });

    // Handle real-time location updates
    locationRealtime.onLocationCaptured(function(data) {
        // Show notification
        if (typeof toastr !== 'undefined') {
            toastr.info(`New location captured: ${data.user_name} - ${data.action_label}`, 'Real-time Update');
        }

        // Refresh table data
        $table.bootstrapTable('refresh');
    });

    // Handle connection status
    locationRealtime.onConnectionStateChange(function(states) {
        if (states.current === 'connected') {
            console.log('Real-time location tracking connected');
        } else if (states.current === 'disconnected') {
            console.log('Real-time location tracking disconnected');
        }
    });
    
    // Enter key support for task search
    $('#taskSearch').on('keypress', function(e) {
        if (e.which === 13) {
            $table.bootstrapTable('refresh');
        }
    });
});

// Query parameters for table
function queryParams(params) {
    return {
        limit: params.limit,
        offset: params.offset,
        sort: params.sort,
        order: params.order,
        user_id: $('#userFilter').val(),
        action: $('#actionFilter').val(),
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        task_search: $('#taskSearch').val()
    };
}

// Response handler for table
function responseHandler(res) {
    return {
        total: res.data.total,
        rows: res.data.rows.map(function(row) {
            return {
                user: `<div class="d-flex align-items-center">
                    <div class="avatar avatar-sm me-2">
                        <span class="avatar-initial rounded-circle bg-label-primary">${row.user.charAt(0)}</span>
                    </div>
                    <span>${row.user}</span>
                </div>`,
                task: `<span class="fw-medium">${row.task}</span>`,
                action: `<span class="badge bg-label-${getActionColor(row.action_raw)}">${row.action}</span>`,
                coordinates: `<small class="text-muted">${row.coordinates}</small>`,
                date: row.date,
                time: row.time,
                actions: `
                    <div class="dropdown">
                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bx bx-dots-vertical-rounded"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#" onclick="viewOnMap(${row.latitude}, ${row.longitude})">
                                <i class="bx bx-map me-1"></i> View on Map
                            </a>
                            <a class="dropdown-item" href="{{ route('tasks.info', '') }}/${row.task_id}">
                                <i class="bx bx-info-circle me-1"></i> View Task
                            </a>
                        </div>
                    </div>
                `
            };
        })
    };
}

// Get action color for badges
function getActionColor(action) {
    switch(action) {
        case 'task_created': return 'info';
        case 'checkin': return 'success';
        case 'checkout': return 'warning';
        default: return 'secondary';
    }
}

// View location on map
function viewOnMap(lat, lng) {
    const url = `https://www.google.com/maps?q=${lat},${lng}`;
    window.open(url, '_blank');
}
</script>
@endsection
