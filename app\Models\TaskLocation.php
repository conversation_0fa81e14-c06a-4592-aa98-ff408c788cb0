<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaskLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'task_id',
        'latitude',
        'longitude',
        'accuracy',
        'altitude',
        'speed',
        'heading',
        'address',
        'city',
        'country',
        'postal_code',
        'action',
        'tracking_type',
        'location_source',
        'battery_level',
        'device_type',
        'network_type',
        'device_info',
        'distance_from_previous',
        'duration_since_previous',
        'is_significant_location',
        'geofence_zones',
        'geofence_event',
        'is_encrypted',
        'expires_at'
    ];

    protected $casts = [
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'accuracy' => 'decimal:2',
        'altitude' => 'decimal:2',
        'speed' => 'decimal:2',
        'heading' => 'decimal:2',
        'distance_from_previous' => 'decimal:2',
        'device_info' => 'array',
        'geofence_zones' => 'array',
        'is_significant_location' => 'boolean',
        'is_encrypted' => 'boolean',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Valid action types for location tracking
     */
    const ACTIONS = [
        'task_created',
        'checkin',
        'checkout',
        'auto_track',
        'geofence_entry',
        'geofence_exit',
        'manual_update',
        'route_point',
    ];

    /**
     * Valid tracking types
     */
    const TRACKING_TYPES = [
        'manual',
        'automatic',
        'geofence',
        'scheduled',
        'emergency',
    ];

    /**
     * Valid location sources
     */
    const LOCATION_SOURCES = [
        'gps',
        'network',
        'passive',
        'fused',
        'manual',
    ];

    /**
     * Get the user that owns the location record
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the task associated with this location
     */
    public function task()
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Scope to filter by action type
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to filter by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by task
     */
    public function scopeByTask($query, $taskId)
    {
        return $query->where('task_id', $taskId);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get formatted coordinates as string
     */
    public function getCoordinatesAttribute()
    {
        return $this->latitude . ', ' . $this->longitude;
    }

    /**
     * Get human readable action name
     */
    public function getActionLabelAttribute()
    {
        return match($this->action) {
            'task_created' => 'Task Created',
            'checkin' => 'Check In',
            'checkout' => 'Check Out',
            'auto_track' => 'Auto Tracking',
            'geofence_entry' => 'Geofence Entry',
            'geofence_exit' => 'Geofence Exit',
            'manual_update' => 'Manual Update',
            'route_point' => 'Route Point',
            default => ucfirst(str_replace('_', ' ', $this->action)),
        };
    }

    /**
     * Scope to filter by tracking type
     */
    public function scopeByTrackingType($query, $type)
    {
        return $query->where('tracking_type', $type);
    }

    /**
     * Scope to filter by location source
     */
    public function scopeByLocationSource($query, $source)
    {
        return $query->where('location_source', $source);
    }

    /**
     * Scope to get significant locations only
     */
    public function scopeSignificantOnly($query)
    {
        return $query->where('is_significant_location', true);
    }

    /**
     * Scope to filter by geofence events
     */
    public function scopeByGeofenceEvent($query, $event)
    {
        return $query->where('geofence_event', $event);
    }

    /**
     * Scope to get locations within radius of a point
     */
    public function scopeWithinRadius($query, $latitude, $longitude, $radiusInMeters)
    {
        $radiusInDegrees = $radiusInMeters / 111320; // Approximate conversion

        return $query->whereRaw(
            "ST_Distance_Sphere(POINT(longitude, latitude), POINT(?, ?)) <= ?",
            [$longitude, $latitude, $radiusInMeters]
        );
    }

    /**
     * Calculate distance from another location
     */
    public function distanceFrom($latitude, $longitude)
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $latFrom = deg2rad($this->latitude);
        $lonFrom = deg2rad($this->longitude);
        $latTo = deg2rad($latitude);
        $lonTo = deg2rad($longitude);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get formatted address
     */
    public function getFormattedAddressAttribute()
    {
        if ($this->address) {
            return $this->address;
        }

        $parts = array_filter([
            $this->city,
            $this->country,
            $this->postal_code
        ]);

        return !empty($parts) ? implode(', ', $parts) : $this->coordinates;
    }

    /**
     * Get location accuracy status
     */
    public function getAccuracyStatusAttribute()
    {
        if (!$this->accuracy) return 'unknown';

        return match(true) {
            $this->accuracy <= 5 => 'excellent',
            $this->accuracy <= 10 => 'good',
            $this->accuracy <= 20 => 'fair',
            default => 'poor'
        };
    }

    /**
     * Get speed in km/h
     */
    public function getSpeedKmhAttribute()
    {
        return $this->speed ? round($this->speed * 3.6, 2) : null;
    }

    /**
     * Get compass direction from heading
     */
    public function getCompassDirectionAttribute()
    {
        if (!$this->heading) return null;

        $directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
        $index = round($this->heading / 22.5) % 16;

        return $directions[$index];
    }

    /**
     * Check if location is stale (older than threshold)
     */
    public function isStale($thresholdMinutes = 30)
    {
        return $this->created_at->diffInMinutes(now()) > $thresholdMinutes;
    }

    /**
     * Get battery status
     */
    public function getBatteryStatusAttribute()
    {
        if (!$this->battery_level) return 'unknown';

        return match(true) {
            $this->battery_level >= 80 => 'high',
            $this->battery_level >= 50 => 'medium',
            $this->battery_level >= 20 => 'low',
            default => 'critical'
        };
    }
}
