<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('task_id')->constrained()->onDelete('cascade');

            // Enhanced Location Data
            $table->decimal('latitude', 10, 7);
            $table->decimal('longitude', 10, 7);
            $table->decimal('accuracy', 8, 2)->nullable(); // GPS accuracy in meters
            $table->decimal('altitude', 8, 2)->nullable(); // Altitude in meters
            $table->decimal('speed', 8, 2)->nullable(); // Speed in m/s
            $table->decimal('heading', 5, 2)->nullable(); // Compass heading in degrees

            // Address & Location Context
            $table->string('address')->nullable(); // Reverse geocoded address
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();

            // Tracking Metadata
            $table->string('action'); // 'checkin', 'checkout', 'task_created', 'auto_track', 'geofence_entry', 'geofence_exit'
            $table->string('tracking_type')->default('manual'); // 'manual', 'automatic', 'geofence'
            $table->string('location_source')->default('gps'); // 'gps', 'network', 'passive'

            // Device & Context Information
            $table->integer('battery_level')->nullable(); // Battery percentage
            $table->string('device_type')->nullable(); // 'mobile', 'tablet', 'desktop'
            $table->string('network_type')->nullable(); // 'wifi', 'cellular', 'offline'
            $table->json('device_info')->nullable(); // Additional device metadata

            // Analytics & Performance
            $table->decimal('distance_from_previous', 10, 2)->nullable(); // Distance in meters
            $table->integer('duration_since_previous')->nullable(); // Duration in seconds
            $table->boolean('is_significant_location')->default(false); // For location clustering

            // Geofencing Support
            $table->json('geofence_zones')->nullable(); // Active geofence zones
            $table->string('geofence_event')->nullable(); // 'entry', 'exit', 'dwell'

            // Security & Compliance
            $table->boolean('is_encrypted')->default(false);
            $table->timestamp('expires_at')->nullable(); // Data retention

            $table->timestamps();

            // Geospatial Indexing for Performance
            $table->spatialIndex(['latitude', 'longitude'], 'location_spatial_index');
            $table->index(['user_id', 'created_at'], 'user_timeline_index');
            $table->index(['task_id', 'action'], 'task_action_index');
            $table->index(['tracking_type', 'created_at'], 'tracking_type_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task_locations');
    }
};
